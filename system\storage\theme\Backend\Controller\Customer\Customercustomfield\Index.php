<?php

namespace Theme25\Backend\Controller\Customer\Customercustomfield;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме custom-field.js, ако съществува
        $customFieldJsUrl = $base . 'backend_js/custom-field.js';
        $customFieldJsPath = DIR_THEME . 'Backend/View/Javascript/custom-field.js';
        
        if (file_exists($customFieldJsPath)) {
            $lastModified = filemtime($customFieldJsPath);
            $customFieldJsUrl .= '?v=' . $lastModified;
            $this->document->addScript($customFieldJsUrl, 'footer');
        }
    }

    /**
     * Изпълнява подготовката на данните за списъка с персонализирани полета
     */
    public function execute() {
        $this->setTitle('Персонализирани полета за клиенти');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('customer/custom_field');
    }

    /**
     * Подготвя данните за списъка с персонализирани полета
     */
    public function prepareData() {
        $this->prepareFilterData()
             ->prepareCustomFieldData()
             ->preparePagination()
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за филтриране
     */
    private function prepareFilterData() {
        // Сортиране и подредба
        $sort = $this->requestGet('sort', 'cf.sort_order');
        $order = $this->requestGet('order', 'ASC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        return $this;
    }

    /**
     * Подготвя данните за персонализираните полета
     */
    private function prepareCustomFieldData() {
        $this->load->model('customer/custom_field');

        $filter_data = [
            'filter_customer' => 1,
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        $custom_field_total = $this->model_customer_custom_field->getTotalCustomFields($filter_data);
        $results = $this->model_customer_custom_field->getCustomFields($filter_data);

        $custom_fields = [];
        foreach ($results as $result) {
            $custom_fields[] = [
                'custom_field_id' => $result['custom_field_id'],
                'name' => $result['name'],
                'location' => $result['location'] == 'account' ? 'Акаунт' : 'Адрес',
                'type' => $this->getCustomFieldTypeText($result['type']),
                'sort_order' => $result['sort_order'],
                'status' => $result['status'] ? 'Активно' : 'Неактивно',
                'edit' => $this->getAdminLink('customer/custom_field/edit', 'custom_field_id=' . $result['custom_field_id']),
                'delete' => $this->getAdminLink('customer/custom_field/delete', 'custom_field_id=' . $result['custom_field_id'])
            ];
        }

        $this->setData([
            'custom_fields' => $custom_fields,
            'custom_field_total' => $custom_field_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['custom_field_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('customer/custom_field', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('customer/custom_field', $filter_params . '&limit={limit}'));
        $pagination->setProductText('персонализирани полета');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)', 
                ($this->data['page'] - 1) * $limit + 1, 
                min($this->data['custom_field_total'], $this->data['page'] * $limit), 
                $this->data['custom_field_total'], 
                ceil($this->data['custom_field_total'] / $limit)
            )
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        $this->setData([
            'add_url' => $this->getAdminLink('customer/custom_field/add'),
            'delete_url' => $this->getAdminLink('customer/custom_field/delete'),
            'user_token' => $this->session->data['user_token']
        ]);

        return $this;
    }

    /**
     * Генерира параметрите за филтриране в URL
     */
    private function buildFilterParams() {
        $params = [];

        if (!empty($this->data['sort'])) {
            $params[] = 'sort=' . $this->data['sort'];
        }

        if (!empty($this->data['order'])) {
            $params[] = 'order=' . $this->data['order'];
        }

        return implode('&', $params);
    }

    /**
     * Връща текста за типа на персонализираното поле
     */
    private function getCustomFieldTypeText($type) {
        $types = [
            'select' => 'Избор',
            'radio' => 'Радио бутон',
            'checkbox' => 'Отметка',
            'text' => 'Текст',
            'textarea' => 'Текстова област',
            'file' => 'Файл',
            'date' => 'Дата',
            'time' => 'Време',
            'datetime' => 'Дата и време'
        ];

        return isset($types[$type]) ? $types[$type] : $type;
    }
}
