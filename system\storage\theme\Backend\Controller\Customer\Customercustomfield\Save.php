<?php

namespace Theme25\Backend\Controller\Customer\Customercustomfield;

class Save extends \Theme25\ControllerSubMethods {

    private $error = [];

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява запазването на персонализирано поле
     */
    public function execute() {
        $json = [];

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->load->model('customer/custom_field');

            $custom_field_id = (int)$this->requestPost('custom_field_id', 0);

            if ($custom_field_id) {
                // Редактиране на съществуващо персонализирано поле
                $this->model_customer_custom_field->editCustomField($custom_field_id, $this->request->post);
                $json['success'] = 'Персонализираното поле беше успешно обновено!';
            } else {
                // Добавяне на ново персонализирано поле
                $custom_field_id = $this->model_customer_custom_field->addCustomField($this->request->post);
                $json['success'] = 'Персонализираното поле беше успешно добавено!';
            }

            $json['redirect'] = $this->getAdminLink('customer/custom_field');
        } else {
            $json['error'] = $this->error;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира формата за персонализирано поле
     */
    protected function validateForm() {
        if (!$this->hasPermission('modify', 'customer/custom_field')) {
            $this->error['warning'] = 'Нямате права за модифициране на персонализирани полета!';
        }

        foreach ($this->requestPost('custom_field_description', []) as $language_id => $value) {
            if ((utf8_strlen($value['name']) < 1) || (utf8_strlen($value['name']) > 128)) {
                $this->error['name'][$language_id] = 'Името на полето трябва да бъде между 1 и 128 символа!';
            }
        }

        if (($this->requestPost('type') == 'select' || $this->requestPost('type') == 'radio' || $this->requestPost('type') == 'checkbox')) {
            if (!$this->requestPost('custom_field_value')) {
                $this->error['warning'] = 'За този тип поле са необходими стойности!';
            } else {
                foreach ($this->requestPost('custom_field_value', []) as $custom_field_value_id => $custom_field_value) {
                    foreach ($custom_field_value['custom_field_value_description'] as $language_id => $custom_field_value_description) {
                        if ((utf8_strlen($custom_field_value_description['name']) < 1) || (utf8_strlen($custom_field_value_description['name']) > 128)) {
                            $this->error['custom_field_value'][$custom_field_value_id][$language_id] = 'Името на стойността трябва да бъде между 1 и 128 символа!';
                        }
                    }
                }
            }
        }

        return !$this->error;
    }
}
