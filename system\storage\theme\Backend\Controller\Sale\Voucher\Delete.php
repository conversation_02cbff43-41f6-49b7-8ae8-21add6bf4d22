<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за изтриване на подаръчен ваучер
 */
class Delete extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява изтриването на ваучер
     */
    public function execute() {
        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'sale/voucher')) {
            $this->setSession('error', 'Нямате права за изтриване на ваучери');
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
            return;
        }

        $voucher_id = (int)$this->requestGet('voucher_id', 0);

        if (!$voucher_id) {
            $this->setSession('error', 'Невалиден номер на ваучер');
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
            return;
        }

        try {
            $this->loadModelsAs([
                'sale/voucher' => 'voucherModel',
                'sale/order' => 'orderModel'
            ]);

            // Проверка дали ваучерът съществува
            $voucher_info = $this->voucherModel->getVoucher($voucher_id);
            if (!$voucher_info) {
                $this->setSession('error', 'Ваучерът не е намерен');
                $this->redirectResponse($this->getAdminLink('sale/voucher'));
                return;
            }

            // Проверка дали ваучерът е използван в поръчки
            $order_voucher_info = $this->orderModel->getOrderVoucherByVoucherId($voucher_id);
            if ($order_voucher_info) {
                $this->setSession('error', 'Ваучерът не може да бъде изтрит, защото е използван в поръчка #' . $order_voucher_info['order_id']);
                $this->redirectResponse($this->getAdminLink('sale/voucher'));
                return;
            }

            // Изтриване на ваучера
            $this->voucherModel->deleteVoucher($voucher_id);

            $this->setSession('success', 'Ваучерът е изтрит успешно');

        } catch (Exception $e) {
            $this->setSession('error', 'Грешка при изтриване: ' . $e->getMessage());
        }

        $this->redirectResponse($this->getAdminLink('sale/voucher'));
    }
}
