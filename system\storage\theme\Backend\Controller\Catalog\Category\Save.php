<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Save extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Запазване на категория
     */
    public function execute() {
        $json = [];
        
        ob_start();

        // Проверка за валидност на заявката
        if (!$this->isValidRequest()) {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();

        // Валидация на задължителни полета
        if (!$this->validateCategoryDescription($post)) {
            $json['error'] = 'Липсва описание на категорията';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка за име на категория
        if (!$this->hasCategoryName($post['category_description'])) {
            $json['error'] = 'Моля въведете име на категорията поне за един език';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на моделите
        $this->loadRequiredModels();

        // Подготвяне на данните за категорията
        $data = $this->prepareCategoryData($post);

        // Валидация на родителската категория
        if (!$this->validateParentCategory($data['parent_id'], $json)) {
            $this->setJSONResponseOutput($json);
            return;
        }

        // Генериране на уникални SEO URL адреси
        $data['category_seo_url'] = $this->generateUniqueSeoUrls($post['category_seo_url'] ?? [], $post['category_id'] ?? 0);

        // Запазване на категорията
        $json = $this->saveCategory($post['category_id'] ?? 0, $data);

        $output = ob_get_clean();
        if ($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали заявката е валидна
     *
     * @return bool
     */
    private function isValidRequest() {
        return $this->request->server['REQUEST_METHOD'] === 'POST';
    }

    /**
     * Проверява дали описанието на категорията е валидно
     *
     * @param array $post Данни от POST заявката
     * @return bool
     */
    private function validateCategoryDescription($post) {
        return isset($post['category_description']) && is_array($post['category_description']);
    }

    /**
     * Проверява дали има име на категория за поне един език
     *
     * @param array $descriptions Описания на категорията
     * @return bool
     */
    private function hasCategoryName($descriptions) {
        foreach ($descriptions as $description) {
            if (!empty($description['name'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * Зарежда необходимите модели
     */
    private function loadRequiredModels() {
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'tool/image' => 'imageModel'
        ]);
    }

    /**
     * Подготвя данните за категорията
     *
     * @param array $post Данни от POST заявката
     * @return array Подготвени данни за категорията
     */
    private function prepareCategoryData($post) {
        $data = [
            'parent_id' => $post['parent_id'] ?? 0,
            'top' => $post['top'] ?? 0,
            'column' => $post['column'] ?? 1,
            'sort_order' => $post['sort_order'] ?? 0,
            'status' => $post['status'] ?? 0,
            'category_description' => $post['category_description'],
            'category_store' => $post['category_store'] ?? [0],
            'category_seo_url' => $post['category_seo_url'] ?? [],
            'image' => ''
        ];

        // Обработка на изображението
        if (!empty($post['image'])) {
            $image_path = $post['image'];
            $image_file = ThemeData()->getImageServerPath() . $image_path;
            if (file_exists($image_file)) {
                $data['image'] = $image_path;
            }
        }

        return $data;
    }

    /**
     * Валидира родителската категория
     *
     * @param int $parent_id ID на родителската категория
     * @param array &$json Референтен масив за грешки
     * @return bool Връща true ако родителската категория е валидна
     */
    private function validateParentCategory($parent_id, &$json) {
        if ($parent_id > 0) {
            $parent_category = $this->categoryModel->getCategory($parent_id);
            if (!$parent_category) {
                $json['error'] = 'Избраната родителска категория не съществува';
                return false;
            }
        }
        return true;
    }

    /**
     * Генерира уникални SEO URL адреси
     *
     * @param array $seo_urls SEO URL-и от POST заявката
     * @param int $category_id ID на категорията
     * @return array Уникални SEO URL-и
     */
    private function generateUniqueSeoUrls($seo_urls, $category_id) {
        $unique_seo_urls = $seo_urls;
        foreach ($unique_seo_urls as $language_id => $keyword) {
            if ($keyword) {
                $current_keyword = $keyword;
                $suffix_number = 1;

                while (!$this->categoryModel->isSeoUrlUnique($current_keyword, $language_id, 0, $category_id)) {
                    if (preg_match('/^(.*)-(\d+)$/', $current_keyword, $matches)) {
                        $base_keyword = $matches[1];
                        $existing_number = (int)$matches[2];
                        $current_keyword = $base_keyword . '-' . ($existing_number + 1);
                    } else {
                        $current_keyword = $keyword . '-' . $suffix_number;
                        $suffix_number++;
                    }
                }
                $unique_seo_urls[$language_id] = $current_keyword;
            }
        }
        return $unique_seo_urls;
    }

    /**
     * Запазва категорията в базата данни
     *
     * @param int $category_id ID на категорията (0 за нова)
     * @param array $data Данни за категорията
     * @return array JSON отговор
     */
    private function saveCategory($category_id, $data) {
        $json = [];

        F()->log->developer($data, __FILE__, __LINE__);
        
        try {
            if ($category_id) {
                // Проверка за самородителство
                if ($data['parent_id'] == $category_id) {
                    $json['error'] = 'Категорията не може да бъде родител на себе си';
                    return $json;
                }

                // Проверка за циклична зависимост
                if ($this->wouldCreateCycle($category_id, $data['parent_id'])) {
                    $json['error'] = 'Избраната родителска категория би създала циклична зависимост';
                    return $json;
                }

                $this->categoryModel->editCategory($category_id, $data);
                $json['success'] = 'Категорията беше успешно актуализирана';
            } else {
                $category_id = $this->categoryModel->addCategory($data);
                $json['success'] = 'Категорията беше успешно добавена';
            }

            $json['category_id'] = $category_id;
        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване на категорията: ' . $e->getMessage();
        }

        return $json;
    }

    /**
     * Проверява дали промяната на родителската категория би създала циклична зависимост
     *
     * @param int $category_id ID на категорията
     * @param int $parent_id ID на новата родителска категория
     * @return bool True ако би създала цикъл
     */
    private function wouldCreateCycle($category_id, $parent_id) {
        if ($parent_id == 0) {
            return false; // Няма родител, няма цикъл
        }

        // Получаване на всички деца на текущата категория
        $children = $this->getAllCategoryChildren($category_id);
        
        // Проверка дали новият родител е сред децата
        return in_array($parent_id, $children);
    }

    /**
     * Получава всички деца на дадена категория (рекурсивно)
     *
     * @param int $category_id ID на категорията
     * @return array Масив с ID-та на всички деца
     */
    private function getAllCategoryChildren($category_id) {
        $children = [];
        
        // Получаване на директните деца
        $direct_children = $this->categoryModel->getCategories(['filter_parent_id' => $category_id]);
        
        foreach ($direct_children as $child) {
            $children[] = $child['category_id'];
            
            // Рекурсивно получаване на децата на детето
            $grandchildren = $this->getAllCategoryChildren($child['category_id']);
            $children = array_merge($children, $grandchildren);
        }
        
        return $children;
    }
}