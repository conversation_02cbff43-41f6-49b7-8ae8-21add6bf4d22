<?php

namespace Theme25\Backend\Controller\Customer\Customergroup;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме customer-group-form.js, ако съществува
        $customerGroupFormUrl = $base . 'backend_js/customer-group-form.js';
        $customerGroupFormPath = DIR_THEME . 'Backend/View/Javascript/customer-group-form.js';
        
        if (file_exists($customerGroupFormPath)) {
            $lastModified = filemtime($customerGroupFormPath);
            $customerGroupFormUrl .= '?v=' . $lastModified;
            $this->document->addScript($customerGroupFormUrl, 'footer');
        }
    }

    /**
     * Изпълнява подготовката на данните за формата за клиентска група
     */
    public function execute() {
        $customer_group_id = (int)$this->requestGet('customer_group_id', 0);
        
        if ($customer_group_id) {
            $this->setTitle('Редактиране на клиентска група');
        } else {
            $this->setTitle('Добавяне на клиентска група');
        }
        
        $this->initAdminData();
        $this->prepareCustomerGroupForm($customer_group_id);
        $this->renderTemplateWithDataAndOutput('customer/customer_group_form');
    }

    /**
     * Подготвя формата за клиентска група
     */
    public function prepareCustomerGroupForm($customer_group_id = 0) {
        $this->prepareCustomerGroupData($customer_group_id)
             ->prepareLanguages()
             ->prepareFormUrls($customer_group_id)
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за клиентската група
     */
    private function prepareCustomerGroupData($customer_group_id) {
        $this->load->model('customer/customer_group');

        if ($customer_group_id && ($customer_group_info = $this->model_customer_customer_group->getCustomerGroup($customer_group_id))) {
            $this->setData([
                'customer_group_id' => $customer_group_info['customer_group_id'],
                'approval' => $customer_group_info['approval'],
                'sort_order' => $customer_group_info['sort_order']
            ]);

            // Зареждане на описанията на различните езици
            $customer_group_description = $this->model_customer_customer_group->getCustomerGroupDescriptions($customer_group_id);
            $this->setData('customer_group_description', $customer_group_description);
        } else {
            $this->setData([
                'customer_group_id' => 0,
                'approval' => 0,
                'sort_order' => 1,
                'customer_group_description' => []
            ]);
        }

        return $this;
    }

    /**
     * Подготвя езиците
     */
    private function prepareLanguages() {
        $this->load->model('localisation/language');
        $languages = $this->model_localisation_language->getLanguages();

        $this->setData('languages', $languages);

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls($customer_group_id) {
        $this->setData([
            'action' => $this->getAdminLink('customer/customer_group/save'),
            'cancel' => $this->getAdminLink('customer/customer_group')
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        $this->setData([
            'user_token' => $this->session->data['user_token'],
            'back_url' => $this->getAdminLink('customer/customer_group')
        ]);

        return $this;
    }
}
