<?php

namespace Theme25\Backend\Controller\Sale\QuickOrder;

/**
 * Sub-контролер за преглед на детайли за бърза поръчка
 */
class Info extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за преглед на бърза поръчка
     */
    public function prepareData() {
        $order_id = (int)$this->requestGet('order_id', 0);

        if (!$order_id) {
            $this->setError('Невалиден номер на поръчка');
            $this->redirectResponse($this->getAdminLink('sale/quick_order'));
            return;
        }

        $this->prepareOrderInfo($order_id)
             ->prepareProductInfo($order_id)
             ->prepareCustomerInfo($order_id)
             ->prepareStatusOptions()
             ->prepareBreadcrumbs($order_id);
    }

    /**
     * Подготвя основната информация за поръчката
     *
     * @param int $order_id
     * @return $this За верижно извикване на методи
     */
    private function prepareOrderInfo($order_id) {
        try {
            $this->loadModelAs('sale/quickorder', 'quickOrderModel');
            $order_info = $this->quickOrderModel->getQuickOrder($order_id);

            if (!$order_info) {
                $this->setError('Поръчката не е намерена');
                $this->redirectResponse($this->getAdminLink('sale/quick_order'));
                return $this;
            }

            // Подготвяне на статус информация
            $status_info = $this->getStatusInfo($order_info['order_status_id']);

            $this->setData($order_info);

            // Изчисляване на общата сума въз основа на ordered_price
            $calculated_total = $order_info['ordered_price'] * $order_info['quantity'];

            $this->setData([
                'order_id' => $order_id,
                'customer_name' => $order_info['names'], // За съвместимост с шаблона
                'date_added' => date('d.m.Y H:i:s', strtotime($order_info['date_added'])),
                'date_modified' => $order_info['date_modified'] != '0000-00-00 00:00:00' ? date('d.m.Y H:i:s', strtotime($order_info['date_modified'])) : '-',
                'status_name' => $status_info['name'],
                'status_class' => $status_info['class'],
                'formatted_total' => $this->formatCurrency($calculated_total, 'BGN', 1)
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при получаване на данни за поръчката: ' . $e->getMessage());
        }

        return $this;
    }

    /**
     * Подготвя информацията за продукта
     *
     * @param int $order_id
     * @return $this За верижно извикване на методи
     */
    private function prepareProductInfo($order_id) {
        $order_info = $this->getData();

        if (!$order_info || !isset($order_info['product_id'])) {
            return $this;
        }

        try {
            $this->loadModelAs('tool/image', 'imageModel');

            // Подготвяне на информация за продукта
            $product_image = $order_info['product_image'] ?: 'no_image.png';
            $ordered_price = $order_info['ordered_price'];
            $quantity = $order_info['quantity'];
            $calculated_total = $ordered_price * $quantity;

            $product_info = [
                'product_id' => $order_info['product_id'],
                'name' => $order_info['product_name'],
                'model' => $order_info['product_model'] ?? '',
                'image' => $this->imageModel->resize($product_image, 100, 100),
                'image_thumb' => $this->imageModel->resize($product_image, 50, 50),
                'quantity' => $quantity,
                'price' => $this->formatCurrency($ordered_price, 'BGN', 1),
                'total' => $this->formatCurrency($calculated_total, 'BGN', 1),
                'product_url' => $this->getAdminLink('catalog/product/edit', 'product_id=' . $order_info['product_id']),
                'product_view_url' => $this->getAdminLink('catalog/product/info', 'product_id=' . $order_info['product_id'])
            ];

            // Ако има опции на продукта
            if (!empty($order_info['product_option_value_id'])) {
                $product_info['option_name'] = $order_info['option_name'] ?? '';
                $product_info['option_value'] = $order_info['option_value'] ?? '';
                $product_info['has_options'] = true;
            } else {
                $product_info['has_options'] = false;
            }

            $this->setData('product_info', $product_info);

        } catch (Exception $e) {
            $this->setError('Грешка при получаване на данни за продукта: ' . $e->getMessage());
        }

        return $this;
    }

    /**
     * Подготвя информацията за клиента
     *
     * @param int $order_id
     * @return $this За верижно извикване на методи
     */
    private function prepareCustomerInfo($order_id) {
        $order_info = $this->getData('order_info');
        
        if (!$order_info) {
            return $this;
        }

        $customer_info = [
            'name' => $order_info['names'],
            'phone' => $order_info['phone'],
            'comment' => $order_info['comment'] ?? ''
        ];

        $this->setData('customer_info', $customer_info);

        return $this;
    }

    /**
     * Подготвя опциите за статус
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareStatusOptions() {
        $status_options = [
            ['status_id' => '0', 'name' => 'Нова поръчка'],
            ['status_id' => '1', 'name' => 'Прозванен'],
            ['status_id' => '2', 'name' => 'Потвърдена'],
            ['status_id' => '3', 'name' => 'Отказана'],
            ['status_id' => '4', 'name' => 'Завършена']
        ];

        $this->setData('status_options', $status_options);

        return $this;
    }

    /**
     * Подготвя breadcrumbs навигацията
     *
     * @param int $order_id
     * @return $this За верижно извикване на методи
     */
    private function prepareBreadcrumbs($order_id) {
        $breadcrumbs = [
            [
                'text' => 'Начало',
                'href' => $this->getAdminLink('common/dashboard')
            ],
            [
                'text' => 'Бързи поръчки',
                'href' => $this->getAdminLink('sale/quick_order')
            ],
            [
                'text' => 'Поръчка #' . $order_id,
                'href' => $this->getAdminLink('sale/quick_order/info', 'order_id=' . $order_id)
            ]
        ];

        $this->setData([
            'breadcrumbs' => $breadcrumbs,
            'cancel_url' => $this->getAdminLink('sale/quick_order'),
            'edit_url' => $this->getAdminLink('sale/quick_order/edit', 'order_id=' . $order_id),
            'delete_url' => $this->getAdminLink('sale/quick_order/delete', 'order_id=' . $order_id)
        ]);

        return $this;
    }

    /**
     * Получава информация за статус
     *
     * @param string $status_id
     * @return array
     */
    private function getStatusInfo($status_id) {
        $statuses = [
            '0' => ['name' => 'Нова поръчка', 'class' => 'bg-blue-100 text-blue-800'],
            '1' => ['name' => 'Прозванен', 'class' => 'bg-yellow-100 text-yellow-800'],
            '2' => ['name' => 'Потвърдена', 'class' => 'bg-green-100 text-green-800'],
            '3' => ['name' => 'Отказана', 'class' => 'bg-red-100 text-red-800'],
            '4' => ['name' => 'Завършена', 'class' => 'bg-gray-100 text-gray-800']
        ];

        return $statuses[$status_id] ?? ['name' => 'Неизвестен', 'class' => 'bg-gray-100 text-gray-800'];
    }
}
