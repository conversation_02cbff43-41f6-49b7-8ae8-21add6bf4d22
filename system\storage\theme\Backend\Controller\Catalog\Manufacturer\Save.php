<?php

namespace Theme25\Backend\Controller\Catalog\Manufacturer;

class Save extends \Theme25\ControllerSubMethods {

    private $error = [];

    public function execute() {
        $json = [];

        try {
            // Проверка на правата за достъп
            if (!$this->user->hasPermission('modify', 'catalog/manufacturer')) {
                $json['error'] = 'Нямате права за модифициране на производители!';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Валидация на данните
            if (!$this->validateForm()) {
                $json['error'] = 'Моля, поправете грешките във формата!';
                $json['errors'] = $this->error;
                $this->setJSONResponseOutput($json);
                return;
            }

            $this->loadModelsAs([
                'catalog/manufacturer' => 'manufacturerModel'
            ]);

            $manufacturer_id = (int)$this->requestGet('manufacturer_id');
            $data = $this->prepareDataForSave();

            if ($manufacturer_id) {
                // Редактиране на съществуващ производител
                $this->manufacturerModel->editManufacturer($manufacturer_id, $data);
                $json['success'] = 'Производителят е редактиран успешно!';
                $json['manufacturer_id'] = $manufacturer_id;
            } else {
                // Добавяне на нов производител
                $manufacturer_id = $this->manufacturerModel->addManufacturer($data);
                $json['success'] = 'Производителят е добавен успешно!';
                $json['manufacturer_id'] = $manufacturer_id;
            }

            $json['redirect'] = $this->getAdminLink('catalog/manufacturer');

        } catch (Exception $e) {
            $json['error'] = 'Възникна грешка при запазването: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира данните от формата
     */
    private function validateForm() {
        $this->error = [];

        // Валидация на името
        if ((utf8_strlen(trim($this->requestPost('name'))) < 1) || (utf8_strlen($this->requestPost('name')) > 64)) {
            $this->error['name'] = 'Името на производителя трябва да бъде между 1 и 64 символа!';
        }

        // Валидация на sort_order
        $sort_order = $this->requestPost('sort_order');
        if (!is_numeric($sort_order) || $sort_order < 0) {
            $this->error['sort_order'] = 'Подредбата трябва да бъде положително число!';
        }

        // Валидация на SEO URL
        $this->validateSeoUrls();

        // Валидация на изображението
        $this->validateImage();

        return empty($this->error);
    }

    /**
     * Валидира SEO URL адресите
     */
    private function validateSeoUrls() {
        $manufacturer_seo_url = $this->requestPost('manufacturer_seo_url', []);
        $manufacturer_id = (int)$this->requestGet('manufacturer_id');

        if ($manufacturer_seo_url) {
            $this->loadModelsAs([
                'design/seo_url' => 'seoUrlModel'
            ]);

            foreach ($manufacturer_seo_url as $store_id => $language) {
                foreach ($language as $language_id => $keyword) {
                    if (trim($keyword)) {
                        // Проверка за уникалност на SEO URL
                        if ($this->seoUrlModel->getTotalSeoUrlsByKeyword($keyword)) {
                            $existing_seo = $this->seoUrlModel->getSeoUrlByKeyword($keyword);
                            
                            // Ако това не е същия производител, има конфликт
                            if ($existing_seo['query'] != 'manufacturer_id=' . $manufacturer_id) {
                                if (!isset($this->error['keyword'])) {
                                    $this->error['keyword'] = [];
                                }
                                if (!isset($this->error['keyword'][$store_id])) {
                                    $this->error['keyword'][$store_id] = [];
                                }
                                $this->error['keyword'][$store_id][$language_id] = 'SEO URL вече се използва!';
                            }
                        }

                        // Валидация на формата на SEO URL
                        if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $keyword)) {
                            if (!isset($this->error['keyword'])) {
                                $this->error['keyword'] = [];
                            }
                            if (!isset($this->error['keyword'][$store_id])) {
                                $this->error['keyword'][$store_id] = [];
                            }
                            $this->error['keyword'][$store_id][$language_id] = 'SEO URL може да съдържа само букви, цифри, тире и долни черти!';
                        }
                    }
                }
            }
        }
    }

    /**
     * Валидира изображението
     */
    private function validateImage() {
        $image = $this->requestPost('image');
        
        if ($image) {
            // Проверка дали файлът съществува
            if (!is_file(DIR_IMAGE . $image)) {
                $this->error['image'] = 'Избраното изображение не съществува!';
            }

            // Проверка на разширението
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $file_extension = strtolower(pathinfo($image, PATHINFO_EXTENSION));
            
            if (!in_array($file_extension, $allowed_extensions)) {
                $this->error['image'] = 'Неподдържан формат на изображението!';
            }

            // Проверка на размера на файла (максимум 2MB)
            if (is_file(DIR_IMAGE . $image)) {
                $file_size = filesize(DIR_IMAGE . $image);
                if ($file_size > 2 * 1024 * 1024) {
                    $this->error['image'] = 'Изображението е твърде голямо (максимум 2MB)!';
                }
            }
        }
    }

    /**
     * Подготвя данните за запазване
     */
    private function prepareDataForSave() {
        $data = [];

        // Основни данни
        $data['name'] = trim($this->requestPost('name'));
        $data['image'] = $this->requestPost('image', '');
        $data['sort_order'] = (int)$this->requestPost('sort_order', 0);

        // Магазини
        $data['manufacturer_store'] = $this->requestPost('manufacturer_store', []);
        if (!is_array($data['manufacturer_store'])) {
            $data['manufacturer_store'] = [];
        }

        // SEO URL
        $data['manufacturer_seo_url'] = $this->requestPost('manufacturer_seo_url', []);

        return $data;
    }

    /**
     * Логира действието за одит
     */
    private function logAction($action, $manufacturer_id, $data) {
        if (method_exists($this, 'log')) {
            $log_data = [
                'action' => $action,
                'manufacturer_id' => $manufacturer_id,
                'manufacturer_name' => $data['name'],
                'user_id' => $this->user->getId(),
                'ip' => $this->request->server['REMOTE_ADDR'] ?? '',
                'user_agent' => $this->request->server['HTTP_USER_AGENT'] ?? ''
            ];

            $this->log->write('MANUFACTURER_' . strtoupper($action) . ': ' . json_encode($log_data));
        }
    }

    /**
     * Изчиства кеша свързан с производители
     */
    private function clearCache() {
        if (method_exists($this, 'cache')) {
            $this->cache->delete('manufacturer');
            $this->cache->delete('product');
            $this->cache->delete('seo_url');
        }
    }

    /**
     * Валидира данните за мултиезичност (за бъдещи разширения)
     */
    private function validateMultiLanguageData() {
        $manufacturer_description = $this->requestPost('manufacturer_description', []);
        
        foreach ($manufacturer_description as $language_id => $description) {
            if (isset($description['meta_title']) && utf8_strlen($description['meta_title']) > 255) {
                if (!isset($this->error['meta_title'])) {
                    $this->error['meta_title'] = [];
                }
                $this->error['meta_title'][$language_id] = 'Meta заглавието не може да бъде повече от 255 символа!';
            }

            if (isset($description['meta_description']) && utf8_strlen($description['meta_description']) > 255) {
                if (!isset($this->error['meta_description'])) {
                    $this->error['meta_description'] = [];
                }
                $this->error['meta_description'][$language_id] = 'Meta описанието не може да бъде повече от 255 символа!';
            }
        }
    }
}
