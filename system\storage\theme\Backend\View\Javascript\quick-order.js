/**
 * JavaScript модул за бързи поръчки
 * Следва BackendModule pattern на темата Rakla.bg
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initQuickOrder();
        }
    });

    // Добавяне на функционалност към основния модул
    if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
        Object.assign(BackendModule, {
            // Конфигурация за бързи поръчки
            quickOrder: {
                maxConcurrentRequests: 10,
                requestQueue: [],
                activeRequests: 0,
                selectors: {
                    filterPanel: '#filters-panel',
                    filterToggle: '[data-toggle="filters"]',
                    statusSelect: 'select[name="status_id"]',
                    quickStatusForm: '#quick-status-form',
                    deleteLinks: 'a[data-action="delete"]',
                    statusUpdateButtons: '[data-action="update-status"]'
                }
            },

            /**
             * Инициализация на модула за бързи поръчки
             */
            initQuickOrder: function() {
                this.bindQuickOrderEvents();
                this.initQuickOrderComponents();
                this.logDev && this.logDev('QuickOrder модул инициализиран');
            },

            /**
             * Свързване на събития за бързи поръчки
             */
            bindQuickOrderEvents: function() {
                // Филтри
                this.bindQuickOrderFilterEvents();

                // Статус актуализации
                this.bindQuickOrderStatusUpdateEvents();

                // Изтриване
                this.bindQuickOrderDeleteEvents();

                // Форми
                this.bindQuickOrderFormEvents();
            },

            /**
             * Свързване на събития за филтри
             */
            bindQuickOrderFilterEvents: function() {
                const self = this;

                // Форма за филтриране
                const filterForm = document.getElementById('filter-form');
                if (filterForm) {
                    // Предотвратяване на стандартното submit поведение
                    filterForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.applyQuickOrderFilters();
                    });

                    // Auto-submit на филтри при промяна
                    const filterInputs = filterForm.querySelectorAll('input[name^="filter_"], select[name^="filter_"]');
                    filterInputs.forEach(input => {
                        if (input.type === 'text' || input.type === 'date') {
                            // Debounce за текстови полета и дати
                            let timeout;
                            input.addEventListener('input', function() {
                                clearTimeout(timeout);
                                timeout = setTimeout(() => {
                                    self.applyQuickOrderFilters();
                                }, 800);
                            });
                        } else if (input.type === 'select-one') {
                            // Незабавно за select полета
                            input.addEventListener('change', function() {
                                self.applyQuickOrderFilters();
                            });
                        }
                    });
                }
            },

            /**
             * Свързване на събития за актуализиране на статус
             */
            bindQuickOrderStatusUpdateEvents: function() {
                const self = this;

                // Бърза актуализация на статус от списъка
                document.addEventListener('change', function(e) {
                    if (e.target.matches('select[data-order-id]')) {
                        const orderId = e.target.getAttribute('data-order-id');
                        const newStatus = e.target.value;
                        self.updateQuickOrderStatus(orderId, newStatus);
                    }
                });

                // Форма за бърза актуализация
                const quickStatusForm = document.querySelector(self.quickOrder.selectors.quickStatusForm);
                if (quickStatusForm) {
                    quickStatusForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        self.handleQuickOrderStatusUpdate(this);
                    });
                }
            },

            /**
             * Свързване на събития за изтриване
             */
            bindQuickOrderDeleteEvents: function() {
                const self = this;

                document.addEventListener('click', function(e) {
                    if (e.target.matches('button[data-action="delete"]') ||
                        e.target.closest('button[data-action="delete"]')) {
                        e.preventDefault();
                        const button = e.target.matches('button') ? e.target : e.target.closest('button');
                        self.confirmQuickOrderDelete(button);
                    }
                });
            },

            /**
             * Свързване на събития за форми
             */
            bindQuickOrderFormEvents: function() {
                const self = this;

                // Валидация на форми
                const forms = document.querySelectorAll('form[id*="quick-order"]');
                forms.forEach(form => {
                    form.addEventListener('submit', function(e) {
                        if (!self.validateQuickOrderForm(this)) {
                            e.preventDefault();
                        }
                    });
                });
            },

            /**
             * Инициализация на компоненти за бързи поръчки
             */
            initQuickOrderComponents: function() {
                this.initQuickOrderTooltips();
                this.initQuickOrderModals();
                this.initQuickOrderNotifications();
            },

            /**
             * Toggle на филтри панел
             */
            toggleQuickOrderFilters: function() {
                const panel = document.querySelector(this.quickOrder.selectors.filterPanel);
                if (panel) {
                    panel.classList.toggle('hidden');
                    this.logDev && this.logDev('Филтри панел превключен');
                }
            },

            /**
             * Прилагане на филтри чрез AJAX
             */
            applyQuickOrderFilters: function() {
                const form = document.getElementById('filter-form');
                if (!form) return;

                // Събиране на данните от формата
                const formData = new FormData(form);
                const params = new URLSearchParams();

                // Добавяне само на непразни стойности
                for (let [key, value] of formData.entries()) {
                    if (value.trim() !== '') {
                        params.append(key, value);
                    }
                }

                // Рестартиране на страницата към 1 при филтриране
                params.set('page', '1');

                // Изпращане на AJAX заявка
                const url = form.action + '?' + params.toString();

                this.logDev && this.logDev('Прилагане на филтри: ' + url);

                // Показване на loading индикатор
                this.showQuickOrderLoading();

                // Пренасочване към новия URL (за сега, по-късно може да се направи AJAX)
                window.location.href = url;
            },

            /**
             * Актуализиране на статус на поръчка
             */
            updateQuickOrderStatus: function(orderId, statusId, comment = '') {
                const self = this;

                if (this.quickOrder.activeRequests >= this.quickOrder.maxConcurrentRequests) {
                    this.quickOrder.requestQueue.push(() => {
                        self.updateQuickOrderStatus(orderId, statusId, comment);
                    });
                    return;
                }

                this.quickOrder.activeRequests++;

                const formData = new FormData();
                formData.append('order_id', orderId);
                formData.append('status_id', statusId);
                formData.append('comment', comment);

                fetch(this.getQuickOrderUpdateStatusUrl(), {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (typeof this.showAlert === 'function') {
                            this.showAlert('success', data.success);
                        }
                        this.updateQuickOrderStatusDisplay(orderId, statusId, data.status_name);
                    } else {
                        if (typeof this.showAlert === 'function') {
                            this.showAlert('error', data.error || 'Възникна грешка');
                        }
                    }
                })
                .catch(error => {
                    this.logDev && this.logDev('Грешка при актуализиране на статус: ' + error.message, 'error');
                    if (typeof this.showAlert === 'function') {
                        this.showAlert('error', 'Възникна грешка при обработката');
                    }
                })
                .finally(() => {
                    this.quickOrder.activeRequests--;
                    this.processQuickOrderRequestQueue();
                });
            },

            /**
             * Обработка на бърза актуализация на статус
             */
            handleQuickOrderStatusUpdate: function(form) {
                const formData = new FormData(form);
                const orderId = formData.get('order_id');
                const statusId = formData.get('status_id');
                const comment = formData.get('comment');

                this.updateQuickOrderStatus(orderId, statusId, comment);
            },

            /**
             * Потвърждение за изтриване
             */
            confirmQuickOrderDelete: function(button) {
                const orderId = button.getAttribute('data-order-id');
                const orderName = button.getAttribute('data-order-name');

                if (confirm(`Сигурни ли сте, че искате да изтриете поръчка #${orderId} от ${orderName}?`)) {
                    this.deleteQuickOrder(orderId, button);
                }
            },

            /**
             * AJAX изтриване на поръчка
             */
            deleteQuickOrder: function(orderId, button) {
                const self = this;

                // Показване на loading състояние
                const originalIcon = button.innerHTML;
                button.innerHTML = '<i class="ri-loader-4-line animate-spin"></i>';
                button.disabled = true;

                // Изпращане на AJAX заявка
                fetch(window.location.origin + '/admin/index.php?route=sale/quick_order/delete&user_token=' + this.getUserToken() + '&order_id=' + orderId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // Премахване на реда от таблицата
                        const row = button.closest('tr');
                        if (row) {
                            row.style.transition = 'opacity 0.3s ease';
                            row.style.opacity = '0';
                            setTimeout(() => {
                                row.remove();
                                self.showQuickOrderNotification('Поръчката е изтрита успешно', 'success');
                            }, 300);
                        }
                    } else {
                        throw new Error('Грешка при изтриване');
                    }
                })
                .catch(error => {
                    // Възстановяване на бутона
                    button.innerHTML = originalIcon;
                    button.disabled = false;

                    self.showQuickOrderNotification('Грешка при изтриване на поръчката', 'error');
                    self.logDev && self.logDev('Грешка при изтриване:', error);
                });
            },

            /**
             * Валидация на форма
             */
            validateQuickOrderForm: function(form) {
                let isValid = true;
                const errors = [];

                // Валидация на задължителни полета
                const requiredFields = form.querySelectorAll('[required]');
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        errors.push(`Полето "${this.getQuickOrderFieldLabel(field)}" е задължително`);
                        this.highlightQuickOrderField(field, true);
                    } else {
                        this.highlightQuickOrderField(field, false);
                    }
                });

                // Валидация на коментар
                const commentField = form.querySelector('textarea[name="comment"]');
                if (commentField && commentField.value.length > 1000) {
                    isValid = false;
                    errors.push('Коментарът не може да бъде по-дълъг от 1000 символа');
                    this.highlightQuickOrderField(commentField, true);
                }

                if (!isValid && typeof this.showAlert === 'function') {
                    this.showAlert('error', errors.join('<br>'));
                }

                return isValid;
            },

            /**
             * Актуализиране на показването на статус
             */
            updateQuickOrderStatusDisplay: function(orderId, statusId, statusName) {
                // Актуализиране на статус badge в таблицата
                const statusBadge = document.querySelector(`tr[data-order-id="${orderId}"] .status-badge`);
                if (statusBadge) {
                    statusBadge.textContent = statusName;
                    statusBadge.className = `status-badge ${this.getQuickOrderStatusClass(statusId)}`;
                }

                // Актуализиране на select елемент
                const statusSelect = document.querySelector(`select[data-order-id="${orderId}"]`);
                if (statusSelect) {
                    statusSelect.value = statusId;
                }
            },

            /**
             * Получаване на CSS клас за статус
             */
            getQuickOrderStatusClass: function(statusId) {
                const classes = {
                    '0': 'bg-blue-100 text-blue-800',
                    '1': 'bg-yellow-100 text-yellow-800',
                    '2': 'bg-green-100 text-green-800',
                    '3': 'bg-red-100 text-red-800',
                    '4': 'bg-gray-100 text-gray-800'
                };
                return classes[statusId] || 'bg-gray-100 text-gray-800';
            },

            /**
             * Инициализация на tooltips
             */
            initQuickOrderTooltips: function() {
                // Добавяне на tooltips за икони
                const actionIcons = document.querySelectorAll('[title]');
                actionIcons.forEach(icon => {
                    icon.addEventListener('mouseenter', function() {
                        // Tooltip логика
                    });
                });
            },

            /**
             * Инициализация на модали
             */
            initQuickOrderModals: function() {
                // Модал логика ако е необходима
            },

            /**
             * Инициализация на нотификации
             */
            initQuickOrderNotifications: function() {
                // Показване на съществуващи нотификации
                const successMessage = document.querySelector('.alert-success, .bg-green-50');
                const errorMessage = document.querySelector('.alert-danger, .bg-red-50');

                if (successMessage) {
                    setTimeout(() => {
                        successMessage.style.opacity = '0';
                        setTimeout(() => successMessage.remove(), 300);
                    }, 5000);
                }

                if (errorMessage) {
                    setTimeout(() => {
                        errorMessage.style.opacity = '0';
                        setTimeout(() => errorMessage.remove(), 300);
                    }, 7000);
                }
            },

            /**
             * Обработка на опашката със заявки
             */
            processQuickOrderRequestQueue: function() {
                if (this.quickOrder.requestQueue.length > 0 &&
                    this.quickOrder.activeRequests < this.quickOrder.maxConcurrentRequests) {
                    const nextRequest = this.quickOrder.requestQueue.shift();
                    nextRequest();
                }
            },

            /**
             * Помощни методи за бързи поръчки
             */
            getQuickOrderUpdateStatusUrl: function() {
                return window.location.origin + '/admin/index.php?route=sale/quick_order/status_update';
            },

            extractQuickOrderIdFromUrl: function(url) {
                const match = url.match(/order_id=(\d+)/);
                return match ? match[1] : null;
            },

            getQuickOrderFieldLabel: function(field) {
                const label = document.querySelector(`label[for="${field.id}"]`);
                return label ? label.textContent.replace('*', '').trim() : field.name;
            },

            highlightQuickOrderField: function(field, hasError) {
                if (hasError) {
                    field.classList.add('border-red-500', 'focus:ring-red-500');
                    field.classList.remove('border-gray-300', 'focus:ring-primary');
                } else {
                    field.classList.remove('border-red-500', 'focus:ring-red-500');
                    field.classList.add('border-gray-300', 'focus:ring-primary');
                }
            },

            showQuickOrderLoadingIndicator: function(element) {
                element.style.opacity = '0.5';
                element.style.pointerEvents = 'none';
            },

            /**
             * Показване на loading индикатор
             */
            showQuickOrderLoading: function() {
                // Създаване на loading overlay
                let loadingOverlay = document.getElementById('quick-order-loading');
                if (!loadingOverlay) {
                    loadingOverlay = document.createElement('div');
                    loadingOverlay.id = 'quick-order-loading';
                    loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    loadingOverlay.innerHTML = `
                        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                            <i class="ri-loader-4-line ri-spin text-2xl text-primary"></i>
                            <span class="text-gray-700">Зареждане...</span>
                        </div>
                    `;
                    document.body.appendChild(loadingOverlay);
                }
                loadingOverlay.classList.remove('hidden');
            },

            /**
             * Скриване на loading индикатор
             */
            hideQuickOrderLoading: function() {
                const loadingOverlay = document.getElementById('quick-order-loading');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                }
            },

            /**
             * Показване на notification съобщение
             */
            showQuickOrderNotification: function(message, type = 'info') {
                // Създаване на notification елемент
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                // Задаване на цветове според типа
                switch (type) {
                    case 'success':
                        notification.className += ' bg-green-500 text-white';
                        break;
                    case 'error':
                        notification.className += ' bg-red-500 text-white';
                        break;
                    case 'warning':
                        notification.className += ' bg-yellow-500 text-white';
                        break;
                    default:
                        notification.className += ' bg-blue-500 text-white';
                }

                notification.innerHTML = `
                    <div class="flex items-center">
                        <span>${message}</span>
                        <button type="button" class="ml-3 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                            <i class="ri-close-line"></i>
                        </button>
                    </div>
                `;

                // Добавяне към страницата
                document.body.appendChild(notification);

                // Анимация за показване
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Автоматично премахване след 5 секунди
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }, 5000);
            },

            /**
             * Получаване на user token
             */
            getUserToken: function() {
                // Опит за получаване от URL
                const urlParams = new URLSearchParams(window.location.search);
                const token = urlParams.get('user_token');
                if (token) {
                    return token;
                }

                // Опит за получаване от meta tag
                const metaToken = document.querySelector('meta[name="user-token"]');
                if (metaToken) {
                    return metaToken.getAttribute('content');
                }

                // Опит за получаване от глобална променлива
                if (typeof user_token !== 'undefined') {
                    return user_token;
                }

                // Fallback - опит за извличане от първия линк в страницата
                const firstLink = document.querySelector('a[href*="user_token"]');
                if (firstLink) {
                    const match = firstLink.href.match(/user_token=([^&]+)/);
                    if (match) {
                        return match[1];
                    }
                }

                return '';
            }
        });
    }

})();
