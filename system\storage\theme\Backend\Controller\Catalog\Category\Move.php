<?php
namespace Theme25\Backend\Controller\Catalog\Category;

use Theme25\Backend\Controller\ControllerSubMethods;

/**
 * Category Move Sub-Controller
 * Управление на преместването на категории
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Move extends ControllerSubMethods {
    
    /**
     * Изпълнение на преместването на категория
     */
    public function execute() {
        $this->load->language('catalog/category');
        $this->load->model('catalog/category');

        $json = [];

        try {
            // Проверка за POST заявка
            if ($this->request->server['REQUEST_METHOD'] !== 'POST') {
                throw new \Exception('Невалиден метод на заявката');
            }

            // Получаване на данните от заявката
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                throw new \Exception('Невалидни данни в заявката');
            }

            // Валидация на входните данни
            $moveData = $this->validateMoveData($input);
            
            // Проверка за права на достъп
            if (!$this->user->hasPermission('modify', 'catalog/category')) {
                throw new \Exception('Нямате права за редактиране на категории');
            }

            // Проверка дали категорията съществува
            $category = $this->model_catalog_category->getCategory($moveData['category_id']);
            if (!$category) {
                throw new \Exception('Категорията не съществува');
            }

            // Проверка дали целевата категория съществува (ако не е 0)
            if ($moveData['target_id'] > 0) {
                $targetCategory = $this->model_catalog_category->getCategory($moveData['target_id']);
                if (!$targetCategory) {
                    throw new \Exception('Целевата категория не съществува');
                }
            }

            // Проверка за цикличност
            if ($this->wouldCreateCycle($moveData['category_id'], $moveData['new_parent_id'])) {
                throw new \Exception('Преместването би създало цикличност в йерархията');
            }

            // Изпълнение на преместването
            $this->performMove($moveData);

            $json['success'] = true;
            $json['message'] = 'Категорията беше преместена успешно';
            $json['reload'] = true; // За сега презареждаме страницата

        } catch (\Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
            $this->logDev('Category move error: ' . $e->getMessage());
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Валидация на данните за преместване
     */
    private function validateMoveData($input) {
        $required = ['category_id', 'target_id', 'position'];
        
        foreach ($required as $field) {
            if (!isset($input[$field])) {
                throw new \Exception("Липсва задължително поле: {$field}");
            }
        }

        $categoryId = (int)$input['category_id'];
        $targetId = (int)$input['target_id'];
        $position = $input['position'];

        if ($categoryId <= 0) {
            throw new \Exception('Невалиден ID на категория');
        }

        if ($targetId < 0) {
            throw new \Exception('Невалиден ID на целева категория');
        }

        if (!in_array($position, ['before', 'after', 'inside'])) {
            throw new \Exception('Невалидна позиция за преместване');
        }

        // Не може да премести категория върху себе си
        if ($categoryId === $targetId) {
            throw new \Exception('Не може да преместите категория върху себе си');
        }

        // Изчисляване на новия parent_id
        $newParentId = $this->calculateNewParentId($targetId, $position);

        return [
            'category_id' => $categoryId,
            'target_id' => $targetId,
            'position' => $position,
            'old_parent_id' => $input['old_parent_id'] ?? 0,
            'new_parent_id' => $newParentId
        ];
    }

    /**
     * Изчисляване на новия parent_id според позицията
     */
    private function calculateNewParentId($targetId, $position) {
        if ($position === 'inside') {
            return $targetId;
        } else {
            // За 'before' и 'after' - същия родител като целевата категория
            if ($targetId === 0) {
                return 0;
            }
            
            $targetCategory = $this->model_catalog_category->getCategory($targetId);
            return $targetCategory ? $targetCategory['parent_id'] : 0;
        }
    }

    /**
     * Проверка за цикличност в йерархията
     */
    private function wouldCreateCycle($categoryId, $newParentId) {
        if ($newParentId === 0) {
            return false; // Преместване в root не може да създаде цикъл
        }

        // Проверка дали новия родител е наследник на преместваната категория
        $descendants = $this->getAllCategoryChildren($categoryId);
        return in_array($newParentId, $descendants);
    }

    /**
     * Получаване на всички наследници на категория
     */
    private function getAllCategoryChildren($categoryId) {
        $children = [];
        $directChildren = $this->model_catalog_category->getCategoriesByParentId($categoryId);
        
        foreach ($directChildren as $child) {
            $children[] = $child['category_id'];
            $children = array_merge($children, $this->getAllCategoryChildren($child['category_id']));
        }
        
        return $children;
    }

    /**
     * Изпълнение на преместването
     */
    private function performMove($moveData) {
        $categoryId = $moveData['category_id'];
        $targetId = $moveData['target_id'];
        $position = $moveData['position'];
        $newParentId = $moveData['new_parent_id'];

        // Получаване на текущия sort_order на категорията
        $category = $this->model_catalog_category->getCategory($categoryId);
        $currentSortOrder = $category['sort_order'];

        // Изчисляване на новия sort_order
        $newSortOrder = $this->calculateNewSortOrder($targetId, $position, $newParentId);

        // Обновяване на parent_id и sort_order
        $this->db->query("
            UPDATE `{{prefix}}category` 
            SET 
                `parent_id` = '" . (int)$newParentId . "',
                `sort_order` = '" . (int)$newSortOrder . "'
            WHERE `category_id` = '" . (int)$categoryId . "'
        ");

        // Преподреждане на останалите категории
        $this->reorderCategories($newParentId, $categoryId, $newSortOrder);

        $this->logDev("Category {$categoryId} moved to parent {$newParentId} with sort_order {$newSortOrder}");
    }

    /**
     * Изчисляване на новия sort_order
     */
    private function calculateNewSortOrder($targetId, $position, $newParentId) {
        if ($targetId === 0) {
            // Преместване в root
            $query = $this->db->query("
                SELECT MAX(`sort_order`) as max_sort 
                FROM `{{prefix}}category` 
                WHERE `parent_id` = 0
            ");
            return ($query->row['max_sort'] ?? 0) + 1;
        }

        $targetCategory = $this->model_catalog_category->getCategory($targetId);
        $targetSortOrder = $targetCategory['sort_order'];

        switch ($position) {
            case 'before':
                return max(1, $targetSortOrder);
                
            case 'after':
                return $targetSortOrder + 1;
                
            case 'inside':
                // Най-високия sort_order в новия родител + 1
                $query = $this->db->query("
                    SELECT MAX(`sort_order`) as max_sort 
                    FROM `{{prefix}}category` 
                    WHERE `parent_id` = '" . (int)$newParentId . "'
                ");
                return ($query->row['max_sort'] ?? 0) + 1;
                
            default:
                return 1;
        }
    }

    /**
     * Преподреждане на останалите категории
     */
    private function reorderCategories($parentId, $movedCategoryId, $newSortOrder) {
        // Получаване на всички категории в същия родител
        $query = $this->db->query("
            SELECT `category_id`, `sort_order` 
            FROM `{{prefix}}category` 
            WHERE `parent_id` = '" . (int)$parentId . "' 
            AND `category_id` != '" . (int)$movedCategoryId . "'
            ORDER BY `sort_order` ASC
        ");

        $categories = $query->rows;
        $sortOrder = 1;

        foreach ($categories as $category) {
            if ($sortOrder === $newSortOrder) {
                $sortOrder++; // Пропускаме позицията на преместената категория
            }

            $this->db->query("
                UPDATE `{{prefix}}category` 
                SET `sort_order` = '" . (int)$sortOrder . "' 
                WHERE `category_id` = '" . (int)$category['category_id'] . "'
            ");

            $sortOrder++;
        }
    }
}
