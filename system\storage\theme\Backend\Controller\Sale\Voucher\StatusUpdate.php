<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за AJAX актуализиране на статус на ваучер
 */
class StatusUpdate extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява актуализирането на статус
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'sale/voucher')) {
            $json['error'] = 'Нямате права за модифициране на ваучери';
            $this->setJSONResponseOutput($json);
            return;
        }

        $voucher_id = (int)$this->requestPost('voucher_id', 0);
        $status = (int)$this->requestPost('status', 0);

        if (!$voucher_id) {
            $json['error'] = 'Невалиден номер на ваучер';
            $this->setJSONResponseOutput($json);
            return;
        }

        try {
            $this->loadModelAs('sale/voucher', 'voucherModel');

            // Проверка дали ваучерът съществува
            $voucher_info = $this->voucherModel->getVoucher($voucher_id);
            if (!$voucher_info) {
                $json['error'] = 'Ваучерът не е намерен';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Актуализиране на статуса
            $this->voucherModel->editVoucher($voucher_id, [
                'code' => $voucher_info['code'],
                'from_name' => $voucher_info['from_name'],
                'from_email' => $voucher_info['from_email'],
                'to_name' => $voucher_info['to_name'],
                'to_email' => $voucher_info['to_email'],
                'voucher_theme_id' => $voucher_info['voucher_theme_id'],
                'message' => $voucher_info['message'],
                'amount' => $voucher_info['amount'],
                'status' => $status
            ]);

            $json['success'] = 'Статусът е актуализиран успешно';
            $json['status_text'] = $status ? 'Активен' : 'Неактивен';
            $json['status_class'] = $status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';

        } catch (Exception $e) {
            $json['error'] = 'Грешка при актуализиране: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }
}
