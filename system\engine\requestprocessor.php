<?php
class RequestProcessor {
	private $registry;
	private $controllerPath;
	
	public function __construct($registry, $controllerPath) {
		$this->registry = $registry;
		$this->controllerPath = $controllerPath; // Frontend or Backend
	}
	
	/**
	 * Метод, който обработва заявки към контролери
	 * @param object $controller Инстанция на контролера
	 * @param string $method Името на метода, който се извиква
	 * @param array $args Аргументи за метода
	 * @return mixed Резултат от изпълнението на метода
	 */
	public function process($controller, $method, $args = array()) {

		$controller_name = is_object($controller) ? get_class($controller) : $controller;

		// F()->log->developer($this->controllerPath . ' => ' . $controller_name . ' => ' . $method, __FILE__, __LINE__);

		if(defined('THEME_REQUEST_PROCESSOR') && class_exists(THEME_REQUEST_PROCESSOR)) {
			$class = THEME_REQUEST_PROCESSOR;
			$instance = new $class($this->registry, $this->controllerPath);
			$result = $instance->process($controller_name, $method, $args);
			if($result !== -11111111111) return $result;
		}

		if(!is_object($controller)) {
			return new \Exception('Error: Could not call ' . $controller . '/' . $method . '!');
		}

		// извикваме оригиналния метод
		return call_user_func_array(array($controller, $method), $args);
	}
}
