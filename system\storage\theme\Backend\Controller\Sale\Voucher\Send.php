<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за изпращане на ваучер по email
 */
class Send extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява изпращането на ваучер
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'sale/voucher')) {
            $json['error'] = 'Нямате права за изпращане на ваучери';
            $this->setJSONResponseOutput($json);
            return;
        }

        $voucher_id = (int)$this->requestPost('voucher_id', 0);

        if (!$voucher_id) {
            $json['error'] = 'Невалиден номер на ваучер';
            $this->setJSONResponseOutput($json);
            return;
        }

        try {
            $this->loadModelsAs([
                'sale/voucher' => 'voucherModel',
                'sale/voucher_theme' => 'voucherThemeModel',
                'setting/setting' => 'settingModel'
            ]);

            // Получаване на информацията за ваучера
            $voucher_info = $this->voucherModel->getVoucher($voucher_id);
            if (!$voucher_info) {
                $json['error'] = 'Ваучерът не е намерен';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Проверка дали ваучерът е активен
            if (!$voucher_info['status']) {
                $json['error'] = 'Ваучерът не е активен и не може да бъде изпратен';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Получаване на темата на ваучера
            $voucher_theme_info = $this->voucherThemeModel->getVoucherTheme($voucher_info['voucher_theme_id']);

            // Подготвяне на данните за email
            $email_data = [
                'voucher_code' => $voucher_info['code'],
                'from_name' => $voucher_info['from_name'],
                'from_email' => $voucher_info['from_email'],
                'to_name' => $voucher_info['to_name'],
                'to_email' => $voucher_info['to_email'],
                'amount' => $this->formatCurrency($voucher_info['amount'], 'BGN', 1),
                'message' => $voucher_info['message'],
                'theme_name' => $voucher_theme_info['name'] ?? 'Стандартна тема',
                'store_name' => $this->config->get('config_name'),
                'store_url' => $this->config->get('config_url')
            ];

            // Изпращане на email
            if ($this->sendVoucherEmail($email_data)) {
                $json['success'] = 'Ваучерът е изпратен успешно на ' . $voucher_info['to_email'];
            } else {
                $json['error'] = 'Грешка при изпращане на email';
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при изпращане: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Изпраща email с ваучера
     *
     * @param array $data
     * @return bool
     */
    private function sendVoucherEmail($data) {
        try {
            // Зареждане на mail библиотеката
            $mail = new \Mail($this->config->get('config_mail_engine'));
            $mail->parameter = $this->config->get('config_mail_parameter');
            $mail->smtp_hostname = $this->config->get('config_mail_smtp_hostname');
            $mail->smtp_username = $this->config->get('config_mail_smtp_username');
            $mail->smtp_password = html_entity_decode($this->config->get('config_mail_smtp_password'), ENT_QUOTES, 'UTF-8');
            $mail->smtp_port = $this->config->get('config_mail_smtp_port');
            $mail->smtp_timeout = $this->config->get('config_mail_smtp_timeout');

            $mail->setTo($data['to_email']);
            $mail->setFrom($this->config->get('config_email'));
            $mail->setSender($data['store_name']);
            $mail->setSubject('Подаръчен ваучер от ' . $data['from_name']);

            // HTML съдържание на email
            $html_content = $this->generateVoucherEmailHTML($data);
            $mail->setHtml($html_content);

            // Текстово съдържание на email
            $text_content = $this->generateVoucherEmailText($data);
            $mail->setText($text_content);

            $mail->send();

            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Генерира HTML съдържание за email
     *
     * @param array $data
     * @return string
     */
    private function generateVoucherEmailHTML($data) {
        $html = '
        <html>
        <head>
            <meta charset="utf-8">
            <title>Подаръчен ваучер</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h1 style="color: #2c5aa0; text-align: center;">Подаръчен ваучер</h1>
                
                <p>Здравейте, <strong>' . htmlspecialchars($data['to_name']) . '</strong>!</p>
                
                <p>Получихте подаръчен ваучер от <strong>' . htmlspecialchars($data['from_name']) . '</strong>.</p>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
                    <h2 style="margin: 0; color: #2c5aa0;">Код на ваучера</h2>
                    <p style="font-size: 24px; font-weight: bold; color: #e74c3c; margin: 10px 0;">' . htmlspecialchars($data['voucher_code']) . '</p>
                    <p style="font-size: 18px; margin: 0;">Стойност: <strong>' . htmlspecialchars($data['amount']) . '</strong></p>
                </div>';

        if (!empty($data['message'])) {
            $html .= '
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #856404;">Лично съобщение:</h3>
                    <p style="margin-bottom: 0;">' . nl2br(htmlspecialchars($data['message'])) . '</p>
                </div>';
        }

        $html .= '
                <p>За да използвате ваучера, въведете кода по време на поръчка в нашия онлайн магазин.</p>
                
                <p style="text-align: center;">
                    <a href="' . htmlspecialchars($data['store_url']) . '" 
                       style="background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                        Посетете магазина
                    </a>
                </p>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                
                <p style="font-size: 12px; color: #666; text-align: center;">
                    Този email е изпратен от ' . htmlspecialchars($data['store_name']) . '<br>
                    ' . htmlspecialchars($data['store_url']) . '
                </p>
            </div>
        </body>
        </html>';

        return $html;
    }

    /**
     * Генерира текстово съдържание за email
     *
     * @param array $data
     * @return string
     */
    private function generateVoucherEmailText($data) {
        $text = "Подаръчен ваучер\n\n";
        $text .= "Здравейте, " . $data['to_name'] . "!\n\n";
        $text .= "Получихте подаръчен ваучер от " . $data['from_name'] . ".\n\n";
        $text .= "Код на ваучера: " . $data['voucher_code'] . "\n";
        $text .= "Стойност: " . $data['amount'] . "\n\n";

        if (!empty($data['message'])) {
            $text .= "Лично съобщение:\n" . $data['message'] . "\n\n";
        }

        $text .= "За да използвате ваучера, въведете кода по време на поръчка в нашия онлайн магазин.\n\n";
        $text .= "Посетете магазина: " . $data['store_url'] . "\n\n";
        $text .= "---\n";
        $text .= "Този email е изпратен от " . $data['store_name'] . "\n";
        $text .= $data['store_url'];

        return $text;
    }
}
