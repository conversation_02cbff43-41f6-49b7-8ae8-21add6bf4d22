# Rakla.bg - Технически стек

## Основни технологии
- **PHP**: Основен език за бекенд разработка
- **MySQL**: Система за бази данни с поддръжка на двойна база данни
- **JavaScript/jQuery**: Интерактивност на фронтенда
- **HTML/CSS**: Рендериране на фронтенда
- **OpenCart**: Базова рамка за електронна търговия (модифицирана)

## Библиотеки и зависимости
- **PhpSpreadsheet**: Използва се за обработка на Excel файлове (импорт/експорт)
- **SimpleXML**: XML обработка за функционалност за импорт/експорт
- **ENV Loader**: Персонализирана имплементация за конфигурация на средата

## Персонализирани разширения на рамката
- **Theme25 Framework**: Персонализирано разширение на OpenCart с:
  - `Controller.php`: Разширен базов контролер с помощни методи
  - `ControllerSubMethods.php`: Поддръжка за архитектура на подконтролери
  - `Data.php`: Помощни програми за обработка на данни
  - `EnvLoader.php`: Управление на конфигурацията на средата
  - `SecondDB.php`: Управление на връзка с двойна база данни
  - `ThemeStyles.php`: CSS селектор константи
  - `ConfigManager.php`: Превключване на конфигурацията между бази данни

## Архитектурен модел
- **Dispatcher архитектура**: Основните контролери действат като диспечери към подконтролери
- **MVC модел**: Модифициран OpenCart MVC с подобрено разделение на отговорностите
- **Структура на пространството от имена**: `Theme25\Backend\Controller\{Module}\{Controller}`

## Среда за разработка
- **Linux**: Основна платформа за разработка
- **Уеб сървър**: Apache (изведено от .htaccess файлове)

## Машина на която работи разработчика
- **Windows 10**
- Инсталиран е PHP и MySQL
- Използван е уеб сървър Apache

## Изграждане и внедряване
Не е идентифицирана специфична система за изграждане. Проектът изглежда използва директно изпълнение на PHP без стъпки за компилация.

## Общи команди
### Разработка
```
# Не са идентифицирани специфични команди за изграждане
# Директно изпълнение на PHP чрез уеб сървър
```

### База данни
```sql
-- Примерни SQL команди, намерени в проекта
-- Създаване на нова таблица
CREATE TABLE `oc_promotions` (
  `promotion_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `data` text NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`promotion_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
```

### Тестване
Тестването на локалната машина под Windows 10 е невъзможно, защото няма инсталирана Opencart