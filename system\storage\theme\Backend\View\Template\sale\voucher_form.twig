<!-- Voucher Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">{{ form_title }}</h1>
            <p class="text-gray-500 mt-1">Попълнете информацията за подаръчния ваучер</p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-2">
            <a href="{{ cancel_url }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <i class="ri-arrow-left-line mr-2"></i>
                {{ button_cancel }}
            </a>
            <button type="submit" form="voucher-form"
                    class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                <i class="ri-save-line mr-2"></i>
                {{ button_save }}
            </button>
        </div>
    </div>
</div>

<!-- Form Content -->
<div class="bg-white">
    <form id="voucher-form" method="post" action="{{ action_url }}" class="p-6">
        <!-- Скрито поле за user_token -->
        <input type="hidden" name="user_token" value="{{ user_token }}">
        
        <!-- Error Messages -->
        {% if errors %}
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="ri-error-warning-line text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Има грешки във формата:</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc pl-5 space-y-1">
                            {% for field, error in errors %}
                            <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Лява колона -->
            <div class="space-y-6">
                <!-- Код на ваучер -->
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                        Код на ваучер <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="code" 
                           name="code" 
                           value="{{ form_data.code }}"
                           maxlength="10"
                           class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.code %}border-red-500{% endif %}"
                           placeholder="Въведете код на ваучера">
                    {% if errors.code %}
                    <p class="mt-1 text-sm text-red-600">{{ errors.code }}</p>
                    {% endif %}
                </div>

                <!-- Сума -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Сума <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="number" 
                               id="amount" 
                               name="amount" 
                               value="{{ form_data.amount }}"
                               step="0.01"
                               min="0"
                               class="w-full px-4 py-2 pr-12 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.amount %}border-red-500{% endif %}"
                               placeholder="0.00">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 text-sm">лв.</span>
                        </div>
                    </div>
                    {% if errors.amount %}
                    <p class="mt-1 text-sm text-red-600">{{ errors.amount }}</p>
                    {% endif %}
                </div>

                <!-- Тема на ваучер -->
                <div>
                    <label for="voucher_theme_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Тема на ваучер <span class="text-red-500">*</span>
                    </label>
                    <select id="voucher_theme_id" 
                            name="voucher_theme_id"
                            class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.voucher_theme_id %}border-red-500{% endif %}">
                        <option value="">Изберете тема</option>
                        {% if voucher_themes %}
                            {% for theme in voucher_themes %}
                            <option value="{{ theme.value }}" {% if theme.value == form_data.voucher_theme_id %}selected{% endif %}>
                                {{ theme.text }}
                            </option>
                            {% endfor %}
                        {% endif %}
                    </select>
                    {% if errors.voucher_theme_id %}
                    <p class="mt-1 text-sm text-red-600">{{ errors.voucher_theme_id }}</p>
                    {% endif %}
                </div>

                <!-- Статус -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Статус
                    </label>
                    <div class="flex items-center">
                        <input type="hidden" name="status" value="0">
                        <input type="checkbox" 
                               id="status" 
                               name="status" 
                               value="1"
                               {% if form_data.status %}checked{% endif %}
                               class="rounded border-gray-300 text-primary focus:ring-primary">
                        <label for="status" class="ml-2 text-sm text-gray-700">Активен</label>
                    </div>
                </div>
            </div>

            <!-- Дясна колона -->
            <div class="space-y-6">
                <!-- Подарител -->
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="from_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Име на подарител <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="from_name" 
                               name="from_name" 
                               value="{{ form_data.from_name }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.from_name %}border-red-500{% endif %}"
                               placeholder="Въведете име на подарителя">
                        {% if errors.from_name %}
                        <p class="mt-1 text-sm text-red-600">{{ errors.from_name }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="from_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email на подарител <span class="text-red-500">*</span>
                        </label>
                        <input type="email" 
                               id="from_email" 
                               name="from_email" 
                               value="{{ form_data.from_email }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.from_email %}border-red-500{% endif %}"
                               placeholder="<EMAIL>">
                        {% if errors.from_email %}
                        <p class="mt-1 text-sm text-red-600">{{ errors.from_email }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Получател -->
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="to_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Име на получател <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="to_name" 
                               name="to_name" 
                               value="{{ form_data.to_name }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.to_name %}border-red-500{% endif %}"
                               placeholder="Въведете име на получателя">
                        {% if errors.to_name %}
                        <p class="mt-1 text-sm text-red-600">{{ errors.to_name }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="to_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email на получател <span class="text-red-500">*</span>
                        </label>
                        <input type="email" 
                               id="to_email" 
                               name="to_email" 
                               value="{{ form_data.to_email }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary {% if errors.to_email %}border-red-500{% endif %}"
                               placeholder="<EMAIL>">
                        {% if errors.to_email %}
                        <p class="mt-1 text-sm text-red-600">{{ errors.to_email }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Съобщение -->
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                        Съобщение
                    </label>
                    <textarea id="message" 
                              name="message" 
                              rows="4"
                              class="w-full px-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary"
                              placeholder="Въведете персонално съобщение (опционално)">{{ form_data.message }}</textarea>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end gap-3">
            <a href="{{ cancel_url }}" 
               class="px-6 py-2 border border-gray-300 rounded-button text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                {{ button_cancel }}
            </a>
            <button type="submit"
                    class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                {{ button_save }}
            </button>
        </div>
    </form>
</div>
