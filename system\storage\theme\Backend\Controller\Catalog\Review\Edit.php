<?php

namespace Theme25\Backend\Controller\Catalog\Review;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'review-form.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Редактиране на коментар');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/review_form');
    }

    /**
     * Подготвя данните за формата за редактиране
     */
    private function prepareData() {
        $this->loadModelsAs([
            'catalog/review' => 'reviewModel',
            'catalog/product' => 'productModel'
        ]);

        $this->prepareFormData()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя данните за формата
     */
    private function prepareFormData() {
        $review_id = (int)$this->requestGet('review_id', 0);

        if (!$review_id) {
            // Пренасочване към листинга ако няма ID
            $this->redirect($this->getAdminLink('catalog/review'));
            return $this;
        }

        // Зареждане на информацията за коментара
        $review_info = $this->reviewModel->getReview($review_id);

        if (!$review_info) {
            // Пренасочване към листинга ако коментарът не съществува
            $this->redirect($this->getAdminLink('catalog/review'));
            return $this;
        }

        // Подготвяне на данните за формата
        $this->setData([
            'review_id' => $review_info['review_id'],
            'product_id' => $review_info['product_id'],
            'product_name' => $review_info['product'] ?? 'Неизвестен продукт',
            'author' => $review_info['author'],
            'text' => $review_info['text'],
            'rating' => $review_info['rating'],
            'status' => $review_info['status'],
            'date_added' => $review_info['date_added']
        ]);

        // Подготвяне на опциите за рейтинг
        $rating_options = [];
        for ($i = 1; $i <= 5; $i++) {
            $rating_options[] = [
                'value' => $i,
                'text' => $i . ' звезд' . ($i == 1 ? 'а' : 'и'),
                'selected' => ($i == $review_info['rating'])
            ];
        }

        $this->setData('rating_options', $rating_options);

        // Подготвяне на опциите за статус
        $status_options = [
            [
                'value' => 1, 
                'text' => 'Активен',
                'selected' => ($review_info['status'] == 1)
            ],
            [
                'value' => 0, 
                'text' => 'Неактивен',
                'selected' => ($review_info['status'] == 0)
            ]
        ];

        $this->setData('status_options', $status_options);

        return $this;
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        $review_id = $this->data['review_id'] ?? '';
        
        $this->setData([
            'action_url' => $this->getAdminLink('catalog/review/save', 'review_id=' . $review_id),
            'cancel_url' => $this->getAdminLink('catalog/review'),
            'product_autocomplete_url' => $this->getAdminLink('catalog/product/autocomplete')
        ]);

        return $this;
    }
}
