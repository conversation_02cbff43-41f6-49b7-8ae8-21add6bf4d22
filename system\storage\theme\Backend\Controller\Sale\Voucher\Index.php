<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за списъка с подаръчни ваучери
 *
 * Този контролер управлява логиката за показване на списъка с ваучери,
 * включително филтриране, сортиране и пагинация.
 *
 * @package Theme25\Backend\Controller\Sale\Voucher
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Index extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за списъка с ваучери
     */
    public function prepareData() {
        $this->prepareVoucherListData()
             ->prepareFilterOptions()
             ->prepareVoucherItems()
             ->preparePagination();

        // Подготвяне на URL-и и филтри за шаблона
        $filter_params = [];
        foreach ($this->filters as $key => $value) {
            if ($value !== '' && $key !== 'page') {
                $filter_params[$key] = $value;
            }
        }

        // Генериране на URL параметри за филтри
        $filter_url_params = $this->buildFilterParams();

        $this->setData([
            'back_url' => $this->getAdminLink('sale/voucher'),
            'current_url' => $this->getAdminLink('sale/voucher', $filter_url_params),
            'clear_filters_url' => $this->getAdminLink('sale/voucher'),
            'filter_action_url' => $this->getAdminLink('sale/voucher'),
            'add_url' => $this->getAdminLink('sale/voucher/add'),
            // Филтри за шаблона
            'filter_status' => $this->filters['filter_status'],
            'filter_search' => $this->filters['filter_search'],
            'filter_code' => $this->filters['filter_code'],
            'filter_from_name' => $this->filters['filter_from_name'],
            'filter_to_name' => $this->filters['filter_to_name'],
            'filter_amount' => $this->filters['filter_amount'],
            'filter_date_added' => $this->filters['filter_date_added'],
            // Сортиране
            'sort' => $this->filters['sort'],
            'order' => $this->filters['order'],
            // User token за AJAX заявки
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя основните данни за списъка с ваучери
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareVoucherListData() {
        // Зареждане на необходимите модели
        try {
            $this->loadModelsAs([
                'sale/voucher' => 'voucherModel',
                'sale/voucher_theme' => 'voucherThemeModel',
                'tool/image' => 'imageModel'
            ]);
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на модели: ' . $e->getMessage());
            return $this;
        }

        // Получаване на филтри от заявката
        $this->filters = [
            'filter_code' => $this->requestGet('filter_code', ''),
            'filter_from_name' => $this->requestGet('filter_from_name', ''),
            'filter_to_name' => $this->requestGet('filter_to_name', ''),
            'filter_amount' => $this->requestGet('filter_amount', ''),
            'filter_status' => $this->requestGet('filter_status', ''),
            'filter_date_added' => $this->requestGet('filter_date_added', ''),
            'filter_search' => $this->requestGet('filter_search', ''),
            'sort' => $this->requestGet('sort', 'v.date_added'),
            'order' => $this->requestGet('order', 'DESC'),
            'page' => (int)$this->requestGet('page', 1)
        ];

        // Ако има общо търсене, разпределяме го по отделните филтри
        if (!empty($this->filters['filter_search'])) {
            $search_term = trim($this->filters['filter_search']);
            // Ако изглежда като код на ваучер
            if (strlen($search_term) <= 10 && preg_match('/^[A-Z0-9]+$/', strtoupper($search_term))) {
                $this->filters['filter_code'] = $search_term;
            }
            // Ако изглежда като сума
            elseif (is_numeric($search_term)) {
                $this->filters['filter_amount'] = $search_term;
            }
            // Иначе търсим в имена
            else {
                $this->filters['filter_from_name'] = $search_term;
                $this->filters['filter_to_name'] = $search_term;
            }
        }

        return $this;
    }

    /**
     * Подготвя опциите за филтриране
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareFilterOptions() {
        // Статуси на ваучери
        $this->setData('status_options', [
            ['value' => '', 'text' => 'Всички статуси'],
            ['value' => '1', 'text' => 'Активен'],
            ['value' => '0', 'text' => 'Неактивен']
        ]);

        // Предаване на филтрите към view
        foreach ($this->filters as $key => $value) {
            $this->setData($key, $value);
        }

        return $this;
    }

    /**
     * Подготвя елементите от списъка с ваучери
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareVoucherItems() {
        $limit = $this->config->get('config_limit_admin') ?: 20;
        $start = ($this->filters['page'] - 1) * $limit;

        $filter_data = array_merge($this->filters, [
            'start' => $start,
            'limit' => $limit
        ]);

        try {
            // Получаване на ваучерите
            $vouchers = $this->voucherModel->getVouchers($filter_data);
            $total_vouchers = $this->voucherModel->getTotalVouchers();

            // Форматиране на данните за view
            $formatted_vouchers = [];
            foreach ($vouchers as $voucher) {
                $formatted_vouchers[] = [
                    'voucher_id' => $voucher['voucher_id'],
                    'code' => $voucher['code'],
                    'from_name' => $voucher['from_name'],
                    'from_email' => $voucher['from_email'],
                    'to_name' => $voucher['to_name'],
                    'to_email' => $voucher['to_email'],
                    'theme' => $voucher['theme'] ?: 'Няма тема',
                    'amount' => $this->formatCurrency($voucher['amount'], 'BGN', 1),
                    'status' => $voucher['status'] ? 'Активен' : 'Неактивен',
                    'status_id' => $voucher['status'],
                    'status_class' => $this->getStatusClass($voucher['status']),
                    'date_added' => date('d.m.Y H:i', strtotime($voucher['date_added'])),
                    'edit_url' => $this->getAdminLink('sale/voucher/edit', 'voucher_id=' . $voucher['voucher_id']),
                    'delete_url' => $this->getAdminLink('sale/voucher/delete', 'voucher_id=' . $voucher['voucher_id']),
                    'history_url' => $this->getAdminLink('sale/voucher/history', 'voucher_id=' . $voucher['voucher_id']),
                    'send_url' => $this->getAdminLink('sale/voucher/send', 'voucher_id=' . $voucher['voucher_id'])
                ];
            }

            $this->setData([
                'vouchers' => $formatted_vouchers,
                'total_vouchers' => $total_vouchers
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при получаване на данни: ' . $e->getMessage());
            $this->setData([
                'vouchers' => [],
                'total_vouchers' => 0
            ]);
        }

        return $this;
    }

    /**
     * Подготвя пагинацията
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin') ?: 20;
        $total = $this->getData('total_vouchers') ?: 0;
        $page = $this->filters['page'];

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $url_separator = !empty($filter_params) ? '&' : '';
        $pagination->url = $this->getAdminLink('sale/voucher', $filter_params . $url_separator . 'page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('sale/voucher', $filter_params . $url_separator . 'limit={limit}'));
        $pagination->setProductText('ваучери');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)',
                ($page - 1) * $limit + 1,
                min($total, $page * $limit),
                $total,
                ceil($total / $limit)
            ),
            'limit_options' => array_map(function($limit_val) {
                return ['value' => $limit_val, 'text' => (string)$limit_val];
            }, $limits),
            'current_limit' => $limit
        ]);

        return $this;
    }

    /**
     * Получава CSS класа за статуса по ID
     *
     * @param int $status_id
     * @return string
     */
    private function getStatusClass($status_id) {
        $classes = [
            '1' => 'bg-green-100 text-green-800',
            '0' => 'bg-red-100 text-red-800'
        ];

        return $classes[$status_id] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Генерира параметрите за филтриране в URL
     *
     * @return string
     */
    private function buildFilterParams() {
        $params = [];

        foreach ($this->filters as $key => $value) {
            if ($value !== '' && $key !== 'page') {
                $params[] = $key . '=' . urlencode($value);
            }
        }

        return !empty($params) ? implode('&', $params) : '';
    }
}
