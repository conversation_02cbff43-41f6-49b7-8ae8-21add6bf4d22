{{ header }}{{ column_left }}

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0 text-2xl font-bold text-gray-900">{{ heading_title }}</h1>
                </div>
                <div class="col-sm-6">
                    <div class="flex justify-end space-x-2">
                        <a href="{{ back_url }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="ri-arrow-left-line mr-2"></i>
                            Назад
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <form id="option-form" class="space-y-6">
                <input type="hidden" name="user_token" value="{{ user_token }}">
                <input type="hidden" name="option_id" value="{{ option_id }}">

                <!-- Основна информация -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Основна информация</h3>
                    </div>
                    
                    <div class="p-6 space-y-6">
                        <!-- Тип опция -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                                    Тип опция <span class="text-red-500">*</span>
                                </label>
                                <select name="type" 
                                        id="type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required>
                                    <option value="">Изберете тип опция</option>
                                    {% for type_key, type_name in option_types %}
                                        <option value="{{ type_key }}" {% if type == type_key %}selected{% endif %}>
                                            {{ type_name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                                    Подредба
                                </label>
                                <input type="number" 
                                       name="sort_order" 
                                       id="sort_order"
                                       value="{{ sort_order }}"
                                       placeholder="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Описания на опцията -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Описания на опцията</h3>
                    </div>
                    
                    <div class="p-6">
                        <!-- Езикови табове -->
                        <div class="flex space-x-1 mb-6 border-b border-gray-200">
                            {% for language in languages %}
                                <button type="button" 
                                        class="language-tab px-4 py-2 text-sm font-medium rounded-t-lg border-b-2 border-transparent hover:border-gray-300 focus:outline-none"
                                        data-language-id="{{ language.language_id }}"
                                        data-language-code="{{ language.code }}">
                                    <img src="language/{{ language.code }}/{{ language.code }}.png" 
                                         alt="{{ language.name }}" 
                                         class="inline-block w-4 h-4 mr-2">
                                    {{ language.name }}
                                </button>
                            {% endfor %}
                        </div>

                        <!-- Езикови панели -->
                        {% for language in languages %}
                            <div id="language-{{ language.language_id }}" class="language-panel hidden">
                                <div class="space-y-4">
                                    <div>
                                        <label for="name_{{ language.language_id }}" class="block text-sm font-medium text-gray-700 mb-2">
                                            Име на опцията ({{ language.name }}) <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" 
                                               name="option_description[{{ language.language_id }}][name]" 
                                               id="name_{{ language.language_id }}"
                                               value="{{ option_description[language.language_id].name }}"
                                               placeholder="Въведете име на опцията"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               maxlength="128"
                                               required>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Стойности на опцията -->
                <div id="option-values-section" class="bg-white rounded-lg shadow-sm border border-gray-200 hidden">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Стойности на опцията</h3>
                            <button type="button" 
                                    id="add-option-value"
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center">
                                <i class="ri-add-line mr-2"></i>
                                Добави стойност
                            </button>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <div id="option-values-container" class="space-y-4">
                            {% if option_values %}
                                {% for key, option_value in option_values %}
                                    <div class="option-value-item bg-gray-50 p-4 rounded-lg border border-gray-200">
                                        <div class="flex justify-between items-center mb-3">
                                            <h4 class="text-sm font-medium text-gray-900">Стойност {{ key + 1 }}</h4>
                                            <button type="button" class="remove-value-btn text-red-600 hover:text-red-800">
                                                <i class="ri-delete-bin-line"></i> Премахни
                                            </button>
                                        </div>
                                        
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div class="space-y-4">
                                                <!-- Езикови табове за стойността -->
                                                <div class="flex space-x-1 mb-2">
                                                    {% for language in languages %}
                                                        <button type="button" 
                                                                class="value-language-tab px-2 py-1 text-xs font-medium rounded border border-gray-300 hover:bg-gray-100"
                                                                data-value-index="{{ key }}"
                                                                data-language-id="{{ language.language_id }}">
                                                            <img src="language/{{ language.code }}/{{ language.code }}.png" 
                                                                 alt="{{ language.name }}" 
                                                                 class="inline-block w-3 h-3 mr-1">
                                                            {{ language.code|upper }}
                                                        </button>
                                                    {% endfor %}
                                                </div>
                                                
                                                {% for language in languages %}
                                                    <div class="value-language-panel" id="value-{{ key }}-language-{{ language.language_id }}">
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                                            Име ({{ language.name }})
                                                        </label>
                                                        <input type="text" 
                                                               name="option_value[{{ key }}][option_value_description][{{ language.language_id }}][name]" 
                                                               value="{{ option_value.option_value_description[language.language_id].name }}"
                                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                               placeholder="Въведете име на стойността">
                                                    </div>
                                                {% endfor %}
                                            </div>
                                            
                                            <div class="space-y-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Изображение</label>
                                                    <input type="text" 
                                                           name="option_value[{{ key }}][image]" 
                                                           value="{{ option_value.image }}"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                           placeholder="Път към изображение">
                                                </div>
                                                
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                                                    <input type="number" 
                                                           name="option_value[{{ key }}][sort_order]" 
                                                           value="{{ option_value.sort_order }}"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <input type="hidden" name="option_value[{{ key }}][option_value_id]" value="{{ option_value.option_value_id }}">
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        {% if not option_values %}
                            <div class="text-center py-8 text-gray-500">
                                <i class="ri-add-circle-line text-3xl mb-2"></i>
                                <p>Няма добавени стойности. Натиснете "Добави стойност" за да започнете.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Бутони за действия -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6">
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-2">
                                {% if option_id > 0 %}
                                    <button type="button" 
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center"
                                            onclick="if(confirm('Сигурни ли сте, че искате да изтриете тази опция?')) { window.location.href='{{ delete_url }}'; }">
                                        <i class="ri-delete-bin-line mr-2"></i>
                                        Изтрий опция
                                    </button>
                                {% endif %}
                            </div>
                            
                            <div class="flex space-x-2">
                                <a href="{{ cancel }}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg flex items-center">
                                    <i class="ri-close-line mr-2"></i>
                                    Отказ
                                </a>
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center">
                                    <i class="ri-save-line mr-2"></i>
                                    Запази
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
</div>

{{ footer }}
