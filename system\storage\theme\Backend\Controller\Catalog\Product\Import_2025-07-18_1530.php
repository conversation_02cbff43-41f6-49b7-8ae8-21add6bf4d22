<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Import extends \Theme25\ControllerSubMethods
{

    private $supportedFormats = [];
    private $unavailableFormats = [];
    private $formatRequirements = [];
    private $batchSize = 150;
    private $largeBatchSize = 100; // Намален batch размер за memory optimization
    private $largeImportThreshold = 1000; // Праг за големи импорти
    private $existingProducts = [];
    private $languageMapping = [];
    private $categoryCache = [];
    private $testMode = false;
    private $testModeStats = [
        'queries_saved' => 0,
        'inserts_saved' => 0,
        'updates_saved' => 0
    ];
    private $importCacheDir = '';
    private $fieldHelper;

    public function __construct($registry)
    {
        parent::__construct($registry);

        // Инициализираме import cache директорията
        $this->importCacheDir = DIR_CACHE . 'import/';
        $this->initializeImportCacheDir();

        // Зареждаме field helper
        $this->loadModelAs('catalog/importfieldshelper', 'fieldHelper');

        $this->loadScripts();
        $this->checkAvailableFormats();
        $this->initializeLanguageMapping();
        $this->initializeCategoryCache();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts()
    {
        $this->addBackendScriptWithVersion([
            'product-import.js',
            'product-import-progress.js',
        ], 'footer');
    }

    /**
     * Проверява кои формати за импорт са налични
     */
    private function checkAvailableFormats()
    {
        $this->supportedFormats = [];
        $this->unavailableFormats = [];
        $this->formatRequirements = [];

        // CSV - винаги наличен (вграден в PHP)
        $this->supportedFormats[] = 'csv';
        $this->formatRequirements['csv'] = [
            'name' => 'CSV',
            'description' => 'Comma Separated Values (.csv)',
            'icon' => 'ri-file-text-line',
            'available' => true,
            'reason' => 'Вграден в PHP'
        ];

        // XML - проверка за simplexml разширение
        if (extension_loaded('simplexml')) {
            $this->supportedFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => true,
                'reason' => 'SimpleXML разширение активно'
            ];
        } else {
            $this->unavailableFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => false,
                'reason' => 'Липсва SimpleXML разширение'
            ];
        }

        // XLSX - проверка за PhpSpreadsheet или алтернативни библиотеки
        $xlsxAvailable = false;
        $xlsxReason = '';

        // Проверка за PhpOffice\PhpSpreadsheet
        if (class_exists('PhpOffice\\PhpSpreadsheet\\IOFactory')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet библиотека';
        }
        // Проверка за алтернативна библиотека SimpleXLSX
        elseif (class_exists('SimpleXLSX')) {
            $xlsxAvailable = true;
            $xlsxReason = 'SimpleXLSX библиотека';
        }
        // Проверка дали файлът съществува в проекта
        elseif (file_exists(DIR_SYSTEM . 'storage/vendor/phpoffice/phpspreadsheet/src/PhpSpreadsheet/IOFactory.php')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet (локално)';
        } else {
            $xlsxReason = 'Липсва PhpOffice\\PhpSpreadsheet или SimpleXLSX библиотека';
        }

        if ($xlsxAvailable) {
            $this->supportedFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => true,
                'reason' => $xlsxReason
            ];
        } else {
            $this->unavailableFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => false,
                'reason' => $xlsxReason
            ];
        }

        // Логиране на резултатите
        F()->log->developer('Available import formats: ' . implode(', ', $this->supportedFormats), __FILE__, __LINE__);
        if (!empty($this->unavailableFormats)) {
            F()->log->developer('Unavailable import formats: ' . implode(', ', $this->unavailableFormats), __FILE__, __LINE__);
        }
    }

    /**
     * Инициализира мапинга на езиците
     */
    private function initializeLanguageMapping()
    {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        foreach ($languages as $language) {
            $this->languageMapping[$language['code']] = $language['language_id'];
        }
    }

    /**
     * Инициализира кеша на категориите
     */
    private function initializeCategoryCache()
    {
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadCategoryPaths();
    }

    /**
     * Зарежда всички категории с техните пътища в кеша
     */
    private function loadCategoryPaths()
    {
        $language_id = $this->getLanguageId();

        $sql = "SELECT
                    cp.category_id,
                    GROUP_CONCAT(cd.name ORDER BY cp.level SEPARATOR ' > ') AS path
                FROM `" . DB_PREFIX . "category_path` cp
                LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (cp.path_id = cd.category_id)
                WHERE cd.language_id = '{$language_id}'
                GROUP BY cp.category_id
                ORDER BY cp.category_id";

        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            $this->categoryCache[$row['path']] = $row['category_id'];
        }
    }

    /**
     * Инициализира директорията за import cache файлове
     */
    private function initializeImportCacheDir()
    {
        if (!is_dir($this->importCacheDir)) {
            if (!mkdir($this->importCacheDir, 0755, true)) {
                F()->log->developer('Failed to create import cache directory: ' . $this->importCacheDir, __FILE__, __LINE__);
                throw new \Exception('Не може да се създаде директория за import cache');
            }
        }

        // Почистваме стари import файлове (по-стари от 24 часа)
        $this->cleanupOldImportFiles();
    }

    /**
     * Почиства стари import cache файлове
     */
    private function cleanupOldImportFiles()
    {
        $files = glob($this->importCacheDir . 'import_*.json');
        $cutoffTime = time() - (24 * 60 * 60); // 24 часа

        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
                F()->log->developer('Cleaned up old import file: ' . basename($file), __FILE__, __LINE__);
            }
        }
    }

    /**
     * Записва import данни в JSON файл
     */
    private function saveImportData($importId, $data)
    {
        $filePath = $this->importCacheDir . 'import_' . $importId . '.json';

        // Добавяме timestamp за tracking
        $data['last_updated'] = time();
        $data['created_at'] = $data['created_at'] ?? time();

        $jsonData = json_encode($data, JSON_PRETTY_PRINT);

        // File locking за concurrent access
        $fp = fopen($filePath, 'w');
        if ($fp && flock($fp, LOCK_EX)) {
            fwrite($fp, $jsonData);
            flock($fp, LOCK_UN);
            fclose($fp);

            F()->log->developer('Import data saved to: ' . $filePath, __FILE__, __LINE__);
            return true;
        } else {
            F()->log->developer('Failed to save import data to: ' . $filePath, __FILE__, __LINE__);
            if ($fp)
                fclose($fp);
            return false;
        }
    }

    /**
     * Чете import данни от JSON файл
     */
    private function loadImportData($importId)
    {
        $filePath = $this->importCacheDir . 'import_' . $importId . '.json';

        if (!file_exists($filePath)) {
            F()->log->developer('Import file not found: ' . $filePath, __FILE__, __LINE__);
            return null;
        }

        // File locking за concurrent access
        $fp = fopen($filePath, 'r');
        if ($fp && flock($fp, LOCK_SH)) {
            $jsonData = fread($fp, filesize($filePath));
            flock($fp, LOCK_UN);
            fclose($fp);

            $data = json_decode($jsonData, true);
            if ($data === null) {
                F()->log->developer('Failed to decode JSON from: ' . $filePath, __FILE__, __LINE__);
                return null;
            }

            F()->log->developer('Import data loaded from: ' . $filePath, __FILE__, __LINE__);
            return $data;
        } else {
            F()->log->developer('Failed to read import data from: ' . $filePath, __FILE__, __LINE__);
            if ($fp)
                fclose($fp);
            return null;
        }
    }

    /**
     * Изтрива import данни от JSON файл
     */
    private function deleteImportData($importId)
    {
        $filePath = $this->importCacheDir . 'import_' . $importId . '.json';

        if (file_exists($filePath)) {
            if (unlink($filePath)) {
                F()->log->developer('Import data deleted: ' . $filePath, __FILE__, __LINE__);
                return true;
            } else {
                F()->log->developer('Failed to delete import data: ' . $filePath, __FILE__, __LINE__);
                return false;
            }
        }

        return true; // Файлът не съществува, считаме че е "изтрит"
    }

    /**
     * Инициира голям импорт с batch обработка
     */
    private function initiateLargeImport($products, $importFormat, $importData)
    {
        // Генерираме уникален ID за импорта
        $importId = uniqid('import_', true);
        $productCount = count($products);

        // Разделяме продуктите на batch-ове с по-малък размер
        $batches = array_chunk($products, $this->largeBatchSize);
        $totalBatches = count($batches);

        F()->log->developer("Инициира голям импорт: {$productCount} продукта в {$totalBatches} batch-а", __FILE__, __LINE__);

        // Запазваме информацията за импорта в JSON файл
        $importInfo = [
            'import_id' => $importId,
            'format' => $importFormat,
            'import_data' => $importData,
            'batches' => $batches,
            'total_batches' => $totalBatches,
            'total_products' => $productCount,
            'processed_batches' => 0,
            'processed_products' => 0,
            'added_products' => 0,
            'updated_products' => 0,
            'errors' => 0,
            'status' => 'initiated',
            'started_at' => time(),
            'test_mode' => $this->testMode
        ];

        if (!$this->saveImportData($importId, $importInfo)) {
            throw new \Exception('Не може да се запази информацията за импорта');
        }

        // Връщаме информация за започване на batch обработката
        $json = [
            'large_import' => true,
            'import_id' => $importId,
            'total_batches' => $totalBatches,
            'total_products' => $productCount,
            'batch_size' => $this->largeBatchSize,
            'message' => "Започва импорт на {$productCount} продукта в {$totalBatches} части"
        ];

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва един batch от голям импорт
     */
    public function processBatch()
    {
        $json = [];

        try {
            if (!isset($_POST['import_id']) || !isset($_POST['batch_index'])) {
                throw new \Exception('Липсват параметри за batch обработка');
            }

            $importId = $_POST['import_id'];
            $batchIndex = (int) $_POST['batch_index'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Проверяваме дали batch-ът е валиден
            if ($batchIndex >= $importInfo['total_batches']) {
                throw new \Exception('Невалиден batch индекс');
            }

            // Активираме тестов режим ако е зададен
            $this->testMode = $importInfo['test_mode'] ?? false;
            if ($this->testMode) {
                $this->testModeStats = [
                    'queries_saved' => 0,
                    'inserts_saved' => 0,
                    'updates_saved' => 0
                ];
            }

            // Получаваме продуктите за този batch
            $batchProducts = $importInfo['batches'][$batchIndex];

            F()->log->developer("Обработва batch {$batchIndex}: " . count($batchProducts) . " продукта", __FILE__, __LINE__);

            // Memory monitoring
            $memoryBefore = memory_get_usage(true);

            // Обработваме batch-а
            $batchResult = $this->processBatchProducts($batchProducts, $importInfo['format']);

            // Memory cleanup
            unset($batchProducts);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memoryAfter = memory_get_usage(true);

            // Обновяваме информацията за прогреса
            $importInfo['processed_batches'] = $batchIndex + 1;
            $importInfo['processed_products'] += $batchResult['processed'];
            $importInfo['added_products'] += $batchResult['added'];
            $importInfo['updated_products'] += $batchResult['updated'];
            $importInfo['errors'] += $batchResult['errors'];
            $importInfo['status'] = 'processing';

            // Записваме обновените данни
            if (!$this->saveImportData($importId, $importInfo)) {
                F()->log->developer('Failed to save import progress: ' . $importId, __FILE__, __LINE__);
                throw new \Exception('Не може да се запише прогресът на импорта');
            }

            // Пауза между batch-овете
            sleep(1);

            $json = [
                'success' => true,
                'batch_index' => $batchIndex,
                'processed_batches' => $batchIndex + 1,
                'total_batches' => $importInfo['total_batches'],
                'processed_products' => $importInfo['processed_products'],
                'total_products' => $importInfo['total_products'],
                'added_products' => $importInfo['added_products'],
                'updated_products' => $importInfo['updated_products'],
                'errors' => $importInfo['errors'],
                'memory_usage' => $memoryAfter,
                'memory_usage_mb' => round($memoryAfter / 1024 / 1024, 2),
                'batch_result' => $batchResult
            ];

            if ($this->testMode) {
                $json['test_stats'] = $this->testModeStats;
            }

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Batch processing error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Финализира голям импорт
     */
    public function finalizeLargeImport()
    {
        $json = [];

        try {
            if (!isset($_POST['import_id'])) {
                throw new \Exception('Липсва import_id параметър');
            }

            $importId = $_POST['import_id'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Обновяваме статуса
            $importInfo['status'] = 'completed';
            $importInfo['completed_at'] = time();
            $importInfo['duration'] = $importInfo['completed_at'] - $importInfo['started_at'];

            // Записваме финалните данни
            $this->saveImportData($importId, $importInfo);

            F()->log->developer("Импорт завършен: {$importInfo['processed_products']} продукта", __FILE__, __LINE__);

            $json = [
                'success' => true,
                'message' => $this->testMode ?
                    'Тестовият импорт завърши успешно (няма реални промени в базата данни)' :
                    'Импортът завърши успешно',
                'statistics' => [
                    'total_processed' => $importInfo['processed_products'],
                    'added' => $importInfo['added_products'],
                    'updated' => $importInfo['updated_products'],
                    'errors' => $importInfo['errors'],
                    'duration' => $importInfo['duration'],
                    'test_mode' => $this->testMode
                ]
            ];

            if ($this->testMode && isset($importInfo['test_stats'])) {
                $json['statistics']['test_stats'] = $importInfo['test_stats'];
            }

            // Почистваме JSON файла след кратко време (5 минути)
            // Това позволява на потребителя да види резултатите
            $this->scheduleCleanup($importId, 300);

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Finalize import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Планира почистване на import данни
     */
    private function scheduleCleanup($importId, $delaySeconds = 300)
    {
        // За простота, ще използваме file timestamp за cleanup
        // В production среда може да се използва cron job или queue система
        $cleanupFile = $this->importCacheDir . 'cleanup_' . $importId . '.txt';
        file_put_contents($cleanupFile, time() + $delaySeconds);
    }

    /**
     * Връща информация за прогреса на импорт
     */
    public function getImportProgress()
    {
        $json = [];

        try {
            if (!isset($_POST['import_id'])) {
                throw new \Exception('Липсва import_id параметър');
            }

            $importId = $_POST['import_id'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Изчисляваме процента на завършеност
            $progressPercent = 0;
            if ($importInfo['total_batches'] > 0) {
                $progressPercent = round(($importInfo['processed_batches'] / $importInfo['total_batches']) * 100, 2);
            }

            $json = [
                'success' => true,
                'import_id' => $importId,
                'status' => $importInfo['status'],
                'progress_percent' => $progressPercent,
                'processed_batches' => $importInfo['processed_batches'],
                'total_batches' => $importInfo['total_batches'],
                'processed_products' => $importInfo['processed_products'],
                'total_products' => $importInfo['total_products'],
                'added_products' => $importInfo['added_products'],
                'updated_products' => $importInfo['updated_products'],
                'errors' => $importInfo['errors'],
                'started_at' => $importInfo['started_at'],
                'last_updated' => $importInfo['last_updated'] ?? time(),
                'test_mode' => $importInfo['test_mode'] ?? false
            ];

            // Добавяме времева информация
            $currentTime = time();
            $elapsedTime = $currentTime - $importInfo['started_at'];
            $json['elapsed_time'] = $elapsedTime;
            $json['elapsed_time_formatted'] = $this->formatDuration($elapsedTime);

            // Изчисляваме приблизителното оставащо време
            if ($progressPercent > 0 && $progressPercent < 100) {
                $estimatedTotalTime = ($elapsedTime / $progressPercent) * 100;
                $remainingTime = $estimatedTotalTime - $elapsedTime;
                $json['estimated_remaining_time'] = max(0, $remainingTime);
                $json['estimated_remaining_time_formatted'] = $this->formatDuration(max(0, $remainingTime));
            }

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Get import progress error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Форматира продължителност в секунди към четим формат
     */
    private function formatDuration($seconds)
    {
        if ($seconds < 60) {
            return round($seconds) . ' сек';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . ' мин ' . round($remainingSeconds) . ' сек';
        } else {
            $hours = floor($seconds / 3600);
            $remainingMinutes = floor(($seconds % 3600) / 60);
            return $hours . ' ч ' . $remainingMinutes . ' мин';
        }
    }

    /**
     * Отменя текущ импорт
     */
    public function cancelImport()
    {
        $json = [];

        try {
            if (!isset($_POST['import_id'])) {
                throw new \Exception('Липсва import_id параметър');
            }

            $importId = $_POST['import_id'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Обновяваме статуса
            $importInfo['status'] = 'cancelled';
            $importInfo['cancelled_at'] = time();

            // Записваме обновените данни
            $this->saveImportData($importId, $importInfo);

            F()->log->developer("Импорт отменен: {$importId}", __FILE__, __LINE__);

            $json = [
                'success' => true,
                'message' => 'Импортът беше отменен успешно',
                'import_id' => $importId
            ];

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Cancel import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва batch от продукти
     */
    private function processBatchProducts($products, $format)
    {
        $result = [
            'processed' => 0,
            'added' => 0,
            'updated' => 0,
            'errors' => 0
        ];

        $productsToAdd = [];
        $productsToUpdate = [];

        // Предварително зареждане на съществуващите продукти ако не е направено
        if (empty($this->existingProducts)) {
            $this->loadExistingProducts();
        }

        // Разделяме продуктите на нови и за обновяване
        foreach ($products as $product) {
            $model = $product['model'] ?? '';

            if (empty($model)) {
                $result['errors']++;
                continue;
            }

            if (isset($this->existingProducts[$model])) {
                $product['product_id'] = $this->existingProducts[$model];
                $productsToUpdate[] = $product;
            } else {
                $productsToAdd[] = $product;
            }
        }

        // Обработка на порции
        if (!empty($productsToUpdate)) {
            $updated = $this->processBatchUpdate($productsToUpdate);
            $result['updated'] = $updated;
        }

        if (!empty($productsToAdd)) {
            $added = $this->processBatchInsert($productsToAdd);
            $result['added'] = $added;
        }

        $result['processed'] = $result['added'] + $result['updated'];

        return $result;
    }

    /**
     * Основен метод за показване на формата за импорт
     */
    public function execute()
    {
        $this->setTitle('Импорт на продукти');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_import');
    }

    /**
     * Подготвя данните за формата
     */
    public function prepareData()
    {
        // Конвертираме max_file_size в байтове за JavaScript
        $maxFileSizeBytes = $this->parseFileSize(ini_get('upload_max_filesize'));

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product'),
            'supported_formats' => $this->supportedFormats,
            'unavailable_formats' => $this->unavailableFormats,
            'format_requirements' => $this->formatRequirements,
            'available_formats' => array_filter($this->formatRequirements, function ($format) {
                return $format['available'];
            }),
            'max_file_size' => ini_get('upload_max_filesize'),
            'max_file_size_bytes' => $maxFileSizeBytes,
            'languages' => $this->languageMapping,
            'is_developer' => isDeveloper(),
            'large_import_threshold' => $this->largeImportThreshold
        ]);
    }

    /**
     * Парсира file size стринг към байтове
     */
    private function parseFileSize($size)
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }

    /**
     * Обработва качването и импорта на файл
     */
    public function processImport()
    {
        $json = [];

        try {
            // Валидация на заявката
            if (!$this->validateImportRequest()) {
                throw new \Exception('Невалидна заявка за импорт');
            }

            // Проверка за тестов режим
            $this->testMode = isset($_POST['test_mode']) && $_POST['test_mode'] == '1' && isDeveloper();

            if ($this->testMode) {
                F()->log->developer('=== ТЕСТОВ РЕЖИМ АКТИВИРАН ===', __FILE__, __LINE__);
                $this->testModeStats = [
                    'queries_saved' => 0,
                    'inserts_saved' => 0,
                    'updates_saved' => 0
                ];
            }

            $uploadedFile = $this->handleFileUpload();
            if (!$uploadedFile) {
                throw new \Exception('Грешка при качване на файла');
            }

            // Определяне на формата на файла
            $fileFormat = $this->detectFileFormat($uploadedFile);

            // Зареждане на съответния модел за обработка
            $importModel = $this->loadImportModel($fileFormat);

            // Парсиране на файла за получаване на продуктите
            $products = $importModel->parseFile($uploadedFile);

            if (empty($products)) {
                throw new \Exception('Няма валидни продукти за импорт в файла');
            }

            $productCount = count($products);
            F()->log->developer("Парсирани {$productCount} продукта от файла", __FILE__, __LINE__);

            // Проверяваме дали импортът е голям и трябва да се раздели
            if ($productCount > $this->largeImportThreshold) {
                // За големи импорти връщаме информация за batch обработка
                $this->initiateLargeImport($products, $fileFormat, $_POST);
                return; // initiateLargeImport() вече изпраща JSON response
            } else {


                F()->log->developer("Обработване на {$productCount} продукта директно", __FILE__, __LINE__);

                // За малки импорти обработваме директно
                $this->loadExistingProducts();
                $result = $this->processBatchProducts($products, $fileFormat);



                if ($this->testMode) {
                    $json['success'] = 'Тестовият импорт завърши успешно (няма реални промени в базата данни)';
                    $result['test_mode'] = true;
                    $result['test_stats'] = $this->testModeStats;
                } else {
                    $json['success'] = 'Импортът завърши успешно';
                }

                $json['statistics'] = $result;
            }

            // Изтриване на временния файл
            if (file_exists($uploadedFile)) {
                unlink($uploadedFile);
            }

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира заявката за импорт
     */
    private function validateImportRequest()
    {
        // Проверка дали има качен файл
        if (empty($_FILES['import_file']) || $_FILES['import_file']['error'] != UPLOAD_ERR_OK) {
            return false;
        }

        // Проверка за валиден формат
        $fileExtension = strtolower(pathinfo($_FILES['import_file']['name'], PATHINFO_EXTENSION));
        $validExtensions = ['csv', 'xml', 'xlsx', 'xls'];

        if (!in_array($fileExtension, $validExtensions)) {
            return false;
        }

        // Проверка дали форматът е поддържан
        $format = $this->detectFileFormat($_FILES['import_file']['name']);
        if (!in_array($format, $this->supportedFormats)) {
            return false;
        }

        return true;
    }

    /**
     * Обработва качването на файл
     */
    private function handleFileUpload()
    {
        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] != UPLOAD_ERR_OK) {
            return false;
        }

        $uploadedFile = $_FILES['import_file']['tmp_name'];
        $originalName = $_FILES['import_file']['name'];
        $fileExtension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));

        // Генерираме уникално име за файла
        $uniqueName = uniqid('import_', true) . '.' . $fileExtension;
        $destinationPath = DIR_CACHE . $uniqueName;

        // Преместваме файла в cache директорията
        if (!move_uploaded_file($uploadedFile, $destinationPath)) {
            F()->log->developer('Failed to move uploaded file to: ' . $destinationPath, __FILE__, __LINE__);
            return false;
        }

        F()->log->developer('File uploaded successfully: ' . $destinationPath, __FILE__, __LINE__);
        return $destinationPath;
    }

    /**
     * Зарежда модел за импорт според формата
     */
    private function loadImportModel($format)
    {
        $modelName = 'catalog/productimport' . $format;
        $this->loadModelAs($modelName, 'importModel');

        if (!isset($this->importModel)) {
            throw new \Exception('Не може да се зареди модел за импорт: ' . $format);
        }

        return $this->importModel;
    }

    /**
     * Зарежда съществуващите продукти за бързо търсене
     */
    private function loadExistingProducts()
    {
        $this->loadModelAs('catalog/product', 'productModel');
        $products = $this->productModel->getProducts(['sort' => 'p.product_id']);

        $this->existingProducts = [];
        foreach ($products as $product) {
            if (!empty($product['model'])) {
                $this->existingProducts[$product['model']] = $product['product_id'];
            }
        }

        F()->log->developer('Loaded ' . count($this->existingProducts) . ' existing products', __FILE__, __LINE__);
    }

    /**
     * Обработва batch от продукти за обновяване
     */
    private function processBatchUpdate($products)
    {
        $updatedCount = 0;

        foreach ($products as $product) {
            if ($this->testMode) {
                // В тестов режим само симулираме обновяването
                $this->testModeStats['queries_saved']++;
                $this->testModeStats['updates_saved']++;
                $updatedCount++;
                continue;
            }

            try {
                $this->productModel->updateProduct($product['product_id'], $product);
                $updatedCount++;
            } catch (\Exception $e) {
                F()->log->developer('Error updating product: ' . $e->getMessage(), __FILE__, __LINE__);
            }
        }

        return $updatedCount;
    }

    /**
     * Обработва batch от продукти за добавяне
     */
    private function processBatchInsert($products)
    {
        $addedCount = 0;

        foreach ($products as $product) {
            if ($this->testMode) {
                // В тестов режим само симулираме добавянето
                $this->testModeStats['queries_saved']++;
                $this->testModeStats['inserts_saved']++;
                $addedCount++;
                continue;
            }

            try {
                $productId = $this->productModel->addProduct($product);
                if ($productId) {
                    $this->existingProducts[$product['model']] = $productId;
                    $addedCount++;
                }
            } catch (\Exception $e) {
                F()->log->developer('Error adding product: ' . $e->getMessage(), __FILE__, __LINE__);
            }
        }

        return $addedCount;
    }

    /**
     * Връща категория ID по път
     */
    private function getCategoryIdByPath($path)
    {
        return $this->categoryCache[$path] ?? 0;
    }
}