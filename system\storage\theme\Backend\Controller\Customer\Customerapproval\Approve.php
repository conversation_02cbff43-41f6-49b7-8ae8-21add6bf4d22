<?php

namespace Theme25\Backend\Controller\Customer\Customerapproval;

class Approve extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява одобряването на клиент
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer_approval')) {
            $json['error'] = 'Нямате права за одобряване на клиенти!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            $type = $this->requestGet('type', '');

            if ($customer_id && in_array($type, ['customer', 'affiliate'])) {
                $this->load->model('customer/customer_approval');

                if ($type == 'customer') {
                    $this->model_customer_customer_approval->approveCustomer($customer_id);
                    $json['success'] = 'Клиентът беше успешно одобрен!';
                } elseif ($type == 'affiliate') {
                    $this->model_customer_customer_approval->approveAffiliate($customer_id);
                    $json['success'] = 'Партньорът беше успешно одобрен!';
                }
            } else {
                $json['error'] = 'Невалидни данни за одобрение!';
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
