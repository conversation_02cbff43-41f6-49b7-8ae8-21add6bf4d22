<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Reward extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Показва reward points на клиента
     */
    public function execute() {
        $customer_id = (int)$this->requestGet('customer_id', 0);
        
        if (!$customer_id) {
            return '';
        }

        $this->loadModelAs('customer/customer', 'customerModel');
        
        $rewards = $this->customerModel->getRewards($customer_id);
        
        // Изчисляваме общите точки
        $total_points = 0;
        foreach ($rewards as $reward) {
            $total_points += $reward['points'];
        }
        
        $data = [
            'rewards' => $rewards,
            'total_points' => $total_points,
            'customer_id' => $customer_id,
            'user_token' => $this->session->data['user_token']
        ];

        return $this->load->view('customer/customer_reward', $data);
    }

    /**
     * Добавя нови reward points за клиента
     */
    public function addreward() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за добавяне на reward points!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            $description = trim($this->requestPost('description', ''));
            $points = (int)$this->requestPost('points', 0);

            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } elseif (empty($description)) {
                $json['error'] = 'Описанието е задължително!';
            } elseif ($points == 0) {
                $json['error'] = 'Точките трябва да са различни от нула!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                
                $this->customerModel->addReward($customer_id, $description, $points);
                
                $json['success'] = 'Reward points са добавени успешно!';
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
