<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class AttributeAutocomplete extends \Theme25\ControllerSubMethods {

    /**
     * Автозавършване за атрибути
     */
    public function autocomplete($get) {
        $json = [];

        // Зареждане на модела за атрибути
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel'
        ]);

        // Получаване на параметрите от заявката
        $filter_name = $get['filter_name'] ?? '';
        $limit = (int)($get['limit'] ?? 10);

        // Ограничаваме лимита между 1 и 50
        $limit = max(1, min(50, $limit));

        // Подготвяне на данните за филтриране
        $filter_data = [
            'filter_name' => $filter_name,
            'start' => 0,
            'limit' => $limit
        ];

        // Ако няма филтър по име, зареждаме първите атрибути
        if (empty($filter_name)) {
            $filter_data['sort'] = 'ad.name';
            $filter_data['order'] = 'ASC';
        }

        // Зареждане на атрибутите
        $attributes = $this->attributeModel->getAttributes($filter_data);

        // Подготвяне на резултата
        foreach ($attributes as $attribute) {
            $json[] = [
                'attribute_id' => $attribute['attribute_id'],
                'name' => $attribute['name'],
                'attribute_group' => $attribute['attribute_group'] ?? ''
            ];
        }

        return $json;
    }


}
