<!-- Voucher Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Подаръчни ваучери</h1>
            <p class="text-gray-500 mt-1">Управление на всички подаръчни ваучери в магазина</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ add_url }}" 
               class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                <i class="ri-add-line mr-2"></i>
                Добави ваучер
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white border-b border-gray-200 px-6 py-3">
    <form id="filter-form" method="get" action="{{ filter_action_url }}">
        <!-- Скрито поле за user_token -->
        <input type="hidden" name="user_token" value="{{ user_token }}">
        <div class="flex flex-wrap items-center gap-4">
            <!-- Статус филтър -->
            <div class="w-full md:w-auto">
                <select name="filter_status"
                        class="w-full md:w-48 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
                    <option value="">Всички статуси</option>
                    {% if status_options %}
                        {% for status in status_options %}
                            <option value="{{ status.value }}" {% if status.value == filter_status %}selected{% endif %}>
                                {{ status.text }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>

            <!-- Общо търсене -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_search"
                       placeholder="Търсене по код, име или сума..."
                       value="{{ filter_search }}"
                       class="w-full md:w-64 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Код на ваучер -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_code"
                       placeholder="Код на ваучер"
                       value="{{ filter_code }}"
                       class="w-full md:w-32 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Име на подарител -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_from_name"
                       placeholder="Име на подарител"
                       value="{{ filter_from_name }}"
                       class="w-full md:w-40 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Име на получател -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_to_name"
                       placeholder="Име на получател"
                       value="{{ filter_to_name }}"
                       class="w-full md:w-40 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Сума -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_amount"
                       placeholder="Сума"
                       value="{{ filter_amount }}"
                       class="w-full md:w-32 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Дата -->
            <div class="w-full md:w-auto">
                <input type="date"
                       name="filter_date_added"
                       value="{{ filter_date_added }}"
                       class="w-full md:w-40 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Скрити полета за сортиране и пагинация -->
            <input type="hidden" name="sort" value="{{ sort }}">
            <input type="hidden" name="order" value="{{ order }}">
            <input type="hidden" name="page" value="1">

            <!-- Бутони -->
            <div class="w-full md:w-auto flex gap-2">
                <button type="submit"
                        class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm">
                    <i class="ri-search-line mr-1"></i>
                    Търси
                </button>

                {% if filter_status or filter_search or filter_code or filter_from_name or filter_to_name or filter_amount or filter_date_added %}
                <a href="{{ clear_filters_url }}"
                   class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center text-sm">
                    <i class="ri-close-line mr-1"></i>
                    Изчисти
                </a>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- Main Content Area -->
<div class="bg-white">
    <!-- Vouchers Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Код
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Подарител
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Получател
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Тема
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Сума
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Статус
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Дата
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Действия
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if vouchers %}
                    {% for voucher in vouchers %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" name="selected[]" value="{{ voucher.voucher_id }}" class="rounded border-gray-300 text-primary focus:ring-primary">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ voucher.code }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ voucher.from_name }}</div>
                            <div class="text-sm text-gray-500">{{ voucher.from_email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ voucher.to_name }}</div>
                            <div class="text-sm text-gray-500">{{ voucher.to_email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ voucher.theme }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ voucher.amount }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ voucher.status_class }}">
                                {{ voucher.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ voucher.date_added }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{{ voucher.edit_url }}" 
                                   class="text-primary hover:text-primary/80 transition-colors"
                                   title="Редактиране">
                                    <i class="ri-edit-line"></i>
                                </a>
                                <a href="{{ voucher.history_url }}" 
                                   class="text-blue-600 hover:text-blue-800 transition-colors"
                                   title="История">
                                    <i class="ri-history-line"></i>
                                </a>
                                <a href="{{ voucher.send_url }}" 
                                   class="text-green-600 hover:text-green-800 transition-colors"
                                   title="Изпрати">
                                    <i class="ri-mail-send-line"></i>
                                </a>
                                <a href="{{ voucher.delete_url }}" 
                                   class="text-red-600 hover:text-red-800 transition-colors"
                                   title="Изтриване"
                                   onclick="return confirm('Сигурни ли сте, че искате да изтриете този ваучер?')">
                                    <i class="ri-delete-bin-line"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="9" class="px-6 py-12 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <i class="ri-gift-line text-4xl text-gray-300 mb-4"></i>
                                <p class="text-lg font-medium">Няма намерени ваучери</p>
                                <p class="text-sm">Добавете първия ваучер или променете филтрите за търсене</p>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if pagination %}
    <div class="bg-white px-6 py-3 border-t border-gray-200">
        {{ pagination|raw }}
    </div>
    {% endif %}
</div>
