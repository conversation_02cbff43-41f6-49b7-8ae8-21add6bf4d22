<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за история на подаръчен ваучер
 */
class History extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява подготовката на данните за историята
     */
    public function execute() {
        $voucher_id = (int)$this->requestGet('voucher_id', 0);

        if (!$voucher_id) {
            $this->setError('Невалиден номер на ваучер');
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
            return;
        }

        try {
            $this->loadModelsAs([
                'sale/voucher' => 'voucherModel'
            ]);

            // Проверка дали ваучерът съществува
            $voucher_info = $this->voucherModel->getVoucher($voucher_id);
            if (!$voucher_info) {
                $this->setError('Ваучерът не е намерен');
                $this->redirectResponse($this->getAdminLink('sale/voucher'));
                return;
            }

            // Подготвяне на данните за историята
            $this->prepareHistoryData($voucher_id, $voucher_info);

        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на историята: ' . $e->getMessage());
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
        }
    }

    /**
     * Подготвя данните за историята на ваучера
     *
     * @param int $voucher_id
     * @param array $voucher_info
     */
    private function prepareHistoryData($voucher_id, $voucher_info) {
        // Получаване на параметри за пагинация
        $page = (int)$this->requestGet('page', 1);
        $limit = 10;
        $start = ($page - 1) * $limit;

        // Получаване на историята на ваучера
        $histories = $this->voucherModel->getVoucherHistories($voucher_id, $start, $limit);
        $total_histories = $this->voucherModel->getTotalVoucherHistories($voucher_id);

        // Форматиране на данните за историята
        $formatted_histories = [];
        foreach ($histories as $history) {
            $formatted_histories[] = [
                'order_id' => $history['order_id'],
                'customer' => $history['customer'] ?: 'Неизвестен клиент',
                'amount' => $this->formatCurrency($history['amount'], 'BGN', 1),
                'date_added' => date('d.m.Y H:i', strtotime($history['date_added'])),
                'order_url' => $this->getAdminLink('sale/order/info', 'order_id=' . $history['order_id'])
            ];
        }

        // Подготвяне на пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $total_histories;
        $pagination->page = $page;
        $pagination->limit = $limit;
        $pagination->url = $this->getAdminLink('sale/voucher/history', 'voucher_id=' . $voucher_id . '&page={page}');

        // Изчисляване на общата използвана сума от всички записи в историята
        $all_histories = $this->voucherModel->getVoucherHistories($voucher_id, 0, 1000);
        $total_used = 0;
        foreach ($all_histories as $history) {
            $total_used += (float)$history['amount'];
        }

        // Изчисляване на оставащата сума
        $remaining_amount = (float)$voucher_info['amount'] - $total_used;

        $this->setData([
            'voucher_info' => [
                'voucher_id' => $voucher_info['voucher_id'],
                'code' => $voucher_info['code'],
                'from_name' => $voucher_info['from_name'],
                'to_name' => $voucher_info['to_name'],
                'amount' => $this->formatCurrency($voucher_info['amount'], 'BGN', 1),
                'status' => $voucher_info['status'] ? 'Активен' : 'Неактивен',
                'date_added' => date('d.m.Y H:i', strtotime($voucher_info['date_added']))
            ],
            'histories' => $formatted_histories,
            'total_used' => $this->formatCurrency($total_used, 'BGN', 1),
            'remaining_amount' => $this->formatCurrency($remaining_amount, 'BGN', 1),
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)',
                ($page - 1) * $limit + 1,
                min($total_histories, $page * $limit),
                $total_histories,
                ceil($total_histories / $limit)
            ),
            'back_url' => $this->getAdminLink('sale/voucher'),
            'edit_url' => $this->getAdminLink('sale/voucher/edit', 'voucher_id=' . $voucher_id)
        ]);
    }
}
