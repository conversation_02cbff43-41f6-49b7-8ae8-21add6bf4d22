<?php

namespace Theme25\Backend\Controller\Catalog\Option;

class Edit extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'option-form.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Редактиране на опция');
        
        // Инициализиране на данните
        $this->initAdminData();
        
        $this->prepareOptionForm();
        
        $this->renderTemplateWithDataAndOutput('catalog/option_form');
    }
    
    /**
     * Подготвя формата за редактиране на опция
     */
    private function prepareOptionForm() {
        $option_id = (int)$this->requestGet('option_id', 0);
        
        if (!$option_id) {
            // Пренасочване към списъка ако няма ID
            $this->response->redirect($this->getAdminLink('catalog/option'));
            return;
        }

        $this->loadModelsAs([
            'catalog/option' => 'optionModel',
            'localisation/language' => 'languageModel'
        ]);

        // Зареждане на данните за опцията
        $option_info = $this->optionModel->getOption($option_id);
        
        if (!$option_info) {
            // Пренасочване към списъка ако опцията не съществува
            $this->response->redirect($this->getAdminLink('catalog/option'));
            return;
        }

        // Подготвяне на данните
        $this->prepareBasicData($option_info)
             ->prepareLanguageData($option_id)
             ->prepareOptionTypes()
             ->prepareOptionValues($option_id)
             ->prepareFormUrls($option_id);

        return $this;
    }

    /**
     * Подготвя основните данни за формата
     */
    private function prepareBasicData($option_info) {
        $this->setData([
            'option_id' => $option_info['option_id'],
            'sort_order' => $option_info['sort_order'],
            'type' => $option_info['type']
        ]);

        return $this;
    }

    /**
     * Подготвя данните за езиците
     */
    private function prepareLanguageData($option_id) {
        $languages = $this->languages;
        $this->setData('languages', $languages);

        // Зареждане на описанията за всички езици
        $option_descriptions = $this->optionModel->getOptionDescriptions($option_id);
        
        // Подготвяне на описанията за всички езици
        $option_description = [];
        foreach ($languages as $language) {
            $option_description[$language['language_id']] = [
                'name' => $option_descriptions[$language['language_id']]['name'] ?? ''
            ];
        }
        
        $this->setData('option_description', $option_description);

        return $this;
    }

    /**
     * Подготвя типовете опции
     */
    private function prepareOptionTypes() {
        $option_types = [
            'select' => 'Select',
            'radio' => 'Radio',
            'checkbox' => 'Checkbox',
            'text' => 'Text',
            'textarea' => 'Textarea',
            'file' => 'File',
            'date' => 'Date',
            'time' => 'Time',
            'datetime' => 'Date & Time'
        ];
        
        $this->setData('option_types', $option_types);

        return $this;
    }

    /**
     * Подготвя стойностите на опцията
     */
    private function prepareOptionValues($option_id) {
        $option_values = [];
        
        // Зареждаме стойностите само за опции които ги поддържат
        if (in_array($this->data['type'], ['select', 'radio', 'checkbox'])) {
            $values = $this->optionModel->getOptionValues($option_id);
            
            foreach ($values as $value) {
                $option_value_descriptions = [];
                foreach ($this->languages as $language) {
                    $option_value_descriptions[$language['language_id']] = [
                        'name' => $value['option_value_description'][$language['language_id']]['name'] ?? ''
                    ];
                }
                
                $option_values[] = [
                    'option_value_id' => $value['option_value_id'],
                    'option_value_description' => $option_value_descriptions,
                    'image' => $value['image'] ?? '',
                    'sort_order' => $value['sort_order'] ?? 0
                ];
            }
        }
        
        $this->setData('option_values', $option_values);

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls($option_id) {
        $this->setData([
            'action' => $this->getAdminLink('catalog/option/save'),
            'cancel' => $this->getAdminLink('catalog/option'),
            'back_url' => $this->getAdminLink('catalog/option'),
            'delete_url' => $this->getAdminLink('catalog/option/delete', ['option_id' => $option_id])
        ]);

        return $this;
    }

    /**
     * Валидира данните от формата
     */
    public function validateForm($data) {
        $errors = [];

        // Проверка за ID на опцията при редактиране
        if (empty($data['option_id']) || !is_numeric($data['option_id'])) {
            $errors['option_id'] = 'Невалиден ID на опция';
        }

        // Проверка за тип опция
        if (empty($data['type'])) {
            $errors['type'] = 'Моля, изберете тип опция';
        }

        // Проверка за описания на опцията
        if (empty($data['option_description']) || !is_array($data['option_description'])) {
            $errors['option_description'] = 'Липсват описания на опцията';
        } else {
            foreach ($data['option_description'] as $language_id => $description) {
                if (empty($description['name']) || strlen(trim($description['name'])) < 1) {
                    $errors['name'][$language_id] = 'Името на опцията е задължително';
                } elseif (strlen($description['name']) > 128) {
                    $errors['name'][$language_id] = 'Името на опцията не може да бъде по-дълго от 128 символа';
                }
            }
        }

        // Проверка за sort_order
        if (isset($data['sort_order']) && !is_numeric($data['sort_order'])) {
            $errors['sort_order'] = 'Подредбата трябва да бъде число';
        }

        // Проверка за стойности на опцията (за select, radio, checkbox)
        if (in_array($data['type'], ['select', 'radio', 'checkbox'])) {
            if (empty($data['option_value']) || !is_array($data['option_value'])) {
                $errors['option_value'] = 'Опциите от тип ' . $data['type'] . ' трябва да имат поне една стойност';
            } else {
                foreach ($data['option_value'] as $key => $option_value) {
                    if (empty($option_value['option_value_description']) || !is_array($option_value['option_value_description'])) {
                        $errors['option_value'][$key] = 'Липсват описания на стойността';
                        continue;
                    }
                    
                    $has_valid_name = false;
                    foreach ($option_value['option_value_description'] as $language_id => $description) {
                        if (!empty($description['name']) && strlen(trim($description['name'])) >= 1) {
                            $has_valid_name = true;
                            break;
                        }
                    }
                    
                    if (!$has_valid_name) {
                        $errors['option_value'][$key] = 'Стойността трябва да има име поне за един език';
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Зареждане на модели с псевдоними
     */
    private function loadModelsAs($models) {
        foreach ($models as $model => $alias) {
            $this->load->model($model);
            $this->{$alias} = $this->{'model_' . str_replace('/', '_', $model)};
        }
    }
}
