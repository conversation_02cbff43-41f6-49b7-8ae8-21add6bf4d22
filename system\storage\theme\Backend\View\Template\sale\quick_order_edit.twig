<!-- Quick Order Edit Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Редактиране на бърза поръчка #{{ order_id }}</h1>
            <p class="text-gray-500 mt-1">Променете статуса и коментара на поръчката</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2">
            {% if cancel_url %}
            <a href="{{ cancel_url }}" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-arrow-left-line"></i>
                </div>
                <span>Отказ</span>
            </a>
            {% endif %}
            <button type="submit"
                    form="edit-form"
                    class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-save-line"></i>
                </div>
                <span>Запази</span>
            </button>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <div class="max-w-2xl mx-auto">

        {% if error_warning %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="ri-error-warning-line text-red-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">{{ error_warning }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Редактиране на поръчка</h2>
            </div>
            
            <form id="edit-form" method="post" action="{{ action_url }}" class="p-6 space-y-6">
                
                <!-- Order Information (Read-only) -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-900 mb-4">Информация за поръчката</h3>

                    <!-- Basic Order Info -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                        <div>
                            <span class="text-gray-500">Номер:</span>
                            <span class="font-medium text-gray-900 ml-2">#{{ order_id }}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Дата:</span>
                            <span class="font-medium text-gray-900 ml-2">{{ date_added }}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Общо:</span>
                            <span class="font-medium text-gray-900 ml-2">{{ formatted_total }}</span>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    {% if customer_info %}
                    <div class="border-t border-gray-200 pt-4 mb-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Клиент</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">Име:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ customer_info.name }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Телефон:</span>
                                <span class="font-medium text-gray-900 ml-2">{{ customer_info.phone }}</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Product Information -->
                    {% if product_info %}
                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Продукт</h4>
                        <div class="flex items-start space-x-4">
                            {% if product_info.image %}
                            <div class="flex-shrink-0">
                                <img src="{{ product_info.image }}" alt="{{ product_info.name }}" class="w-16 h-16 object-cover rounded border">
                            </div>
                            {% endif %}
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h5 class="text-sm font-medium text-gray-900">{{ product_info.name }}</h5>
                                    {% if product_info.product_url %}
                                    <a href="{{ product_info.product_url }}" class="text-primary hover:text-primary/80 text-xs" title="Редактиране на продукт">
                                        <i class="ri-edit-line"></i>
                                    </a>
                                    {% endif %}
                                </div>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    {% if product_info.model %}
                                    <div>
                                        <span class="text-gray-500">Модел:</span>
                                        <span class="text-gray-900 ml-1">{{ product_info.model }}</span>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <span class="text-gray-500">Количество:</span>
                                        <span class="text-gray-900 ml-1">{{ product_info.quantity }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">Цена:</span>
                                        <span class="text-gray-900 ml-1">{{ product_info.price }}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">Общо:</span>
                                        <span class="text-gray-900 ml-1">{{ product_info.total }}</span>
                                    </div>
                                </div>
                                {% if product_info.has_options and product_info.option_name %}
                                <div class="mt-2 text-sm">
                                    <span class="text-gray-500">{{ product_info.option_name }}:</span>
                                    <span class="text-gray-900 ml-1">{{ product_info.option_value }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Status Selection -->
                <div>
                    <label for="status_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Статус на поръчката <span class="text-red-500">*</span>
                    </label>
                    <select id="status_id" 
                            name="status_id" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        {% for status in status_options %}
                        <option value="{{ status.status_id }}" {% if status.status_id == status_id %}selected{% endif %}>
                            {{ status.name }}
                        </option>
                        {% endfor %}
                    </select>
                    <p class="mt-1 text-sm text-gray-500">Изберете новия статус на поръчката</p>
                </div>

                <!-- Comment -->
                <div>
                    <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
                        Коментар
                    </label>
                    <textarea id="comment" 
                              name="comment" 
                              rows="4"
                              placeholder="Добавете коментар за поръчката..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical">{{ comment }}</textarea>
                    <p class="mt-1 text-sm text-gray-500">Опционален коментар за поръчката (максимум 1000 символа)</p>
                </div>



                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ cancel_url }}" 
                       class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Отказ
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                        Запази промените
                    </button>
                </div>
            </form>
        </div>
    </div>
</main>
