<?php

namespace Theme25\Backend\Controller\Catalog\Manufacturer;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'manufacturer-listing.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Производители');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/manufacturer');
    }

    /**
     * Подготвя данните за листването на производители
     */
    private function prepareData() {
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);

        // Подготвяне на филтрите
        $this->prepareFilters()
             ->prepareManufacturersList()
             ->preparePagination()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя филтрите за търсене
     */
    private function prepareFilters() {
        $filter_name = $this->requestGet('filter_name', '');
        $filter_sort_order = $this->requestGet('filter_sort_order', '');
        $sort = $this->requestGet('sort', 'name');
        $order = $this->requestGet('order', 'ASC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'filter_name' => $filter_name,
            'filter_sort_order' => $filter_sort_order,
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        return $this;
    }

    /**
     * Подготвя списъка с производители
     */
    private function prepareManufacturersList() {
        $filter_data = [
            'filter_name' => $this->data['filter_name'],
            'filter_sort_order' => $this->data['filter_sort_order'],
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->config->get('config_limit_admin'),
            'limit' => $this->config->get('config_limit_admin')
        ];

        $manufacturers = $this->manufacturerModel->getManufacturers($filter_data);
        $manufacturer_total = $this->manufacturerModel->getTotalManufacturers($filter_data);

        $results = [];

        foreach ($manufacturers as $manufacturer) {
            $results[] = [
                'manufacturer_id' => $manufacturer['manufacturer_id'],
                'name' => $manufacturer['name'],
                'sort_order' => $manufacturer['sort_order'],
                'image' => $manufacturer['image'] ? ThemeData()->getImageServerUrl() . $manufacturer['image'] : '',
                'edit' => $this->getAdminLink('catalog/manufacturer/edit', 'manufacturer_id=' . $manufacturer['manufacturer_id']),
                'delete' => $this->getAdminLink('catalog/manufacturer/delete', 'manufacturer_id=' . $manufacturer['manufacturer_id'])
            ];
        }

        $this->setData([
            'manufacturers' => $results,
            'manufacturer_total' => $manufacturer_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $url = '';

        if (isset($this->data['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->data['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->data['filter_sort_order'])) {
            $url .= '&filter_sort_order=' . $this->data['filter_sort_order'];
        }

        if (isset($this->data['sort'])) {
            $url .= '&sort=' . $this->data['sort'];
        }

        if (isset($this->data['order'])) {
            $url .= '&order=' . $this->data['order'];
        }

        $pagination = new \Pagination();
        $pagination->total = $this->data['manufacturer_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->getAdminLink('catalog/manufacturer', $url . '&page={page}');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf($this->getLanguageText('text_pagination'), 
                ($this->data['manufacturer_total']) ? (($this->data['page'] - 1) * $this->config->get('config_limit_admin')) + 1 : 0, 
                ((($this->data['page'] - 1) * $this->config->get('config_limit_admin')) > ($this->data['manufacturer_total'] - $this->config->get('config_limit_admin'))) ? $this->data['manufacturer_total'] : ((($this->data['page'] - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), 
                $this->data['manufacturer_total'], 
                ceil($this->data['manufacturer_total'] / $this->config->get('config_limit_admin'))
            )
        ]);

        return $this;
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        $url = '';

        if (isset($this->data['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->data['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->data['filter_sort_order'])) {
            $url .= '&filter_sort_order=' . $this->data['filter_sort_order'];
        }

        if (isset($this->data['page'])) {
            $url .= '&page=' . $this->data['page'];
        }

        $sort_url = '';
        if ($this->data['order'] == 'ASC') {
            $sort_url .= '&order=DESC';
        } else {
            $sort_url .= '&order=ASC';
        }

        $this->setData([
            'add' => $this->getAdminLink('catalog/manufacturer/add'),
            'delete' => $this->getAdminLink('catalog/manufacturer/delete'),
            'sort_name' => $this->getAdminLink('catalog/manufacturer', $url . '&sort=name' . $sort_url),
            'sort_sort_order' => $this->getAdminLink('catalog/manufacturer', $url . '&sort=sort_order' . $sort_url)
        ]);

        return $this;
    }

    /**
     * AJAX търсене за производители
     */
    public function ajaxSearch() {
        $json = [];

        if ($this->requestGet('filter_name')) {
            $this->loadModelsAs([
                'catalog/manufacturer' => 'manufacturerModel'
            ]);

            $filter_data = [
                'filter_name' => $this->requestGet('filter_name'),
                'start' => 0,
                'limit' => 10
            ];

            $results = $this->manufacturerModel->getManufacturers($filter_data);

            foreach ($results as $result) {
                $json[] = [
                    'manufacturer_id' => $result['manufacturer_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                    'image' => $result['image'] ? ThemeData()->getImageServerUrl() . $result['image'] : ''
                ];
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
