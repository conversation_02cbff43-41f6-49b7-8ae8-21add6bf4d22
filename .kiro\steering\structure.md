# Rakla.bg - Структура на проекта

## Организация на директориите

### Основна структура
```
/
├── admin/                  # Оригинални OpenCart admin файлове
├── catalog/                # Оригинални OpenCart frontend файлове
├── system/                 # Системни файлове
│   ├── storage/            # Директория за съхранение
│   │   └── theme/          # Персонализирана имплементация на тема
│   │       ├── Backend/    # Персонализации на админ панела
│   │       │   ├── Controller/  # Backend контролери
│   │       │   ├── Model/       # Backend модели
│   │       │   └── View/        # Backend изгледи
│   │       ├── Frontend/   # Frontend персонализации
│   │       │   ├── Controller/  # Frontend контролери
│   │       │   ├── Model/       # Frontend модели
│   │       │   └── View/        # Frontend изгледи
│   │       ├── Controller.php   # Разширение на базовия контролер
│   │       ├── ControllerSubMethods.php  # Поддръжка на подконтролери
│   │       ├── Data.php         # Помощни програми за данни
│   │       ├── EnvLoader.php    # Конфигурация на средата
│   │       ├── SecondDB.php     # Поддръжка на двойна база данни
│   │       ├── ThemeStyles.php  # CSS константи
│   │       └── ConfigManager.php # Управление на конфигурацията
├── image/                  # Съхранение на изображения
└── Work/                   # Файлове за разработка и документация - да не се търси информация там
```

## Ключови архитектурни компоненти

### Структура на контролерите
Проектът използва dispatcher архитектура с основни контролери, делегиращи към подконтролери:

```
Backend/Controller/Catalog/Product.php         # Основен dispatcher контролер
└── Backend/Controller/Catalog/Product/        # Подконтролери
    ├── Index.php                              # Функционалност за листване
    ├── Edit.php                               # Функционалност за редактиране
    ├── Import.php                             # Функционалност за импортиране
    └── ...                                    # Други специализирани контролери
```

### Структура на изгледите
Изгледите са организирани в подобна йерархия:

```
Backend/View/Template/
├── catalog/
│   ├── product.tpl                # Основен шаблон за листване на продукти
│   ├── product_form.tpl           # Шаблон за формуляр за редактиране на продукт
│   └── product_import.tpl         # Шаблон за импортиране на продукт
└── common/
    ├── header.tpl                 # Общ хедър
    ├── footer.tpl                 # Общ футър
    └── layout.tpl                 # Основен шаблон за оформление
```

### Структура на моделите
Моделите следват същия организационен модел:

```
Backend/Model/
└── Catalog/
    ├── Product.php                # Основен модел на продукта
    ├── Productimportcsv.php       # CSV модел за импортиране
    ├── Productimportxlsx.php      # XLSX модел за импортиране
    └── Productimportxml.php       # XML модел за импортиране
```

## Конвенции за именуване

### Файлове и директории
- **Контролери**: PascalCase.php (напр., `Product.php`, `Import.php`)
- **Подконтролери**: PascalCase.php (напр., `Index.php`, `Edit.php`)
- **Директории**: PascalCase/ (напр., `Backend/`, `Controller/`)
- **Шаблони**: lowercase_with_underscores.twig (напр., `product_form.twig`)

### Класове и методи
- **Класове**: PascalCase (напр., `class Product`, `class Import`)
- **Публични методи**: camelCase (напр., `prepareData()`, `loadContents()`)
- **Частни методи**: camelCase (напр., `loadCategoryPaths()`)

### Пространства от имена
- **Формат**: `Theme25\{Section}\{Type}\{Module}\{Controller}`
- **Пример**: `Theme25\Backend\Controller\Catalog\Product\Import`

## Специални файлове и директории

### Документационни файлове
Хранилището съдържа множество markdown файлове, документиращи различни аспекти на системата:
- `project-conventions.md`: Архитектура и стандарти за кодиране
- `*_SUMMARY.md` файлове: Документация на специфични имплементации и поправки
