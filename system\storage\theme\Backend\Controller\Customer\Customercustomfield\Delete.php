<?php

namespace Theme25\Backend\Controller\Customer\Customercustomfield;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява изтриването на персонализирано поле/полета
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/custom_field')) {
            $json['error'] = 'Нямате права за изтриване на персонализирани полета!';
        } else {
            $this->load->model('customer/custom_field');

            if (isset($this->request->post['selected']) && is_array($this->request->post['selected'])) {
                // Масово изтриване
                $deleted_count = 0;

                foreach ($this->request->post['selected'] as $custom_field_id) {
                    $custom_field_id = (int)$custom_field_id;
                    
                    if ($custom_field_id) {
                        $this->model_customer_custom_field->deleteCustomField($custom_field_id);
                        $deleted_count++;
                    }
                }

                if ($deleted_count > 0) {
                    $json['success'] = sprintf('Успешно изтрити %d персонализирани полета!', $deleted_count);
                } else {
                    $json['error'] = 'Не са избрани персонализирани полета за изтриване!';
                }
            } else {
                // Единично изтриване
                $custom_field_id = (int)$this->requestGet('custom_field_id', 0);

                if ($custom_field_id) {
                    $custom_field_info = $this->model_customer_custom_field->getCustomField($custom_field_id);
                    $this->model_customer_custom_field->deleteCustomField($custom_field_id);
                    
                    if ($custom_field_info) {
                        $json['success'] = sprintf('Персонализираното поле "%s" беше успешно изтрито!', 
                            $custom_field_info['name']);
                    } else {
                        $json['success'] = 'Персонализираното поле беше успешно изтрито!';
                    }
                } else {
                    $json['error'] = 'Невалиден ID на персонализирано поле!';
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
