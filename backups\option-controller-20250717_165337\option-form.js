/**
 * Option Form Module
 * Управление на формата за опции - интегриран в BackendModule
 */
(function() {
    'use strict';

    // Разширяване на BackendModule с функционалност за форми на опции
    Object.assign(BackendModule, {
        
        optionForm: {
            config: {
                saveUrl: 'index.php?route=catalog/option/save',
                userToken: ''
            },

            /**
             * Инициализация на формата за опции
             */
            init: function() {
                console.log('Initializing option form...');
                this.config.userToken = BackendModule.config.userToken;
                this.bindEvents();
                this.initializeComponents();
            },

            /**
             * Свързване на събития
             */
            bindEvents: function() {
                this.bindFormEvents();
                this.bindValidationEvents();
                this.bindLanguageTabEvents();
                this.bindOptionTypeEvents();
                this.bindOptionValueEvents();
                this.bindSortOrderAutoFill();
            },

            /**
             * Свързване на събития за формата
             */
            bindFormEvents: function() {
                const form = document.getElementById('option-form');
                const self = this; // Запазване на правилната референция
                
                if (form) {
                    form.addEventListener('submit', (e) => {
                        e.preventDefault();
                        self.saveOption();
                    });
                }

                // Автоматично запазване при промяна на полетата
                const autoSaveFields = form?.querySelectorAll('input[name*="option_description"], select[name="type"], input[name="sort_order"]');
                autoSaveFields?.forEach(field => {
                    field.addEventListener('blur', () => {
                        self.validateSpecificField(field);
                    });
                });
            },

            /**
             * Свързване на събития за валидация
             */
            bindValidationEvents: function() {
                const nameInputs = document.querySelectorAll('input[name*="[name]"]');
                const self = this; // Запазване на правилната референция
                
                nameInputs.forEach(input => {
                    input.addEventListener('input', () => {
                        self.validateNameField(input);
                    });
                    
                    input.addEventListener('blur', () => {
                        self.validateNameField(input);
                    });
                });

                const sortOrderInput = document.querySelector('input[name="sort_order"]');
                if (sortOrderInput) {
                    sortOrderInput.addEventListener('input', () => {
                        self.validateSortOrder(sortOrderInput);
                    });
                }
            },

            /**
             * Свързване на събития за езиковите табове
             */
            bindLanguageTabEvents: function() {
                const languageTabs = document.querySelectorAll('.language-tab');
                const self = this; // Запазване на правилната референция
                
                languageTabs.forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        self.switchLanguageTab(tab);
                    });
                });
            },

            /**
             * Свързване на събития за типа опция
             */
            bindOptionTypeEvents: function() {
                const typeSelect = document.querySelector('select[name="type"]');
                const self = this;
                
                if (typeSelect) {
                    typeSelect.addEventListener('change', () => {
                        self.handleOptionTypeChange(typeSelect.value);
                    });
                    
                    // Инициализация при зареждане
                    this.handleOptionTypeChange(typeSelect.value);
                }
            },

            /**
             * Свързване на събития за стойностите на опцията
             */
            bindOptionValueEvents: function() {
                const addValueBtn = document.getElementById('add-option-value');
                const self = this;
                
                if (addValueBtn) {
                    addValueBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        self.addOptionValue();
                    });
                }

                // Свързване на съществуващите стойности
                this.bindExistingOptionValues();
            },

            /**
             * Свързване на автоматично попълване на sort_order
             */
            bindSortOrderAutoFill: function() {
                const typeSelect = document.querySelector('select[name="type"]');
                const sortOrderInput = document.querySelector('input[name="sort_order"]');
                const optionIdInput = document.querySelector('input[name="option_id"]');
                
                // Проверка дали е форма за добавяне на нова опция
                const isNewOption = !optionIdInput || optionIdInput.value === '0' || optionIdInput.value === '';
                
                if (!typeSelect || !sortOrderInput || !isNewOption) {
                    return;
                }

                console.log('Setting up sort_order auto-fill for new option');

                const self = this; // Запазване на правилната референция
                let debounceTimer;

                typeSelect.addEventListener('change', () => {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        self.autoFillSortOrder(typeSelect, sortOrderInput);
                    }, 200);
                });
            },

            /**
             * Инициализация на компоненти
             */
            initializeComponents: function() {
                this.initializeLanguageTabs();
                this.initializeTooltips();
                this.validateAllFields();
            },

            /**
             * Инициализация на езиковите табове
             */
            initializeLanguageTabs: function() {
                const firstTab = document.querySelector('.language-tab');
                if (firstTab) {
                    this.switchLanguageTab(firstTab);
                }
            },

            /**
             * Инициализация на tooltips
             */
            initializeTooltips: function() {
                const tooltipElements = document.querySelectorAll('[data-tooltip]');
                tooltipElements.forEach(element => {
                    // Инициализация на tooltip функционалност
                    element.addEventListener('mouseenter', () => {
                        this.showTooltip(element);
                    });
                    
                    element.addEventListener('mouseleave', () => {
                        this.hideTooltip(element);
                    });
                });
            },

            /**
             * Превключване на езиков таб
             */
            switchLanguageTab: function(tab) {
                const languageId = tab.dataset.languageId;
                
                // Деактивиране на всички табове
                document.querySelectorAll('.language-tab').forEach(t => {
                    t.classList.remove('active', 'bg-blue-500', 'text-white');
                    t.classList.add('bg-gray-200', 'text-gray-700');
                });
                
                // Активиране на избрания таб
                tab.classList.add('active', 'bg-blue-500', 'text-white');
                tab.classList.remove('bg-gray-200', 'text-gray-700');
                
                // Скриване на всички езикови панели
                document.querySelectorAll('.language-panel').forEach(panel => {
                    panel.classList.add('hidden');
                });
                
                // Показване на избрания панел
                const targetPanel = document.getElementById(`language-${languageId}`);
                if (targetPanel) {
                    targetPanel.classList.remove('hidden');
                }
            },

            /**
             * Обработка на промяна в типа опция
             */
            handleOptionTypeChange: function(optionType) {
                const optionValuesSection = document.getElementById('option-values-section');
                
                if (!optionValuesSection) return;

                // Показване/скриване на секцията за стойности
                if (['select', 'radio', 'checkbox'].includes(optionType)) {
                    optionValuesSection.classList.remove('hidden');
                } else {
                    optionValuesSection.classList.add('hidden');
                }
            },

            /**
             * Добавяне на нова стойност на опцията
             */
            addOptionValue: function() {
                const container = document.getElementById('option-values-container');
                if (!container) return;

                const valueIndex = container.children.length;
                const languages = this.getLanguages();
                
                const valueHtml = this.generateOptionValueHtml(valueIndex, languages);
                
                const wrapper = document.createElement('div');
                wrapper.innerHTML = valueHtml;
                const valueElement = wrapper.firstElementChild;
                
                container.appendChild(valueElement);
                
                // Свързване на събития за новата стойност
                this.bindOptionValueEvents(valueElement);
            },

            /**
             * Генериране на HTML за стойност на опция
             */
            generateOptionValueHtml: function(index, languages) {
                let languageInputs = '';
                
                languages.forEach(language => {
                    languageInputs += `
                        <div class="language-panel" id="value-${index}-language-${language.language_id}">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                Име (${language.name})
                            </label>
                            <input type="text" 
                                   name="option_value[${index}][option_value_description][${language.language_id}][name]" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Въведете име на стойността">
                        </div>
                    `;
                });

                return `
                    <div class="option-value-item bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="text-sm font-medium text-gray-900">Стойност ${index + 1}</h4>
                            <button type="button" class="remove-value-btn text-red-600 hover:text-red-800">
                                <i class="ri-delete-bin-line"></i> Премахни
                            </button>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                ${languageInputs}
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Изображение</label>
                                    <input type="text" 
                                           name="option_value[${index}][image]" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="Път към изображение">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Подредба</label>
                                    <input type="number" 
                                           name="option_value[${index}][sort_order]" 
                                           value="0"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                        
                        <input type="hidden" name="option_value[${index}][option_value_id]" value="0">
                    </div>
                `;
            },

            /**
             * Свързване на събития за съществуващи стойности
             */
            bindExistingOptionValues: function() {
                const removeButtons = document.querySelectorAll('.remove-value-btn');
                const self = this;
                
                removeButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.preventDefault();
                        self.removeOptionValue(btn);
                    });
                });
            },

            /**
             * Премахване на стойност на опция
             */
            removeOptionValue: function(button) {
                const valueItem = button.closest('.option-value-item');
                if (valueItem && confirm('Сигурни ли сте, че искате да премахнете тази стойност?')) {
                    valueItem.remove();
                    this.reindexOptionValues();
                }
            },

            /**
             * Преиндексиране на стойностите на опцията
             */
            reindexOptionValues: function() {
                const valueItems = document.querySelectorAll('.option-value-item');
                
                valueItems.forEach((item, index) => {
                    // Актуализиране на заглавието
                    const title = item.querySelector('h4');
                    if (title) {
                        title.textContent = `Стойност ${index + 1}`;
                    }
                    
                    // Актуализиране на name атрибутите на полетата
                    const inputs = item.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        if (input.name) {
                            input.name = input.name.replace(/\[\d+\]/, `[${index}]`);
                        }
                    });
                });
            },

            /**
             * Получаване на списъка с езици
             */
            getLanguages: function() {
                // Опит за получаване на езиците от глобална променлива или DOM
                if (window.languages) {
                    return window.languages;
                }
                
                // Fallback - извличане от съществуващите табове
                const languageTabs = document.querySelectorAll('.language-tab');
                const languages = [];
                
                languageTabs.forEach(tab => {
                    languages.push({
                        language_id: tab.dataset.languageId,
                        name: tab.textContent.trim(),
                        code: tab.dataset.languageCode || 'bg'
                    });
                });
                
                return languages;
            },

            /**
             * Автоматично попълване на sort_order стойността
             */
            autoFillSortOrder: function(typeSelect, sortOrderInput) {
                const optionType = typeSelect.value;
                
                if (!optionType || optionType === '') {
                    console.log('No option type selected, clearing sort_order');
                    sortOrderInput.value = '';
                    return;
                }

                console.log('Auto-filling sort_order for type:', optionType);

                // Показване на loading индикатор
                this.showSortOrderLoading(sortOrderInput);

                // AJAX заявка за получаване на следващата стойност
                const params = new URLSearchParams();
                params.append('user_token', BackendModule.config.userToken);
                params.append('option_type', optionType);

                fetch(`index.php?route=catalog/option/getNextSortOrder&${params.toString()}`)
                    .then(response => response.json())
                    .then(data => {
                        this.hideSortOrderLoading(sortOrderInput);
                        
                        if (data.success && data.next_sort_order) {
                            console.log('Setting sort_order to:', data.next_sort_order);
                            sortOrderInput.value = data.next_sort_order;
                            
                            // Премахване на грешки ако има такива
                            this.clearFieldError(sortOrderInput);
                        } else if (data.error) {
                            console.warn('Error getting next sort_order:', data.error);
                            sortOrderInput.value = '';
                        } else {
                            console.warn('Unexpected response:', data);
                            sortOrderInput.value = '1'; // Fallback стойност
                        }
                    })
                    .catch(error => {
                        this.hideSortOrderLoading(sortOrderInput);
                        console.warn('AJAX error getting next sort_order:', error);
                        sortOrderInput.value = ''; // Оставяме полето празно при грешка
                    });
            },

            /**
             * Показване на loading индикатор в sort_order полето
             */
            showSortOrderLoading: function(input) {
                input.style.backgroundImage = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23666\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M21 12a9 9 0 11-6.219-8.56\'/%3E%3C/svg%3E")';
                input.style.backgroundRepeat = 'no-repeat';
                input.style.backgroundPosition = 'right 8px center';
                input.style.backgroundSize = '16px 16px';
                input.style.paddingRight = '32px';
                input.disabled = true;
                input.placeholder = 'Зареждане...';
            },

            /**
             * Скриване на loading индикатор в sort_order полето
             */
            hideSortOrderLoading: function(input) {
                input.style.backgroundImage = '';
                input.style.backgroundRepeat = '';
                input.style.backgroundPosition = '';
                input.style.backgroundSize = '';
                input.style.paddingRight = '';
                input.disabled = false;
                input.placeholder = '0';
            },

            /**
             * Валидация на конкретно поле според типа му
             */
            validateSpecificField: function(field) {
                const fieldName = field.name;

                if (fieldName.includes('option_description') && fieldName.includes('[name]')) {
                    // Поле за име на опция
                    return this.validateNameField(field);
                } else if (fieldName === 'sort_order') {
                    // Поле за подредба
                    return this.validateSortOrder(field);
                } else if (fieldName === 'type') {
                    // Поле за тип опция - основна валидация
                    this.clearFieldError(field);
                    if (!field.value || field.value === '') {
                        this.showFieldError(field, 'Моля, изберете тип опция');
                        return false;
                    }
                    return true;
                }

                // За други полета - само изчистваме грешките
                this.clearFieldError(field);
                return true;
            },

            /**
             * Валидация на поле за име
             */
            validateNameField: function(input) {
                const value = input.value.trim();

                // Премахване на предишни грешки
                this.clearFieldError(input);

                if (value.length === 0) {
                    this.showFieldError(input, 'Името на опцията е задължително');
                    return false;
                } else if (value.length > 128) {
                    this.showFieldError(input, 'Името на опцията не може да бъде по-дълго от 128 символа');
                    return false;
                }

                return true;
            },

            /**
             * Валидация на полето за подредба
             */
            validateSortOrder: function(input) {
                const value = input.value.trim();

                this.clearFieldError(input);

                if (value !== '' && !this.isNumeric(value)) {
                    this.showFieldError(input, 'Подредбата трябва да бъде число');
                    return false;
                }

                return true;
            },

            /**
             * Валидация на всички полета
             */
            validateAllFields: function() {
                let isValid = true;

                // Валидация на имената
                const nameInputs = document.querySelectorAll('input[name*="[name]"]');
                nameInputs.forEach(input => {
                    if (!this.validateNameField(input)) {
                        isValid = false;
                    }
                });

                // Валидация на типа опция
                const typeSelect = document.querySelector('select[name="type"]');
                if (typeSelect && !this.validateSpecificField(typeSelect)) {
                    isValid = false;
                }

                // Валидация на подредбата
                const sortOrderInput = document.querySelector('input[name="sort_order"]');
                if (sortOrderInput && !this.validateSortOrder(sortOrderInput)) {
                    isValid = false;
                }

                // Валидация на стойностите на опцията
                if (!this.validateOptionValues()) {
                    isValid = false;
                }

                return isValid;
            },

            /**
             * Валидация на стойностите на опцията
             */
            validateOptionValues: function() {
                const typeSelect = document.querySelector('select[name="type"]');
                if (!typeSelect) return true;

                const optionType = typeSelect.value;

                // За типове които изискват стойности
                if (['select', 'radio', 'checkbox'].includes(optionType)) {
                    const valueItems = document.querySelectorAll('.option-value-item');

                    if (valueItems.length === 0) {
                        this.showGeneralError('Опциите от тип ' + optionType + ' трябва да имат поне една стойност');
                        return false;
                    }

                    let hasValidValue = false;
                    valueItems.forEach(item => {
                        const nameInputs = item.querySelectorAll('input[name*="[name]"]');
                        nameInputs.forEach(input => {
                            if (input.value.trim().length > 0) {
                                hasValidValue = true;
                            }
                        });
                    });

                    if (!hasValidValue) {
                        this.showGeneralError('Трябва да въведете име поне за една стойност');
                        return false;
                    }
                }

                return true;
            },

            /**
             * Проверка дали стойността е число
             */
            isNumeric: function(value) {
                return !isNaN(parseFloat(value)) && isFinite(value);
            },

            /**
             * Изчистване на грешка от поле
             */
            clearFieldError: function(field) {
                // Премахване на error класове
                field.classList.remove('border-red-500', 'border-red-300');
                field.classList.add('border-gray-300');

                // Премахване на error съобщение
                const errorElement = field.parentNode.querySelector('.field-error');
                if (errorElement) {
                    errorElement.remove();
                }

                // Премахване на error икона
                const errorIcon = field.parentNode.querySelector('.error-icon');
                if (errorIcon) {
                    errorIcon.remove();
                }
            },

            /**
             * Показване на грешка за поле
             */
            showFieldError: function(field, message) {
                // Добавяне на error класове
                field.classList.remove('border-gray-300');
                field.classList.add('border-red-500');

                // Премахване на съществуващо error съобщение
                this.clearFieldError(field);

                // Добавяне на ново error съобщение
                const errorElement = document.createElement('div');
                errorElement.className = 'field-error text-red-500 text-sm mt-1';
                errorElement.textContent = message;

                field.parentNode.appendChild(errorElement);

                // Добавяне на error икона
                const errorIcon = document.createElement('div');
                errorIcon.className = 'error-icon absolute right-2 top-1/2 transform -translate-y-1/2 text-red-500';
                errorIcon.innerHTML = '<i class="ri-error-warning-line"></i>';

                if (field.parentNode.style.position !== 'relative') {
                    field.parentNode.style.position = 'relative';
                }

                field.parentNode.appendChild(errorIcon);
            },

            /**
             * Показване на обща грешка
             */
            showGeneralError: function(message) {
                // Премахване на съществуващи общи грешки
                const existingError = document.querySelector('.general-error');
                if (existingError) {
                    existingError.remove();
                }

                // Създаване на нова обща грешка
                const errorElement = document.createElement('div');
                errorElement.className = 'general-error bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
                errorElement.innerHTML = `
                    <div class="flex items-center">
                        <i class="ri-error-warning-line mr-2"></i>
                        <span>${message}</span>
                    </div>
                `;

                // Добавяне в началото на формата
                const form = document.getElementById('option-form');
                if (form) {
                    form.insertBefore(errorElement, form.firstChild);
                }
            },

            /**
             * Запазване на опция
             */
            saveOption: function() {
                console.log('Saving option...');

                // Валидация преди запазване
                if (!this.validateAllFields()) {
                    console.log('Validation failed');
                    return;
                }

                // Показване на loading състояние
                this.showLoading();

                // Подготвяне на данните за изпращане
                const form = document.getElementById('option-form');
                const formData = new FormData(form);
                formData.append('user_token', this.config.userToken);

                // AJAX заявка за запазване
                fetch(this.config.saveUrl, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    this.hideLoading();

                    if (data.success) {
                        console.log('Option saved successfully');
                        BackendModule.showAlert('success', data.success);

                        // Пренасочване към списъка или към редактиране
                        if (data.redirect) {
                            setTimeout(() => {
                                window.location.href = data.redirect;
                            }, 1500);
                        } else if (data.option_id) {
                            // Актуализиране на формата за редактиране
                            const optionIdInput = document.querySelector('input[name="option_id"]');
                            if (optionIdInput) {
                                optionIdInput.value = data.option_id;
                            }
                        }
                    } else if (data.errors) {
                        console.log('Validation errors:', data.errors);
                        this.displayValidationErrors(data.errors);
                    } else if (data.error) {
                        console.log('Save error:', data.error);
                        BackendModule.showAlert('error', data.error);
                    }
                })
                .catch(error => {
                    this.hideLoading();
                    console.error('AJAX error:', error);
                    BackendModule.showAlert('error', 'Възникна грешка при запазване на опцията');
                });
            },

            /**
             * Показване на validation грешки
             */
            displayValidationErrors: function(errors) {
                // Изчистване на съществуващи грешки
                document.querySelectorAll('.field-error, .general-error').forEach(el => el.remove());

                // Показване на грешки за конкретни полета
                Object.keys(errors).forEach(fieldName => {
                    if (fieldName === 'name' && typeof errors[fieldName] === 'object') {
                        // Грешки за имена по езици
                        Object.keys(errors[fieldName]).forEach(languageId => {
                            const field = document.querySelector(`input[name="option_description[${languageId}][name]"]`);
                            if (field) {
                                this.showFieldError(field, errors[fieldName][languageId]);
                            }
                        });
                    } else {
                        // Други грешки
                        const field = document.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            this.showFieldError(field, errors[fieldName]);
                        } else {
                            // Обща грешка ако няма конкретно поле
                            this.showGeneralError(errors[fieldName]);
                        }
                    }
                });
            },

            /**
             * Показване на loading състояние
             */
            showLoading: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }
            },

            /**
             * Скриване на loading състояние
             */
            hideLoading: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                }
            },

            /**
             * Показване на tooltip
             */
            showTooltip: function(element) {
                const tooltipText = element.dataset.tooltip;
                if (!tooltipText) return;

                // Премахване на съществуващи tooltips
                document.querySelectorAll('.custom-tooltip').forEach(tip => tip.remove());

                // Създаване на нов tooltip
                const tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip absolute z-50 bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg';
                tooltip.textContent = tooltipText;

                // Позициониране на tooltip
                const rect = element.getBoundingClientRect();
                tooltip.style.left = rect.left + 'px';
                tooltip.style.top = (rect.top - 30) + 'px';

                document.body.appendChild(tooltip);
            },

            /**
             * Скриване на tooltip
             */
            hideTooltip: function(element) {
                document.querySelectorAll('.custom-tooltip').forEach(tip => tip.remove());
            }
        }
    });

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('option-form')) {
            BackendModule.optionForm.init();
        }
    });

})();
