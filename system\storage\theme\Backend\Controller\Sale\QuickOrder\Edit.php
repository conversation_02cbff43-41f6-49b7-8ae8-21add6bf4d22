<?php

namespace Theme25\Backend\Controller\Sale\QuickOrder;

/**
 * Sub-контролер за редактиране на бърза поръчка
 */
class Edit extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява редактирането на бърза поръчка
     */
    public function execute() {
        $order_id = (int)$this->requestGet('order_id', 0);

        if (!$order_id) {
            $this->setError('Невалиден номер на поръчка');
            $this->redirectResponse($this->getAdminLink('sale/quick_order'));
            return;
        }

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'sale/quick_order')) {
            $this->setError('Нямате права за редактиране на бързи поръчки');
            $this->redirectResponse($this->getAdminLink('sale/quick_order'));
            return;
        }

        if ($this->isPostRequest()) {
            $this->processEdit($order_id);
        } else {
            $this->prepareEditForm($order_id);
        }
    }

    /**
     * Обработва POST заявката за редактиране
     *
     * @param int $order_id
     */
    private function processEdit($order_id) {
        try {
            $this->loadModelAs('sale/quickorder', 'quickOrderModel');
            
            // Получаване на данните от формата
            $data = $this->requestPost();
            
            // Валидация на данните
            if ($this->validateEditData($data)) {
                // Актуализиране на поръчката
                $update_data = [
                    'order_status_id' => (int)$data['status_id'],
                    'comment' => trim($data['comment'] ?? '')
                ];

                $result = $this->quickOrderModel->updateQuickOrder($order_id, $update_data);
                
                if ($result) {
                    $this->setSession('success', 'Бързата поръчка е актуализирана успешно');
                    $this->redirectResponse($this->getAdminLink('sale/quick_order/info', 'order_id=' . $order_id));
                } else {
                    $this->setError('Грешка при актуализиране на поръчката');
                }
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при обработка: ' . $e->getMessage());
        }

        // При грешка - показваме отново формата
        $this->prepareEditForm($order_id);
    }

    /**
     * Подготвя формата за редактиране
     *
     * @param int $order_id
     */
    private function prepareEditForm($order_id) {
        try {
            $this->loadModelAs('sale/quickorder', 'quickOrderModel');
            $order_info = $this->quickOrderModel->getQuickOrder($order_id);

            if (!$order_info) {
                $this->setError('Поръчката не е намерена');
                $this->redirectResponse($this->getAdminLink('sale/quick_order'));
                return;
            }

            // Подготвяне на продуктна информация
            $product_info = $this->prepareProductInfo($order_info);

            // Подготвяне на клиентска информация
            $customer_info = $this->prepareCustomerInfo($order_info);

            // Изчисляване на общата сума въз основа на ordered_price
            $calculated_total = $order_info['ordered_price'] * $order_info['quantity'];

            // Подготвяне на данни за формата
            $this->setData([
                'order_id' => $order_id,
                'order_info' => $order_info,
                'product_info' => $product_info,
                'customer_info' => $customer_info,
                'status_id' => $this->requestPost('status_id', $order_info['order_status_id']),
                'comment' => $this->requestPost('comment', $order_info['comment']),
                'status_options' => $this->getStatusOptions(),
                'breadcrumbs' => $this->prepareBreadcrumbs($order_id),
                'action_url' => $this->getAdminLink('sale/quick_order/edit', 'order_id=' . $order_id),
                'cancel_url' => $this->getAdminLink('sale/quick_order/info', 'order_id=' . $order_id),
                'date_added' => date('d.m.Y H:i:s', strtotime($order_info['date_added'])),
                'formatted_total' => $this->formatCurrency($calculated_total, 'BGN', 1)
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на данни: ' . $e->getMessage());
            $this->redirectResponse($this->getAdminLink('sale/quick_order'));
        }
    }

    /**
     * Валидира данните за редактиране
     *
     * @param array $data
     * @return bool
     */
    private function validateEditData($data) {
        $errors = [];

        // Валидация на статус
        if (!isset($data['status_id']) || !is_numeric($data['status_id'])) {
            $errors[] = 'Моля, изберете валиден статус';
        } else {
            $valid_statuses = ['0', '1', '2', '3', '4'];
            if (!in_array($data['status_id'], $valid_statuses)) {
                $errors[] = 'Невалиден статус';
            }
        }

        // Валидация на коментар (опционален)
        if (isset($data['comment']) && strlen($data['comment']) > 1000) {
            $errors[] = 'Коментарът не може да бъде по-дълъг от 1000 символа';
        }

        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->setError($error);
            }
            return false;
        }

        return true;
    }

    /**
     * Подготвя информацията за продукта
     *
     * @param array $order_info
     * @return array
     */
    private function prepareProductInfo($order_info) {
        try {
            $this->loadModelAs('tool/image', 'imageModel');

            $product_image = $order_info['product_image'] ?: 'no_image.png';
            $ordered_price = $order_info['ordered_price'];
            $quantity = $order_info['quantity'];
            $calculated_total = $ordered_price * $quantity;

            $product_info = [
                'product_id' => $order_info['product_id'],
                'name' => $order_info['product_name'],
                'model' => $order_info['product_model'] ?? '',
                'image' => $this->imageModel->resize($product_image, 80, 80),
                'quantity' => $quantity,
                'price' => $this->formatCurrency($ordered_price, 'BGN', 1),
                'total' => $this->formatCurrency($calculated_total, 'BGN', 1),
                'product_url' => $this->getAdminLink('catalog/product/edit', 'product_id=' . $order_info['product_id'])
            ];

            // Ако има опции на продукта
            if (!empty($order_info['product_option_value_id'])) {
                $product_info['option_name'] = $order_info['option_name'] ?? '';
                $product_info['option_value'] = $order_info['option_value'] ?? '';
                $product_info['has_options'] = true;
            } else {
                $product_info['has_options'] = false;
            }

            return $product_info;

        } catch (Exception $e) {
            // Fallback при грешка - използваме ordered_price и изчисляваме общата сума
            $ordered_price = $order_info['ordered_price'];
            $quantity = $order_info['quantity'];
            $calculated_total = $ordered_price * $quantity;

            return [
                'product_id' => $order_info['product_id'],
                'name' => $order_info['product_name'],
                'model' => '',
                'image' => '',
                'quantity' => $quantity,
                'price' => $this->formatCurrency($ordered_price, 'BGN', 1),
                'total' => $this->formatCurrency($calculated_total, 'BGN', 1),
                'has_options' => false
            ];
        }
    }

    /**
     * Подготвя информацията за клиента
     *
     * @param array $order_info
     * @return array
     */
    private function prepareCustomerInfo($order_info) {
        return [
            'name' => $order_info['names'],
            'phone' => $order_info['phone'],
            'comment' => $order_info['comment'] ?? ''
        ];
    }

    /**
     * Получава опциите за статус
     *
     * @return array
     */
    private function getStatusOptions() {
        return [
            ['status_id' => '0', 'name' => 'Нова поръчка'],
            ['status_id' => '1', 'name' => 'Прозванен'],
            ['status_id' => '2', 'name' => 'Потвърдена'],
            ['status_id' => '3', 'name' => 'Отказана'],
            ['status_id' => '4', 'name' => 'Завършена']
        ];
    }

    /**
     * Подготвя breadcrumbs навигацията
     *
     * @param int $order_id
     * @return array
     */
    private function prepareBreadcrumbs($order_id) {
        return [
            [
                'text' => 'Начало',
                'href' => $this->getAdminLink('common/dashboard')
            ],
            [
                'text' => 'Бързи поръчки',
                'href' => $this->getAdminLink('sale/quick_order')
            ],
            [
                'text' => 'Поръчка #' . $order_id,
                'href' => $this->getAdminLink('sale/quick_order/info', 'order_id=' . $order_id)
            ],
            [
                'text' => 'Редактиране',
                'href' => $this->getAdminLink('sale/quick_order/edit', 'order_id=' . $order_id)
            ]
        ];
    }
}
