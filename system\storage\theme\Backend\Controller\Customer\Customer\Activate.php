<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Activate extends \Theme25\ControllerSubMethods {
    
    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('customers.log');
    }

    /**
     * Изпълнява активирането на избрани клиенти
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за активиране на клиенти!';
        } else {
            $post = $this->requestPost();

            if (isset($post['selected']) && is_array($post['selected'])) {
                $this->loadModelAs('customer/customer', 'customerModel');

                $activated_count = 0;
                $errors = [];
                $activated_customers = [];

                foreach ($post['selected'] as $customer_id) {
                    $customer_id = (int)$customer_id;
                    
                    if ($customer_id > 0) {
                        $customer_info = $this->customerModel->getCustomer($customer_id);
                        
                        if ($customer_info) {
                            // Активираме клиента
                            $this->dbQuery("UPDATE `" . DB_PREFIX . "customer` SET status = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                            
                            $activated_count++;
                            $activated_customers[] = [
                                'id' => $customer_id,
                                'name' => $customer_info['firstname'] . ' ' . $customer_info['lastname'],
                                'email' => $customer_info['email']
                            ];
                        } else {
                            $errors[] = 'Клиент с ID ' . $customer_id . ' не съществува!';
                        }
                    }
                }

                if ($activated_count > 0) {
                    // Логваме действието
                    $this->logBulkActivation($activated_customers);
                    
                    $json['success'] = sprintf('Успешно активирани %d клиента!', $activated_count);
                    $json['activated_count'] = $activated_count;
                    $json['activated_customers'] = $activated_customers;
                }

                if (!empty($errors)) {
                    $json['warnings'] = $errors;
                }

                if ($activated_count == 0) {
                    $json['error'] = 'Няма клиенти за активиране или всички избрани клиенти са невалидни!';
                }
            } else {
                $json['error'] = 'Не са избрани клиенти за активиране!';
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Логва bulk активиране на клиенти
     */
    private function logBulkActivation($activated_customers) {
        try {
            $this->loadModelAs('user/user', 'userModel');
            $user_info = $this->userModel->getUser($this->user->getId());
            
            if ($user_info) {
                $customer_names = array_map(function($customer) {
                    return $customer['name'] . ' (' . $customer['email'] . ')';
                }, $activated_customers);
                
                $log_message = sprintf(
                    'Администратор %s активира %d клиента: %s',
                    $user_info['username'],
                    count($activated_customers),
                    implode(', ', $customer_names)
                );
                
                $this->writeLog($log_message);
            }
        } catch (Exception $e) {
            // Не спираме изпълнението при грешка в логването
            $this->writeLog('Грешка при логване на bulk активиране: ' . $e->getMessage());
        }
    }

    /**
     * Валидира заявката за активиране
     */
    private function validateActivationRequest() {
        $errors = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $errors[] = 'Нямате права за активиране на клиенти!';
        }

        if (!isset($post['selected']) || !is_array($post['selected']) || empty($post['selected'])) {
            $errors[] = 'Не са избрани клиенти за активиране!';
        }

        return $errors;
    }

    /**
     * Проверява дали клиент съществува
     */
    private function customerExists($customer_id) {
        if(!$this->hasModel('customerModel')) $this->loadModelAs('customer/customer', 'customerModel');
        $customer_info = $this->customerModel->getCustomer($customer_id);
        return !empty($customer_info);
    }

    /**
     * Проверява дали клиент вече е активен
     */
    private function isCustomerActive($customer_id) {
        $query = $this->dbQuery("SELECT status FROM `" . DB_PREFIX . "customer` WHERE customer_id = '" . (int)$customer_id . "'");
        
        if ($query->num_rows) {
            return (bool)$query->row['status'];
        }
        
        return false;
    }

    /**
     * Получава информация за клиент
     */
    private function getCustomerInfo($customer_id) {
        if(!$this->hasModel('customerModel')) $this->loadModelAs('customer/customer', 'customerModel');
        return $this->customerModel->getCustomer($customer_id);
    }

    /**
     * Активира единичен клиент
     */
    private function activateCustomer($customer_id) {
        try {
            $this->dbQuery("UPDATE `" . DB_PREFIX . "customer` SET status = '1' WHERE customer_id = '" . (int)$customer_id . "'");
            return true;
        } catch (Exception $e) {
            $this->writeLog('Грешка при активиране на клиент ' . $customer_id . ': ' . $e->getMessage());
            return false;
        }
    }
}
