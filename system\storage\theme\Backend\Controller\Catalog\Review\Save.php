<?php

namespace Theme25\Backend\Controller\Catalog\Review;

class Save extends \Theme25\ControllerSubMethods {

    private $errors = [];

    public function __construct($registry) {
        parent::__construct($registry);
    }

    public function execute() {
        $json = [];

        ob_start();

        try {
            $this->loadModelsAs([
                'catalog/review' => 'reviewModel',
                'catalog/product' => 'productModel'
            ]);

            // Валидация на данните
            if (!$this->validateForm()) {
                $json = [
                    'success' => false,
                    'errors' => $this->errors
                ];
            } else {
                // Запазване на коментара
                $review_id = $this->saveReview();
                
                if ($review_id) {
                    $json = [
                        'success' => true,
                        'message' => 'Коментарът е запазен успешно!',
                        'review_id' => $review_id,
                        'redirect_url' => $this->getAdminLink('catalog/review')
                    ];
                } else {
                    $json = [
                        'success' => false,
                        'errors' => ['general' => 'Грешка при запазване на коментара']
                    ];
                }
            }

        } catch (\Exception $e) {
            $json = [
                'success' => false,
                'errors' => ['general' => 'Системна грешка: ' . $e->getMessage()]
            ];
        }

        $output = ob_get_clean();
        if($output) {
            $json['debug_output'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира формата за коментар
     */
    private function validateForm() {
        $this->errors = [];

        // Валидация на продукт
        $product_id = (int)$this->requestPost('product_id', 0);
        if (!$product_id) {
            $this->errors['product_id'] = 'Моля, изберете продукт';
        } else {
            // Проверка дали продуктът съществува
            $product_info = $this->productModel->getProduct($product_id);
            if (!$product_info) {
                $this->errors['product_id'] = 'Избраният продукт не съществува';
            }
        }

        // Валидация на автор
        $author = trim($this->requestPost('author', ''));
        if (empty($author)) {
            $this->errors['author'] = 'Полето "Автор" е задължително';
        } elseif (mb_strlen($author) < 3 || mb_strlen($author) > 64) {
            $this->errors['author'] = 'Авторът трябва да бъде между 3 и 64 символа';
        }

        // Валидация на текст
        $text = trim($this->requestPost('text', ''));
        if (empty($text)) {
            $this->errors['text'] = 'Полето "Текст" е задължително';
        } elseif (mb_strlen($text) < 10) {
            $this->errors['text'] = 'Текстът трябва да бъде поне 10 символа';
        }

        // Валидация на рейтинг
        $rating = (int)$this->requestPost('rating', 0);
        if ($rating < 1 || $rating > 5) {
            $this->errors['rating'] = 'Рейтингът трябва да бъде между 1 и 5';
        }

        // Валидация на статус
        $status = $this->requestPost('status', '');
        if (!in_array($status, ['0', '1'])) {
            $this->errors['status'] = 'Невалиден статус';
        }

        // Валидация на дата
        $date_added = $this->requestPost('date_added', '');
        if (empty($date_added)) {
            $this->errors['date_added'] = 'Полето "Дата" е задължително';
        } elseif (!$this->isValidDateTime($date_added)) {
            $this->errors['date_added'] = 'Невалиден формат на датата';
        }

        return empty($this->errors);
    }

    /**
     * Запазва коментара в базата данни
     */
    private function saveReview() {
        $review_id = (int)$this->requestPost('review_id', 0);
        
        $data = [
            'product_id' => (int)$this->requestPost('product_id'),
            'author' => trim($this->requestPost('author')),
            'text' => trim($this->requestPost('text')),
            'rating' => (int)$this->requestPost('rating'),
            'status' => (int)$this->requestPost('status'),
            'date_added' => $this->requestPost('date_added')
        ];

        if ($review_id) {
            // Редактиране на съществуващ коментар
            $this->reviewModel->editReview($review_id, $data);
            return $review_id;
        } else {
            // Добавяне на нов коментар
            return $this->reviewModel->addReview($data);
        }
    }

    /**
     * Проверява дали датата е валидна
     */
    private function isValidDateTime($date) {
        $d = \DateTime::createFromFormat('Y-m-d H:i:s', $date);
        return $d && $d->format('Y-m-d H:i:s') === $date;
    }
}
