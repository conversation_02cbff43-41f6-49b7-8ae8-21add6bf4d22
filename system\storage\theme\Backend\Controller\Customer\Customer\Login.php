<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Login extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява логин като клиент
     */
    public function execute() {
        $customer_id = (int)$this->requestGet('customer_id', 0);
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $this->session->data['error'] = 'Нямате права за логин като клиент!';
            $this->response->redirect($this->getAdminLink('customer/customer'));
            return;
        }

        if (!$customer_id) {
            $this->session->data['error'] = 'Невалиден клиент!';
            $this->response->redirect($this->getAdminLink('customer/customer'));
            return;
        }

        $this->load->model('customer/customer');
        $customer_info = $this->model_customer_customer->getCustomer($customer_id);

        if (!$customer_info) {
            $this->session->data['error'] = 'Клиентът не съществува!';
            $this->response->redirect($this->getAdminLink('customer/customer'));
            return;
        }

        if (!$customer_info['status']) {
            $this->session->data['error'] = 'Клиентът е неактивен!';
            $this->response->redirect($this->getAdminLink('customer/customer'));
            return;
        }

        // Логваме действието
        $this->load->model('user/user');
        $user_info = $this->model_user_user->getUser($this->user->getId());
        
        if ($user_info) {
            $this->log->write('Администратор ' . $user_info['username'] . ' влезе като клиент ' . $customer_info['email']);
        }

        // Създаваме сесия за клиента във frontend
        $token = bin2hex(random_bytes(32));

        // Създаваме таблицата ако не съществува
        $this->createCustomerLoginTokenTableIfNotExists();

        // Запазваме токена в базата данни за сигурност
        $this->db->query("INSERT INTO `" . DB_PREFIX . "customer_login_token` SET
            customer_id = '" . (int)$customer_id . "',
            token = '" . $this->db->escape($token) . "',
            date_added = NOW(),
            expires = DATE_ADD(NOW(), INTERVAL 1 HOUR)");

        // Пренасочваме към frontend с токена
        $frontend_url = $this->getConfig('config_url') ? $this->getConfig('config_url') : HTTP_CATALOG;
        $login_url = $frontend_url . 'index.php?route=account/login&admin_token=' . $token;

        $this->response->redirect($login_url);
    }

    /**
     * Създава таблицата customer_login_token ако не съществува
     */
    private function createCustomerLoginTokenTableIfNotExists() {
        try {
            $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_login_token` (
                `customer_login_token_id` int(11) NOT NULL AUTO_INCREMENT,
                `customer_id` int(11) NOT NULL,
                `token` varchar(64) NOT NULL,
                `date_added` datetime NOT NULL,
                `expires` datetime NOT NULL,
                PRIMARY KEY (`customer_login_token_id`),
                UNIQUE KEY `token` (`token`),
                KEY `customer_id` (`customer_id`),
                KEY `expires` (`expires`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
        } catch (Exception $e) {
            $this->log->write('Грешка при създаване на таблица customer_login_token: ' . $e->getMessage());
        }
    }
}
