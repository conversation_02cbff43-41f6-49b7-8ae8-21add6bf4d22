<?php

namespace Theme25\Backend\Controller\Catalog\Manufacturer;

class Autocomplete extends \Theme25\ControllerSubMethods {
    
    /**
     * Обработва заявките за автодопълване на производители
     * 
     * @param array $get GET параметри от заявката
     * @return array
     */
    public function autocomplete($get) {
        $json = [];
        
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);
        
        $filter_data = [
            'start' => 0,
            'limit' => 10
        ];

        if (isset($get['filter_name'])) {
            $filter_data['filter_name'] = $get['filter_name'];
        }

        if (isset($get['limit'])) {
            $filter_data['limit'] = min(50, max(1, (int)$get['limit']));
        }
        
        $results = $this->manufacturerModel->getManufacturers($filter_data);
        
        foreach ($results as $result) {
            $json[] = [
                'manufacturer_id' => $result['manufacturer_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                'image' => $result['image'] ? ThemeData()->getImageServerUrl() . $result['image'] : '',
                'sort_order' => $result['sort_order']
            ];
        }
        
        return $json;
    }

    /**
     * Получава следващата стойност за sort_order
     * 
     * @return array
     */
    public function getNextSortOrder() {
        $json = [];
        
        try {
            $query = $this->db->query("SELECT MAX(sort_order) as max_sort FROM " . DB_PREFIX . "manufacturer");
            $next_sort_order = $query->row['max_sort'] ? $query->row['max_sort'] + 1 : 0;
            
            $json['next_sort_order'] = $next_sort_order;
            $json['success'] = true;
            
        } catch (Exception $e) {
            $json['error'] = 'Грешка при получаването на следващия sort_order: ' . $e->getMessage();
        }
        
        return $json;
    }

    /**
     * Търси производители с разширени опции
     * 
     * @return array
     */
    public function search() {
        $json = [];
        
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);
        
        $filter_data = [
            'start' => 0,
            'limit' => 20
        ];

        // Основно търсене по име
        if ($this->requestGet('filter_name')) {
            $filter_data['filter_name'] = $this->requestGet('filter_name');
        }

        // Търсене по sort_order
        if ($this->requestGet('filter_sort_order')) {
            $filter_data['filter_sort_order'] = $this->requestGet('filter_sort_order');
        }

        // Сортиране
        if ($this->requestGet('sort')) {
            $filter_data['sort'] = $this->requestGet('sort');
        }

        if ($this->requestGet('order')) {
            $filter_data['order'] = $this->requestGet('order');
        }

        // Пагинация
        if ($this->requestGet('page')) {
            $page = max(1, (int)$this->requestGet('page'));
            $filter_data['start'] = ($page - 1) * $filter_data['limit'];
        }

        $results = $this->manufacturerModel->getManufacturers($filter_data);
        $total = $this->manufacturerModel->getTotalManufacturers($filter_data);
        
        foreach ($results as $result) {
            $json['manufacturers'][] = [
                'manufacturer_id' => $result['manufacturer_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                'image' => $result['image'] ? ThemeData()->getImageServerUrl() . $result['image'] : '',
                'sort_order' => $result['sort_order'],
                'edit_url' => $this->getAdminLink('catalog/manufacturer/edit', 'manufacturer_id=' . $result['manufacturer_id'])
            ];
        }

        $json['total'] = $total;
        $json['page'] = isset($page) ? $page : 1;
        $json['limit'] = $filter_data['limit'];
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава производители за dropdown списъци
     * 
     * @return array
     */
    public function getForDropdown() {
        $json = [];
        
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);
        
        $filter_data = [
            'sort' => 'name',
            'order' => 'ASC'
        ];

        // Ако е зададен лимит
        if ($this->requestGet('limit')) {
            $filter_data['limit'] = min(100, max(1, (int)$this->requestGet('limit')));
        }
        
        $results = $this->manufacturerModel->getManufacturers($filter_data);
        
        // Добавяне на празна опция в началото
        $json[] = [
            'manufacturer_id' => 0,
            'name' => '--- Изберете производител ---'
        ];
        
        foreach ($results as $result) {
            $json[] = [
                'manufacturer_id' => $result['manufacturer_id'],
                'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
            ];
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали производител с дадено име съществува
     * 
     * @return array
     */
    public function checkExists() {
        $json = [];
        
        $name = trim($this->requestGet('name', ''));
        $manufacturer_id = (int)$this->requestGet('manufacturer_id', 0);
        
        if (empty($name)) {
            $json['error'] = 'Името не може да бъде празно!';
            $this->setJSONResponseOutput($json);
            return;
        }
        
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);
        
        $filter_data = [
            'filter_name' => $name,
            'exact_match' => true
        ];
        
        $results = $this->manufacturerModel->getManufacturers($filter_data);
        
        $exists = false;
        foreach ($results as $result) {
            if (strtolower($result['name']) === strtolower($name) && $result['manufacturer_id'] != $manufacturer_id) {
                $exists = true;
                break;
            }
        }
        
        $json['exists'] = $exists;
        $json['name'] = $name;
        
        if ($exists) {
            $json['message'] = 'Производител с това име вече съществува!';
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава статистики за производители
     * 
     * @return array
     */
    public function getStats() {
        $json = [];
        
        try {
            $this->loadModelsAs([
                'catalog/manufacturer' => 'manufacturerModel',
                'catalog/product' => 'productModel'
            ]);
            
            // Общ брой производители
            $total_manufacturers = $this->manufacturerModel->getTotalManufacturers();
            
            // Производители с продукти
            $query = $this->db->query("
                SELECT COUNT(DISTINCT m.manufacturer_id) as count 
                FROM " . DB_PREFIX . "manufacturer m 
                INNER JOIN " . DB_PREFIX . "product p ON m.manufacturer_id = p.manufacturer_id
            ");
            $manufacturers_with_products = $query->row['count'];
            
            // Производители без продукти
            $manufacturers_without_products = $total_manufacturers - $manufacturers_with_products;
            
            // Топ 5 производители по брой продукти
            $query = $this->db->query("
                SELECT m.manufacturer_id, m.name, COUNT(p.product_id) as product_count
                FROM " . DB_PREFIX . "manufacturer m 
                LEFT JOIN " . DB_PREFIX . "product p ON m.manufacturer_id = p.manufacturer_id
                GROUP BY m.manufacturer_id, m.name
                ORDER BY product_count DESC
                LIMIT 5
            ");
            $top_manufacturers = $query->rows;
            
            $json['total_manufacturers'] = $total_manufacturers;
            $json['manufacturers_with_products'] = $manufacturers_with_products;
            $json['manufacturers_without_products'] = $manufacturers_without_products;
            $json['top_manufacturers'] = $top_manufacturers;
            $json['success'] = true;
            
        } catch (Exception $e) {
            $json['error'] = 'Грешка при получаването на статистиките: ' . $e->getMessage();
        }
        
        $this->setJSONResponseOutput($json);
    }
}
