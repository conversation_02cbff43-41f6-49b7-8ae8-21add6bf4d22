<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class History extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Показва историята на клиента - връща HTML за tab view
     */
    public function execute() {
        try {
            $customer_id = (int)$this->requestGet('customer_id', 0);

            if (!$customer_id) {
                echo '<p class="text-gray-500">Невалиден клиент!</p>';
                return;
            }

            if (!$this->hasPermission('modify', 'customer/customer')) {
                echo '<p class="text-red-500">Нямате права за преглед на история!</p>';
                return;
            }

            $this->loadModelAs('customer/customer', 'customerModel');
            $customer_info = $this->customerModel->getCustomer($customer_id);

            if (!$customer_info) {
                echo '<p class="text-red-500">Клиентът не съществува!</p>';
                return;
            }

            // Получаваме историята
            $history_data = $this->getHistoryData($customer_id);

            // Генерираме HTML
            echo $this->generateHistoryListHtml($history_data);

        } catch (Exception $e) {
            echo '<p class="text-red-500">Грешка при зареждане на история: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }

    /**
     * Добавя нова история за клиента
     */
    public function addhistory() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за добавяне на история!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            $comment = trim($this->requestPost('comment', ''));

            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } elseif (empty($comment)) {
                $json['error'] = 'Коментарът е задължителен!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                
                $this->customerModel->addHistory($customer_id, $comment);
                
                $json['success'] = 'Историята е добавена успешно!';
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Генерира HTML за списъка с история
     */
    private function generateHistoryListHtml($history_data) {
        if (empty($history_data['histories'])) {
            return '<p class="text-gray-500">Няма записана история за този клиент.</p>';
        }

        $html = '<div class="space-y-3">';

        foreach ($history_data['histories'] as $history) {
            $html .= '
                <div class="flex items-start space-x-3 p-4 bg-white border border-gray-200 rounded-lg">
                    <div class="flex-shrink-0">
                        <i class="ri-message-2-line text-blue-600"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="text-sm text-gray-900">' . nl2br(htmlspecialchars($history['comment'])) . '</div>
                        <div class="text-xs text-gray-500 mt-1">
                            ' . htmlspecialchars($history['date_added']) . '
                        </div>
                    </div>
                </div>';
        }

        $html .= '</div>';

        // Добавяме информация за общия брой
        if ($history_data['total'] > count($history_data['histories'])) {
            $html .= '<div class="mt-4 text-sm text-gray-500 text-center">
                Показани ' . count($history_data['histories']) . ' от ' . $history_data['total'] . ' записа
            </div>';
        }

        return $html;
    }

    /**
     * Получава данните за историята (за HTML генериране)
     */
    private function getHistoryData($customer_id) {
        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);

        $this->loadModelAs('customer/customer', 'customerModel');

        $histories = $this->customerModel->getHistories($customer_id, $start, $limit);
        $total = $this->customerModel->getTotalHistories($customer_id);

        $history_data = [];
        foreach ($histories as $history) {
            $history_data[] = [
                'customer_history_id' => $history['customer_history_id'],
                'comment' => $history['comment'],
                'date_added' => date('d.m.Y H:i', strtotime($history['date_added']))
            ];
        }

        return [
            'histories' => $history_data,
            'total' => $total
        ];
    }
}
