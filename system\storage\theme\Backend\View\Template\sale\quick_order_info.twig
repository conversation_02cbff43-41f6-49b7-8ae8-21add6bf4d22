<!-- Quick Order Info Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Детайли на бърза поръчка #{{ order_id }}</h1>
            <p class="text-gray-500 mt-1">Преглед на информацията за бързата поръчка</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2">
            {% if back_url %}
            <a href="{{ back_url }}" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-arrow-left-line"></i>
                </div>
                <span>Назад</span>
            </a>
            {% endif %}
            {% if edit_url %}
            <a href="{{ edit_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-edit-line"></i>
                </div>
                <span>Редактиране</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <div class="max-w-7xl space-y-6">

        {% if error_warning %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="ri-error-warning-line text-red-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">{{ error_warning }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        {% if success %}
        <div class="bg-green-50 border-l-4 border-green-400 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="ri-check-line text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">{{ success }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Quick Order Information -->
        <div class="bg-white rounded shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Информация за бързата поръчка</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-3">Основна информация</h4>
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-medium">Номер:</span> #{{ order_id }}</p>
                            <p class="text-sm"><span class="font-medium">Дата:</span> {{' '}}{{ date_added }}</p>
                            {% if date_modified %}
                            <p class="text-sm"><span class="font-medium">Последна промяна:</span> {{' '}}{{ date_modified }}</p>
                            {% endif %}
                            <p class="text-sm"><span class="font-medium">Статус:</span>
                                <span class="status-badge inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if status_id == '0' %}bg-blue-100 text-blue-800
                                    {% elseif status_id == '1' %}bg-yellow-100 text-yellow-800
                                    {% elseif status_id == '2' %}bg-green-100 text-green-800
                                    {% elseif status_id == '3' %}bg-red-100 text-red-800
                                    {% elseif status_id == '4' %}bg-gray-100 text-gray-800
                                    {% endif %}">{{ status_name }}</span>
                            </p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-3">Информация за клиента</h4>
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-medium">Име:</span> {{' '}}{{ customer_name }}</p>
                            <p class="text-sm"><span class="font-medium">Телефон:</span> {{' '}}{{ phone }}</p>
                            {% if email %}
                            <p class="text-sm"><span class="font-medium">Email:</span> {{' '}}{{ email }}</p>
                            {% endif %}
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-3">Продукт</h4>
                        {% if product_info %}
                        <div class="flex items-start space-x-4">
                            {% if product_info.image %}
                            <div class="flex-shrink-0">
                                <img src="{{ product_info.image }}" alt="{{ product_info.name }}" class="w-16 h-16 object-cover rounded border">
                            </div>
                            {% endif %}
                            <div class="flex-1 space-y-2">
                                <div class="flex items-center space-x-2">
                                    <p class="text-sm font-medium">{{ product_info.name }}</p>
                                    {% if product_info.product_url %}
                                    <a href="{{ product_info.product_url }}" class="text-primary hover:text-primary/80 text-xs" title="Редактиране на продукт">
                                        <i class="ri-edit-line"></i>
                                    </a>
                                    {% endif %}
                                </div>
                                {% if product_info.model %}
                                <p class="text-xs text-gray-500">Модел: {{ product_info.model }}</p>
                                {% endif %}
                                <p class="text-sm"><span class="font-medium">Количество:</span> {{ product_info.quantity }}</p>
                                <p class="text-sm"><span class="font-medium">Цена:</span> {{ product_info.price }}</p>
                                <p class="text-sm"><span class="font-medium">Общо:</span> {{ product_info.total }}</p>
                                {% if product_info.has_options and product_info.option_name %}
                                <p class="text-sm"><span class="font-medium">{{ product_info.option_name }}:</span> {{ product_info.option_value }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <div class="space-y-2">
                            <p class="text-sm"><span class="font-medium">Име:</span> {{ product_name }}</p>
                            <p class="text-sm"><span class="font-medium">Количество:</span> {{ quantity }}</p>
                            {% if product_option_value %}
                            <p class="text-sm"><span class="font-medium">Опция:</span> {{ product_option_value }}</p>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>


        <!-- Comments Section -->
        {% if comment %}
        <div class="bg-white rounded shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Коментар</h3>
            </div>
            <div class="p-6">
                <p class="text-sm text-gray-900 bg-gray-50 p-3 rounded border">{{ comment }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</main>
