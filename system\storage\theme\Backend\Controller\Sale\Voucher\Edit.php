<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за редактиране на подаръчен ваучер
 */
class Edit extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява редактирането на ваучер
     */
    public function execute() {
        $voucher_id = (int)$this->requestGet('voucher_id', 0);

        if (!$voucher_id) {
            $this->setError('Невалиден номер на ваучер');
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
            return;
        }

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'sale/voucher')) {
            $this->setError('Нямате права за редактиране на ваучери');
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
            return;
        }

        if ($this->isPostRequest()) {
            $this->processEdit($voucher_id);
        } else {
            $this->prepareEditForm($voucher_id);
        }
    }

    /**
     * Обработва POST заявката за редактиране
     *
     * @param int $voucher_id
     */
    private function processEdit($voucher_id) {
        try {
            $this->loadModelAs('sale/voucher', 'voucherModel');
            
            // Получаване на данните от формата
            $data = $this->requestPost();
            
            // Валидация на данните
            if ($this->validateEditData($data, $voucher_id)) {
                // Подготвяне на данните за актуализиране
                $voucher_data = [
                    'code' => strtoupper(trim($data['code'])),
                    'from_name' => trim($data['from_name']),
                    'from_email' => trim($data['from_email']),
                    'to_name' => trim($data['to_name']),
                    'to_email' => trim($data['to_email']),
                    'voucher_theme_id' => (int)$data['voucher_theme_id'],
                    'message' => trim($data['message'] ?? ''),
                    'amount' => (float)$data['amount'],
                    'status' => isset($data['status']) ? (int)$data['status'] : 1
                ];

                $this->voucherModel->editVoucher($voucher_id, $voucher_data);
                
                $this->setSession('success', 'Ваучерът е актуализиран успешно');
                $this->redirectResponse($this->getAdminLink('sale/voucher'));
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при обработка: ' . $e->getMessage());
        }

        // При грешка - показваме отново формата
        $this->prepareEditForm($voucher_id);
    }

    /**
     * Подготвя формата за редактиране
     *
     * @param int $voucher_id
     */
    private function prepareEditForm($voucher_id) {
        try {
            $this->loadModelsAs([
                'sale/voucher' => 'voucherModel',
                'sale/voucher_theme' => 'voucherThemeModel'
            ]);

            $voucher_info = $this->voucherModel->getVoucher($voucher_id);

            if (!$voucher_info) {
                $this->setError('Ваучерът не е намерен');
                $this->redirectResponse($this->getAdminLink('sale/voucher'));
                return;
            }

            // Получаване на темите за ваучери
            $voucher_themes = $this->voucherThemeModel->getVoucherThemes();
            $theme_options = [];
            foreach ($voucher_themes as $theme) {
                $theme_options[] = [
                    'value' => $theme['voucher_theme_id'],
                    'text' => $theme['name']
                ];
            }

            // Подготвяне на данни за формата
            $form_data = [
                'voucher_id' => $voucher_info['voucher_id'],
                'code' => $this->requestPost('code', $voucher_info['code']),
                'from_name' => $this->requestPost('from_name', $voucher_info['from_name']),
                'from_email' => $this->requestPost('from_email', $voucher_info['from_email']),
                'to_name' => $this->requestPost('to_name', $voucher_info['to_name']),
                'to_email' => $this->requestPost('to_email', $voucher_info['to_email']),
                'voucher_theme_id' => $this->requestPost('voucher_theme_id', $voucher_info['voucher_theme_id']),
                'message' => $this->requestPost('message', $voucher_info['message']),
                'amount' => $this->requestPost('amount', $voucher_info['amount']),
                'status' => $this->requestPost('status', $voucher_info['status'])
            ];

            $this->setData([
                'form_data' => $form_data,
                'voucher_themes' => $theme_options,
                'action_url' => $this->getAdminLink('sale/voucher/edit', 'voucher_id=' . $voucher_id),
                'cancel_url' => $this->getAdminLink('sale/voucher'),
                'form_title' => 'Редактиране на подаръчен ваучер',
                'button_save' => 'Запази',
                'button_cancel' => 'Отказ'
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при подготовка на формата: ' . $e->getMessage());
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
        }
    }

    /**
     * Валидира данните за редактиране
     *
     * @param array $data
     * @param int $voucher_id
     * @return bool
     */
    private function validateEditData($data, $voucher_id) {
        $errors = [];

        // Валидация на кода
        if (empty($data['code'])) {
            $errors['code'] = 'Кодът на ваучера е задължителен';
        } elseif (strlen($data['code']) < 3 || strlen($data['code']) > 10) {
            $errors['code'] = 'Кодът трябва да бъде между 3 и 10 символа';
        } else {
            // Проверка дали кодът вече съществува при друг ваучер
            $this->loadModelAs('sale/voucher', 'voucherModel');
            $existing_voucher = $this->voucherModel->getVoucherByCode($data['code']);
            if ($existing_voucher && $existing_voucher['voucher_id'] != $voucher_id) {
                $errors['code'] = 'Ваучер с този код вече съществува';
            }
        }

        // Валидация на имената
        if (empty($data['from_name'])) {
            $errors['from_name'] = 'Името на подарителя е задължително';
        }

        if (empty($data['to_name'])) {
            $errors['to_name'] = 'Името на получателя е задължително';
        }

        // Валидация на email адресите
        if (empty($data['from_email'])) {
            $errors['from_email'] = 'Email адресът на подарителя е задължителен';
        } elseif (!filter_var($data['from_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['from_email'] = 'Невалиден email адрес на подарителя';
        }

        if (empty($data['to_email'])) {
            $errors['to_email'] = 'Email адресът на получателя е задължителен';
        } elseif (!filter_var($data['to_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['to_email'] = 'Невалиден email адрес на получателя';
        }

        // Валидация на сумата
        if (empty($data['amount']) || !is_numeric($data['amount'])) {
            $errors['amount'] = 'Сумата е задължителна и трябва да бъде число';
        } elseif ((float)$data['amount'] <= 0) {
            $errors['amount'] = 'Сумата трябва да бъде по-голяма от 0';
        }

        // Валидация на темата
        if (empty($data['voucher_theme_id'])) {
            $errors['voucher_theme_id'] = 'Темата на ваучера е задължителна';
        }

        // Запазване на грешките в данните
        if (!empty($errors)) {
            $this->setData('errors', $errors);
            return false;
        }

        return true;
    }
}
