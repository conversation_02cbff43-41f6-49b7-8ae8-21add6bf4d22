<?php

namespace Theme25\Backend\Controller\Sale\QuickOrder;

/**
 * Sub-контролер за списъка с бързи поръчки
 *
 * Този контролер управлява логиката за показване на списъка с бързи поръчки,
 * включително филтриране, сортиране и пагинация.
 *
 * @package Theme25\Backend\Controller\Sale\QuickOrder
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Index extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за списъка с бързи поръчки
     */
    public function prepareData() {
        $this->prepareQuickOrderListData()
             ->prepareFilterOptions()
             ->prepareQuickOrderItems()
             ->preparePagination();

        // Подготвяне на URL-и и филтри за шаблона
        $filter_params = [];
        foreach ($this->filters as $key => $value) {
            if ($value !== '' && $key !== 'page') {
                $filter_params[$key] = $value;
            }
        }

        // Генериране на URL параметри за филтри
        $filter_url_params = $this->buildFilterParams();

        $this->setData([
            'back_url' => $this->getAdminLink('sale/quick_order'),
            'current_url' => $this->getAdminLink('sale/quick_order', $filter_url_params),
            'clear_filters_url' => $this->getAdminLink('sale/quick_order'),
            'filter_action_url' => $this->getAdminLink('sale/quick_order'),
            // Филтри за шаблона
            'filter_status_id' => $this->filters['filter_status_id'],
            'filter_search' => $this->filters['filter_search'],
            'filter_order_id' => $this->filters['filter_order_id'],
            'filter_names' => $this->filters['filter_names'],
            'filter_phone' => $this->filters['filter_phone'],
            'filter_date_added' => $this->filters['filter_date_added'],
            // Сортиране
            'sort' => $this->filters['sort'],
            'order' => $this->filters['order'],
            // User token за AJAX заявки
            'user_token' => $this->getUserToken()
        ]);
    }

    /**
     * Подготвя основните данни за списъка с бързи поръчки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareQuickOrderListData() {
        // Зареждане на необходимите модели
        try {
            $this->loadModelsAs([
                'sale/quickorder' => 'quickOrders',
                'localisation/order_status' => 'orderStatuses',
                'tool/image' => 'imageModel'
            ]);
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на модели: ' . $e->getMessage());
            return $this;
        }

        // Получаване на филтри от заявката
        $this->filters = [
            'filter_order_id' => $this->requestGet('filter_order_id', ''),
            'filter_names' => $this->requestGet('filter_names', ''),
            'filter_phone' => $this->requestGet('filter_phone', ''),
            'filter_product_name' => $this->requestGet('filter_product_name', ''),
            'filter_status_id' => $this->requestGet('filter_status_id', ''),
            'filter_date_added' => $this->requestGet('filter_date_added', ''),
            'filter_date_modified' => $this->requestGet('filter_date_modified', ''),
            'filter_search' => $this->requestGet('filter_search', ''),
            'sort' => $this->requestGet('sort', 'qo.order_id'),
            'order' => $this->requestGet('order', 'DESC'),
            'page' => (int)$this->requestGet('page', 1)
        ];

        // Ако има общо търсене, разпределяме го по отделните филтри
        if (!empty($this->filters['filter_search'])) {
            $search_term = trim($this->filters['filter_search']);
            // Ако изглежда като номер на поръчка
            if (is_numeric($search_term)) {
                $this->filters['filter_order_id'] = $search_term;
            }
            // Ако изглежда като телефон
            elseif (preg_match('/^[\d\s\+\-\(\)]+$/', $search_term)) {
                $this->filters['filter_phone'] = $search_term;
            }
            // Иначе търсим в имена и продукти
            else {
                $this->filters['filter_names'] = $search_term;
                $this->filters['filter_product_name'] = $search_term;
            }
        }

        return $this;
    }

    /**
     * Подготвя опциите за филтриране
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareFilterOptions() {
        // Статуси на бързи поръчки
        $this->setData('order_statuses', [
            ['status_id' => '0', 'name' => 'Нова поръчка'],
            ['status_id' => '1', 'name' => 'Прозванен'],
            ['status_id' => '2', 'name' => 'Потвърдена'],
            ['status_id' => '3', 'name' => 'Отказана'],
            ['status_id' => '4', 'name' => 'Завършена']
        ]);

        // Предаване на филтрите към view
        foreach ($this->filters as $key => $value) {
            $this->setData($key, $value);
        }

        return $this;
    }

    /**
     * Подготвя елементите от списъка с бързи поръчки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareQuickOrderItems() {
        $limit = $this->config->get('config_limit_admin') ?: 20;
        $start = ($this->filters['page'] - 1) * $limit;

        $filter_data = array_merge($this->filters, [
            'start' => $start,
            'limit' => $limit
        ]);

        try {
            // Получаване на бързите поръчки
            $quick_orders = $this->quickOrders->getQuickOrders($filter_data);
            $total_orders = $this->quickOrders->getTotalQuickOrders($filter_data);

            // Форматиране на данните за view
            $formatted_orders = [];
            foreach ($quick_orders as $order) {
                // Подготвяне на продуктно изображение
                $product_image = $order['product_image'] ?: 'no_image.png';
                $image_thumb = $this->imageModel->resize($product_image, 50, 50);

                // Изчисляване на общата сума въз основа на ordered_price
                $ordered_price = $order['ordered_price'] ?? $order['price']; // Fallback към price ако няма ordered_price
                $calculated_total = $ordered_price * $order['quantity'];

                $formatted_orders[] = [
                    'order_id' => $order['order_id'],
                    'customer_name' => $order['names'], // Използваме customer_name за съвместимост с шаблона
                    'phone' => $order['phone'],
                    'product_name' => $order['product_name'],
                    'product_image' => $image_thumb,
                    'quantity' => $order['quantity'],
                    'price' => $this->formatCurrency($ordered_price, 'BGN', 1),
                    'total' => $this->formatCurrency($calculated_total, 'BGN', 1),
                    'status' => $this->getStatusName($order['order_status_id']),
                    'status_id' => $order['order_status_id'],
                    'status_class' => $this->getStatusClass($order['order_status_id']),
                    'date_added' => date('d.m.Y H:i', strtotime($order['date_added'])),
                    'date_modified' => $order['date_modified'] ? date('d.m.Y H:i', strtotime($order['date_modified'])) : '',
                    'view_url' => $this->getAdminLink('sale/quick_order/info', 'order_id=' . $order['order_id']),
                    'edit_url' => $this->getAdminLink('sale/quick_order/edit', 'order_id=' . $order['order_id']),
                    'delete_url' => $this->getAdminLink('sale/quick_order/delete', 'order_id=' . $order['order_id'])
                ];
            }

            $this->setData([
                'quick_orders' => $formatted_orders,
                'total_orders' => $total_orders
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при получаване на данни: ' . $e->getMessage());
            $this->setData([
                'quick_orders' => [],
                'total_orders' => 0
            ]);
        }

        return $this;
    }

    /**
     * Подготвя пагинацията
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin') ?: 20;
        $total = $this->getData('total_orders') ?: 0;
        $page = $this->filters['page'];

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $url_separator = !empty($filter_params) ? '&' : '';
        $pagination->url = $this->getAdminLink('sale/quick_order', $filter_params . $url_separator . 'page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('sale/quick_order', $filter_params . $url_separator . 'limit={limit}'));
        $pagination->setProductText('бързи поръчки');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)',
                ($page - 1) * $limit + 1,
                min($total, $page * $limit),
                $total,
                ceil($total / $limit)
            ),
            'limit_options' => array_map(function($limit_val) {
                return ['value' => $limit_val, 'text' => (string)$limit_val];
            }, $limits),
            'current_limit' => $limit
        ]);

        return $this;
    }



    /**
     * Получава името на статуса по ID
     *
     * @param int $status_id
     * @return string
     */
    private function getStatusName($status_id) {
        $statuses = [
            '0' => 'Нова поръчка',
            '1' => 'Прозванен',
            '2' => 'Потвърдена',
            '3' => 'Отказана',
            '4' => 'Завършена'
        ];

        return $statuses[$status_id] ?? 'Неизвестен';
    }

    /**
     * Получава CSS класа за статуса по ID
     *
     * @param int $status_id
     * @return string
     */
    private function getStatusClass($status_id) {
        $classes = [
            '0' => 'bg-blue-100 text-blue-800',
            '1' => 'bg-yellow-100 text-yellow-800',
            '2' => 'bg-green-100 text-green-800',
            '3' => 'bg-red-100 text-red-800',
            '4' => 'bg-gray-100 text-gray-800'
        ];

        return $classes[$status_id] ?? 'bg-gray-100 text-gray-800';
    }

    /**
     * Генерира параметрите за филтриране в URL
     *
     * @return string
     */
    private function buildFilterParams() {
        $params = [];

        foreach ($this->filters as $key => $value) {
            if ($value !== '' && $key !== 'page') {
                $params[] = $key . '=' . urlencode($value);
            }
        }

        return !empty($params) ? implode('&', $params) : '';
    }
}
