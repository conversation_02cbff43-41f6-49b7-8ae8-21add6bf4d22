<?php
class Action {
	private $id;
	private $route;
	private $method = 'index';

	public function __construct($route) {
		$this->id = $route;
		
		$parts = explode('/', preg_replace('/[^a-zA-Z0-9_\/]/', '', (string)$route));

		// Break apart the route
		while ($parts) {
			$file = DIR_APPLICATION . 'controller/' . implode('/', $parts) . '.php';

			if (is_file($file)) {
				$this->route = implode('/', $parts);		
				break;
			} else {
				$this->method = array_pop($parts);
			}
		}
	}
	
	public function getId() {
		return $this->id;
	}
	
	public function execute($registry, array $args = array()) {
		// Stop any magical methods being called
		if (substr($this->method, 0, 2) == '__') {
			return new \Exception('Error: Calls to magic methods are not allowed!');
		}

		$file = DIR_APPLICATION . 'controller/' . $this->route . '.php';		
		$class = 'Controller' . preg_replace('/[^a-zA-Z0-9]/', '', $this->route);

		$controller = null;

		// Initialize the class
		if (is_file($file)) {
			include_once($file);
			$controller = new $class($registry);
		} 
		else {
			$this->route = $this->getId();
		}

		$theme_file = $this->buildThemeControllerFilePath($this->route);

		$flag_call_theme_controller = false;

		if(is_file($theme_file)) {
			$flag_call_theme_controller = true;
		}

		$reflection = new ReflectionClass($class);
		
		if (($reflection->hasMethod($this->method) && $reflection->getMethod($this->method)->getNumberOfRequiredParameters() <= count($args))) {
			return $this->callRequestProcessor($controller, $this->method, $args, $registry);
		} else if($class == 'Controller') 
		{
			$parts = explode('/', preg_replace('/[^a-zA-Z0-9_\/]/', '', (string)$this->route));

			// Трансформиране на всяка част - първа буква главна, останалите малки
			$transformedParts = [];
			foreach ($parts as $part) {
				$transformedParts[] = ucfirst(strtolower($part));
			}

			$class = 'Controller' . preg_replace('/[^a-zA-Z0-9]/', '', implode('/', $transformedParts));
			return $this->callRequestProcessor($class, 'index', $args, $registry);
		} else {
			if($flag_call_theme_controller) {
				return $this->callRequestProcessor($controller, $this->method, $args, $registry);
			}
			return new \Exception('Error: Could not call ' . $this->route . '/' . $this->method . '!');
		}
	}

	private function callRequestProcessor($controller, $method, $args = array(), $registry = null) {
		// Инициализиране на RequestProcessor класа
		include_once(DIR_SYSTEM . 'engine/requestprocessor.php');

		$processor = new RequestProcessor($registry, DIR_APPLICATION);

		// Пренасочване на извикването на метода чрез RequestProcessor
		$result = $processor->process($controller, $method, $args);

		if($result === -11111111111) {
			return new \Exception('Error: Could not call ' . $this->route . '/' . $method . '!');
		}
		return $result;
	}

	
	private function wichPathIs() {
		$parts = explode('/', rtrim(DIR_APPLICATION, '/'));
		$path = $parts[count($parts) - 1] == 'catalog' ? 'Frontend' : 'Backend';
		return $path;
	}

	private function buildThemeControllerFilePath($route) {
		$parts = explode('/', $route);
		$parts = array_map(function($part) {
			return ucfirst($part);
		}, $parts);
        $theme_controller = implode('/', $parts);
        return DIR_THEME . $this->wichPathIs() . '/Controller/' . $theme_controller . '.php';
	}
}
