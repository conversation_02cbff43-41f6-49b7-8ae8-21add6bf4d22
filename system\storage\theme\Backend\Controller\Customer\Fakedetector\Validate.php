<?php

namespace Theme25\Backend\Controller\Customer\Fakedetector;

class Validate extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Валидира съвместимостта на модула с Rakla.bg проекта
     */
    public function execute() {
        $json = [];

        try {
            $validation_results = [
                'table_conflicts' => $this->checkTableConflicts(),
                'dependency_check' => $this->checkDependencies(),
                'permission_check' => $this->checkPermissions(),
                'database_compatibility' => $this->checkDatabaseCompatibility()
            ];

            $json['validation'] = $validation_results;
            $json['overall_status'] = $this->getOverallStatus($validation_results);
            
        } catch (Exception $e) {
            $json['error'] = 'Грешка при валидация: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява за конфликти с имената на таблиците
     */
    private function checkTableConflicts() {
        $our_tables = [
            'fake_customer_log',
            'fake_blacklist', 
            'fake_detector_settings'
        ];

        $conflicts = [];
        $existing = [];

        foreach ($our_tables as $table) {
            $full_name = DB_PREFIX . $table;
            $result = $this->dbQuery("SHOW TABLES LIKE '" . $full_name . "'");
            
            if ($result->num_rows > 0) {
                $existing[] = $full_name;
            }
        }

        return [
            'status' => count($conflicts) === 0 ? 'OK' : 'CONFLICT',
            'conflicts' => $conflicts,
            'existing_tables' => $existing,
            'message' => count($conflicts) === 0 
                ? 'Няма конфликти с имената на таблиците'
                : 'Открити конфликти с имената на таблиците'
        ];
    }

    /**
     * Проверява зависимостите от други таблици
     */
    private function checkDependencies() {
        $required_tables = [
            'customer' => 'Основна таблица за клиенти',
            'customer_ip' => 'Таблица за IP адреси (опционална)',
            'order' => 'Таблица за поръчки'
        ];

        $missing = [];
        $existing = [];

        foreach ($required_tables as $table => $description) {
            $full_name = DB_PREFIX . $table;
            $result = $this->dbQuery("SHOW TABLES LIKE '" . $full_name . "'");
            
            if ($result->num_rows > 0) {
                $existing[] = ['table' => $full_name, 'description' => $description];
            } else {
                $missing[] = ['table' => $full_name, 'description' => $description];
            }
        }

        return [
            'status' => count($missing) === 0 ? 'OK' : 'WARNING',
            'existing' => $existing,
            'missing' => $missing,
            'message' => count($missing) === 0 
                ? 'Всички зависимости са налични'
                : 'Липсват някои опционални таблици'
        ];
    }

    /**
     * Проверява права за достъп
     */
    private function checkPermissions() {
        $required_permissions = [
            'customer/customer' => 'access',
            'customer/customer' => 'modify'
        ];

        $missing_permissions = [];

        foreach ($required_permissions as $route => $type) {
            if (!$this->hasPermission($type, $route)) {
                $missing_permissions[] = "$type permission for $route";
            }
        }

        return [
            'status' => count($missing_permissions) === 0 ? 'OK' : 'ERROR',
            'missing' => $missing_permissions,
            'message' => count($missing_permissions) === 0 
                ? 'Всички необходими права са налични'
                : 'Липсват права за достъп'
        ];
    }

    /**
     * Проверява съвместимостта на базата данни
     */
    private function checkDatabaseCompatibility() {
        $checks = [];

        // Проверка на версията на MySQL
        $version_result = $this->dbQuery("SELECT VERSION() as version");
        $mysql_version = $version_result->row['version'];
        $checks['mysql_version'] = [
            'value' => $mysql_version,
            'status' => version_compare($mysql_version, '5.7.0', '>=') ? 'OK' : 'WARNING',
            'message' => version_compare($mysql_version, '5.7.0', '>=') 
                ? 'MySQL версията е съвместима'
                : 'Препоръчва се MySQL 5.7 или по-нова версия'
        ];

        // Проверка на charset поддръжката
        $charset_result = $this->dbQuery("SHOW CHARACTER SET LIKE 'utf8mb4'");
        $checks['utf8mb4_support'] = [
            'status' => $charset_result->num_rows > 0 ? 'OK' : 'WARNING',
            'message' => $charset_result->num_rows > 0 
                ? 'UTF8MB4 charset е поддържан'
                : 'UTF8MB4 charset не е наличен, ще се използва UTF8'
        ];

        // Проверка на ENGINE поддръжката
        $engine_result = $this->dbQuery("SHOW ENGINES WHERE Engine = 'InnoDB' AND Support IN ('YES', 'DEFAULT')");
        $checks['innodb_support'] = [
            'status' => $engine_result->num_rows > 0 ? 'OK' : 'ERROR',
            'message' => $engine_result->num_rows > 0 
                ? 'InnoDB engine е поддържан'
                : 'InnoDB engine не е наличен'
        ];

        $overall_status = 'OK';
        foreach ($checks as $check) {
            if ($check['status'] === 'ERROR') {
                $overall_status = 'ERROR';
                break;
            } elseif ($check['status'] === 'WARNING' && $overall_status === 'OK') {
                $overall_status = 'WARNING';
            }
        }

        return [
            'status' => $overall_status,
            'checks' => $checks,
            'message' => 'Проверка на съвместимостта на базата данни завършена'
        ];
    }

    /**
     * Определя общия статус на валидацията
     */
    private function getOverallStatus($results) {
        $statuses = [];
        foreach ($results as $result) {
            $statuses[] = $result['status'];
        }

        if (in_array('ERROR', $statuses)) {
            return 'ERROR';
        } elseif (in_array('WARNING', $statuses)) {
            return 'WARNING';
        } else {
            return 'OK';
        }
    }

    /**
     * Генерира детайлен отчет за валидацията
     */
    public function generateReport() {
        $validation = $this->execute();
        
        $report = "=== FAKE DETECTOR MODULE VALIDATION REPORT ===\n\n";
        $report .= "Дата: " . date('Y-m-d H:i:s') . "\n";
        $report .= "Проект: Rakla.bg\n\n";
        
        // Добавяне на детайли за всяка проверка
        // ... (ще се добави при нужда)
        
        return $report;
    }
}
