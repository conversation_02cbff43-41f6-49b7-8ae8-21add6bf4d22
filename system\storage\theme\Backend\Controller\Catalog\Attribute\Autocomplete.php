<?php

namespace Theme25\Backend\Controller\Catalog\Attribute;

class Autocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Автозавършване за атрибути
     */
    public function autocomplete($get) {
        $json = [];

        // Зареждане на модела за атрибути
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel'
        ]);

        // Получаване на параметрите от заявката
        $filter_name = $get['filter_name'] ?? '';
        $filter_attribute_group_id = $get['filter_attribute_group_id'] ?? '';
        $limit = (int)($get['limit'] ?? 10);

        // Ограничаваме лимита между 1 и 50
        $limit = max(1, min(50, $limit));

        // Подготвяне на данните за филтриране
        $filter_data = [
            'filter_name' => $filter_name,
            'start' => 0,
            'limit' => $limit
        ];

        // Филтриране по група атрибути ако е зададена
        if (!empty($filter_attribute_group_id) && is_numeric($filter_attribute_group_id)) {
            $filter_data['filter_attribute_group_id'] = (int)$filter_attribute_group_id;
        }

        // Ако няма филтър по име, зареждаме първите атрибути
        if (empty($filter_name)) {
            $filter_data['sort'] = 'ad.name';
            $filter_data['order'] = 'ASC';
        }

        try {
            // Зареждане на атрибутите
            $attributes = $this->attributeModel->getAttributes($filter_data);

            // Подготвяне на резултата
            foreach ($attributes as $attribute) {
                $json[] = [
                    'attribute_id' => $attribute['attribute_id'],
                    'name' => strip_tags(html_entity_decode($attribute['name'], ENT_QUOTES, 'UTF-8')),
                    'attribute_group' => $attribute['attribute_group'] ?? 'Без група',
                    'attribute_group_id' => $attribute['attribute_group_id'] ?? 0,
                    'sort_order' => $attribute['sort_order'] ?? 0
                ];
            }

            // Сортиране по име ако има търсене
            if (!empty($filter_name)) {
                usort($json, function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });
            }

        } catch (\Exception $e) {
            // В случай на грешка, връщаме празен резултат
            $json = [];
        }

        return $json;
    }

    /**
     * Автозавършване за атрибути по група
     */
    public function autocompleteByGroup($get) {
        $json = [];

        $attribute_group_id = (int)($get['attribute_group_id'] ?? 0);
        
        if (!$attribute_group_id) {
            return $json;
        }

        // Зареждане на модела за атрибути
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel'
        ]);

        $filter_name = $get['filter_name'] ?? '';
        $limit = (int)($get['limit'] ?? 20);
        $limit = max(1, min(50, $limit));

        $filter_data = [
            'filter_attribute_group_id' => $attribute_group_id,
            'filter_name' => $filter_name,
            'sort' => 'ad.name',
            'order' => 'ASC',
            'start' => 0,
            'limit' => $limit
        ];

        try {
            $attributes = $this->attributeModel->getAttributes($filter_data);

            foreach ($attributes as $attribute) {
                $json[] = [
                    'attribute_id' => $attribute['attribute_id'],
                    'name' => strip_tags(html_entity_decode($attribute['name'], ENT_QUOTES, 'UTF-8')),
                    'attribute_group' => $attribute['attribute_group'] ?? 'Без група',
                    'sort_order' => $attribute['sort_order'] ?? 0
                ];
            }

        } catch (\Exception $e) {
            $json = [];
        }

        return $json;
    }

    /**
     * Търсене на атрибути с разширени опции
     */
    public function search($get) {
        $json = [];

        // Зареждане на модела за атрибути
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel',
            'catalog/attribute_group' => 'attributeGroupModel'
        ]);

        $search_term = $get['search'] ?? '';
        $attribute_group_id = (int)($get['attribute_group_id'] ?? 0);
        $limit = (int)($get['limit'] ?? 15);
        $page = max(1, (int)($get['page'] ?? 1));

        $limit = max(1, min(100, $limit));
        $start = ($page - 1) * $limit;

        $filter_data = [
            'filter_name' => $search_term,
            'sort' => 'ad.name',
            'order' => 'ASC',
            'start' => $start,
            'limit' => $limit
        ];

        if ($attribute_group_id > 0) {
            $filter_data['filter_attribute_group_id'] = $attribute_group_id;
        }

        try {
            $attributes = $this->attributeModel->getAttributes($filter_data);
            $total = $this->attributeModel->getTotalAttributes($filter_data);

            $results = [];
            foreach ($attributes as $attribute) {
                $results[] = [
                    'attribute_id' => $attribute['attribute_id'],
                    'name' => strip_tags(html_entity_decode($attribute['name'], ENT_QUOTES, 'UTF-8')),
                    'attribute_group' => $attribute['attribute_group'] ?? 'Без група',
                    'attribute_group_id' => $attribute['attribute_group_id'] ?? 0,
                    'sort_order' => $attribute['sort_order'] ?? 0
                ];
            }

            $json = [
                'results' => $results,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit)
            ];

        } catch (\Exception $e) {
            $json = [
                'results' => [],
                'total' => 0,
                'page' => 1,
                'limit' => $limit,
                'total_pages' => 0,
                'error' => 'Грешка при търсене на атрибути'
            ];
        }

        return $json;
    }

    /**
     * Получаване на следващата стойност за sort_order в група атрибути
     */
    public function getNextSortOrder() {
        $json = [];

        ob_start();

        try {
            $attribute_group_id = (int)$this->requestGet('attribute_group_id', 0);

            if (!$attribute_group_id) {
                $json = ['error' => 'Невалиден ID на група атрибути'];
            } else {
                // Заявка за максималната стойност на sort_order в групата
                $query = $this->db->query("
                    SELECT MAX(sort_order) as max_sort_order
                    FROM `" . DB_PREFIX . "attribute`
                    WHERE attribute_group_id = '" . (int)$attribute_group_id . "'
                ");

                $max_sort_order = (int)($query->row['max_sort_order'] ?? 0);
                $next_sort_order = $max_sort_order + 1;

                $json = [
                    'success' => true,
                    'next_sort_order' => $next_sort_order,
                    'max_sort_order' => $max_sort_order,
                    'attribute_group_id' => $attribute_group_id
                ];
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при изчисляване на sort_order: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json = ['error' => 'Unexpected output: ' . $output];
        }

        return $json;
    }
}
