<?php

namespace Theme25\Backend\Controller\Catalog\Attribute;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'attribute-listing.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Атрибути');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/attribute');
    }

    /**
     * Подготвя данните за листването на атрибути
     */
    private function prepareData() {
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel',
            'catalog/attribute_group' => 'attributeGroupModel'
        ]);

        // Подготвяне на филтрите
        $this->prepareFilters()
             ->prepareAttributesList()
             ->preparePagination()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя филтрите за търсене
     */
    private function prepareFilters() {
        $filter_name = $this->requestGet('filter_name', '');
        $filter_attribute_group_id = $this->requestGet('filter_attribute_group_id', '');
        $sort = $this->requestGet('sort', 'ad.name');
        $order = $this->requestGet('order', 'ASC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'filter_name' => $filter_name,
            'filter_attribute_group_id' => $filter_attribute_group_id,
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        // Подготвяне на групите атрибути за филтъра
        $attribute_groups = $this->attributeGroupModel->getAttributeGroups();
        $this->setData('attribute_groups', $attribute_groups);

        return $this;
    }

    /**
     * Подготвя списъка с атрибути
     */
    private function prepareAttributesList() {
        $filter_data = [
            'filter_name' => $this->data['filter_name'],
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        if (!empty($this->data['filter_attribute_group_id'])) {
            $filter_data['filter_attribute_group_id'] = $this->data['filter_attribute_group_id'];
        }

        $attributes = $this->attributeModel->getAttributes($filter_data);
        $attribute_total = $this->attributeModel->getTotalAttributes($filter_data);

        // Подготвяне на данните за всеки атрибут
        $attribute_list = [];
        foreach ($attributes as $attribute) {
            $edit_params = 'attribute_id=' . $attribute['attribute_id'];
            $delete_params = 'attribute_id=' . $attribute['attribute_id'];

            $attribute_list[] = [
                'attribute_id' => $attribute['attribute_id'],
                'name' => $attribute['name'],
                'attribute_group' => $attribute['attribute_group'] ?? 'Без група',
                'sort_order' => $attribute['sort_order'],
                'edit_url' => $this->getAdminLink('catalog/attribute/edit', $edit_params),
                'delete_url' => $this->getAdminLink('catalog/attribute/delete', $delete_params)
            ];
        }

        $this->setData([
            'attributes' => $attribute_list,
            'attribute_total' => $attribute_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['attribute_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('catalog/attribute', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('catalog/attribute', $filter_params . '&limit={limit}'));
        $pagination->setProductText('атрибута');

        // Генериране на HTML код за цялата пагинация
        $this->setData('pagination_html', $pagination->render());

        return $this;
    }

    /**
     * Генерира URL параметри за филтрите
     */
    private function buildFilterParams() {
        $params = [];

        // Филтри, които трябва да се запазят в URL
        $filter_fields = [
            'filter_name',
            'filter_attribute_group_id',
            'sort',
            'order'
        ];

        foreach ($filter_fields as $field) {
            if (isset($this->data[$field]) && $this->data[$field] !== '') {
                $params[] = $field . '=' . urlencode($this->data[$field]);
            }
        }

        return $params ? '&' . implode('&', $params) : '';
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        $url_params = [];
        
        if (!empty($this->data['filter_name'])) {
            $url_params['filter_name'] = $this->data['filter_name'];
        }
        
        if (!empty($this->data['filter_attribute_group_id'])) {
            $url_params['filter_attribute_group_id'] = $this->data['filter_attribute_group_id'];
        }

        $this->setData([
            'add_url' => $this->getAdminLink('catalog/attribute/add'),
            'delete_selected_url' => $this->getAdminLink('catalog/attribute/delete'),
            'filter_url' => $this->getAdminLink('catalog/attribute', $url_params),
            'clear_filter_url' => $this->getAdminLink('catalog/attribute')
        ]);

        return $this;
    }

    /**
     * AJAX метод за autocomplete търсене
     */
    public function ajaxSearch() {
        $json = [];

        ob_start();

        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel',
        ]);

        try {
            $filter_name = $this->requestGet('filter_name', '');
            $filter_attribute_group_id = $this->requestGet('filter_attribute_group_id', '');
            $limit = min(10, max(1, (int)$this->requestGet('limit', 10)));

            $filter_data = [
                'filter_name' => $filter_name,
                'start' => 0,
                'limit' => $limit,
                'sort' => 'ad.name',
                'order' => 'ASC'
            ];

            if (!empty($filter_attribute_group_id) && is_numeric($filter_attribute_group_id)) {
                $filter_data['filter_attribute_group_id'] = (int)$filter_attribute_group_id;
            }

            $attributes = $this->attributeModel->getAttributes($filter_data);

            foreach ($attributes as $attribute) {
                $json[] = [
                    'attribute_id' => $attribute['attribute_id'],
                    'name' => strip_tags(html_entity_decode($attribute['name'], ENT_QUOTES, 'UTF-8')),
                    'attribute_group' => $attribute['attribute_group'] ?? 'Без група',
                    'attribute_group_id' => $attribute['attribute_group_id'] ?? 0,
                    'sort_order' => $attribute['sort_order'] ?? 0
                ];
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при търсене: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json = ['error' => 'Unexpected output: ' . $output];
        }

        $this->setJSONResponseOutput($json);
    }
}
