<?php

namespace Theme25\Backend\Controller\Catalog;

class Category extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/category');
		$this->loadScripts();
    }


    /**
     * Главна страница с категории - dispatcher метод
     */
    public function index() {
        $this->setTitle('Категории');
        $this->initAdminData();

        $subController = $this->setBackendSubController('Catalog/Category/Index', $this);
        if ($subController) {
            $subController->execute();
        } else {
            $this->renderTemplateWithDataAndOutput('catalog/category');
        }
    }

    /**
     * Добавяне на нова категория - dispatcher метод
     */
    public function add() {
        $this->setTitle('Добавяне на категория');
        $this->initAdminData();

        $subController = $this->setBackendSubController('Catalog/Category/Edit', $this);
        if ($subController) {
            $subController->execute();
        }
    }

    /**
     * Редактиране на категория - dispatcher метод
     */
    public function edit() {
        $this->setTitle('Редакция на категория');
        $this->initAdminData();

        $subController = $this->setBackendSubController('Catalog/Category/Edit', $this);
        if ($subController) {
            $subController->execute();
        }
    }

    /**
     * Запазване на категория - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Catalog/Category/Save', $this);
        if ($subController) {
            $subController->execute();
        }
    }

    /**
     * Изтриване на категория - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Catalog/Category/Delete', $this);
        if ($subController) {
            $subController->execute();
        }
    }

    /**
     * AJAX автодопълване за категории
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        if (isset($this->request->get['filter_name'])) {
            $this->load->model('catalog/category');

            $filter_data = [
                'filter_name' => $this->request->get['filter_name'],
                'sort' => 'name',
                'order' => 'ASC',
                'start' => 0,
                'limit' => 5
            ];

            $results = $this->model_catalog_category->getCategories($filter_data);

            foreach ($results as $result) {
                $json[] = [
                    'category_id' => $result['category_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Копиране на категория
     */
    public function copy() {
        $category_id = $this->request->get['category_id'] ?? 0;

        if (!$category_id) {
            $this->session->data['error'] = 'Невалиден ID на категория';
            $this->response->redirect($this->url->link('catalog/category', 'user_token=' . $this->session->data['user_token']));
            return;
        }

        $copySubController = $this->setBackendSubController('Catalog/Category/Copy', $this);
        if ($copySubController) {
            $copySubController->execute();
        }
    }

    /**
     * Масово изтриване на категории
     */
    public function deleteMultiple() {
        $deleteSubController = $this->setBackendSubController('Catalog/Category/Delete', $this);
        if ($deleteSubController) {
            $deleteSubController->deleteMultiple();
        }
    }

    /**
     * Преместване на категория (drag & drop)
     */
    public function move() {
        $moveSubController = $this->setBackendSubController('Catalog/Category/Move', $this);
        if ($moveSubController) {
            $moveSubController->execute();
        }
    }
}
