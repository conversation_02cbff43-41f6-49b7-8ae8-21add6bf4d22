<?php

namespace Theme25\Backend\Controller\Catalog\Manufacturer;

class Delete extends \Theme25\ControllerSubMethods {

    public function execute() {
        $json = [];

        try {
            // Проверка на правата за достъп
            if (!$this->user->hasPermission('modify', 'catalog/manufacturer')) {
                $json['error'] = 'Нямате права за изтриване на производители!';
                $this->setJSONResponseOutput($json);
                return;
            }

            $this->loadModelsAs([
                'catalog/manufacturer' => 'manufacturerModel',
                'catalog/product' => 'productModel'
            ]);

            $manufacturer_ids = [];
            
            // Получаване на ID-тата за изтриване
            if ($this->requestGet('manufacturer_id')) {
                $manufacturer_ids[] = (int)$this->requestGet('manufacturer_id');
            } elseif ($this->requestPost('selected')) {
                $manufacturer_ids = array_map('intval', $this->requestPost('selected'));
            }

            if (empty($manufacturer_ids)) {
                $json['error'] = 'Не са избрани производители за изтриване!';
                $this->setJSONResponseOutput($json);
                return;
            }

            $deleted_count = 0;
            $errors = [];

            foreach ($manufacturer_ids as $manufacturer_id) {
                // Проверка дали производителят съществува
                $manufacturer_info = $this->manufacturerModel->getManufacturer($manufacturer_id);
                
                if (!$manufacturer_info) {
                    $errors[] = "Производител с ID {$manufacturer_id} не съществува!";
                    continue;
                }

                // Проверка за свързани продукти
                $product_total = $this->productModel->getTotalProductsByManufacturerId($manufacturer_id);
                
                if ($product_total > 0) {
                    $errors[] = "Производителят '{$manufacturer_info['name']}' не може да бъде изтрит, защото има {$product_total} свързани продукта!";
                    continue;
                }

                // Изтриване на производителя
                if ($this->deleteManufacturer($manufacturer_id)) {
                    $deleted_count++;
                    
                    // Логиране на действието
                    $this->logDeleteAction($manufacturer_id, $manufacturer_info);
                } else {
                    $errors[] = "Грешка при изтриването на производител '{$manufacturer_info['name']}'!";
                }
            }

            // Изчистване на кеша
            $this->clearCache();

            // Подготвяне на отговора
            if ($deleted_count > 0) {
                $json['success'] = "Успешно изтрити {$deleted_count} производители!";
            }

            if (!empty($errors)) {
                $json['warnings'] = $errors;
            }

            if ($deleted_count == 0 && !empty($errors)) {
                $json['error'] = 'Нито един производител не беше изтрит!';
            }

        } catch (Exception $e) {
            $json['error'] = 'Възникна грешка при изтриването: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Изтрива производител и всички свързани данни
     */
    private function deleteManufacturer($manufacturer_id) {
        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");

            // Изтриване на SEO URL
            $this->db->query("DELETE FROM `" . DB_PREFIX . "seo_url` WHERE query = 'manufacturer_id=" . (int)$manufacturer_id . "'");

            // Изтриване от manufacturer_to_store
            $this->db->query("DELETE FROM " . DB_PREFIX . "manufacturer_to_store WHERE manufacturer_id = '" . (int)$manufacturer_id . "'");

            // Изтриване на основния запис
            $this->db->query("DELETE FROM " . DB_PREFIX . "manufacturer WHERE manufacturer_id = '" . (int)$manufacturer_id . "'");

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");

            return true;

        } catch (Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            return false;
        }
    }

    /**
     * Проверява дали производителят може да бъде изтрит
     */
    private function canDeleteManufacturer($manufacturer_id) {
        $restrictions = [];

        // Проверка за продукти
        $product_total = $this->productModel->getTotalProductsByManufacturerId($manufacturer_id);
        if ($product_total > 0) {
            $restrictions[] = [
                'type' => 'products',
                'count' => $product_total,
                'message' => "Има {$product_total} свързани продукта"
            ];
        }

        // Проверка за други възможни ограничения (за бъдещи разширения)
        $this->checkAdditionalRestrictions($manufacturer_id, $restrictions);

        return empty($restrictions) ? true : $restrictions;
    }

    /**
     * Проверява за допълнителни ограничения
     */
    private function checkAdditionalRestrictions($manufacturer_id, &$restrictions) {
        // Тук можем да добавим проверки за други модули
        // които могат да използват производители
        
        // Пример: проверка за промоции (ако има такива)
        /*
        if (method_exists($this, 'model_marketing_promotion')) {
            $promotion_total = $this->model_marketing_promotion->getTotalPromotionsByManufacturerId($manufacturer_id);
            if ($promotion_total > 0) {
                $restrictions[] = [
                    'type' => 'promotions',
                    'count' => $promotion_total,
                    'message' => "Има {$promotion_total} свързани промоции"
                ];
            }
        }
        */
    }

    /**
     * Логира действието за изтриване
     */
    private function logDeleteAction($manufacturer_id, $manufacturer_info) {
        if (method_exists($this, 'log')) {
            $log_data = [
                'action' => 'delete',
                'manufacturer_id' => $manufacturer_id,
                'manufacturer_name' => $manufacturer_info['name'],
                'user_id' => $this->user->getId(),
                'ip' => $this->request->server['REMOTE_ADDR'] ?? '',
                'user_agent' => $this->request->server['HTTP_USER_AGENT'] ?? '',
                'timestamp' => date('Y-m-d H:i:s')
            ];

            $this->log->write('MANUFACTURER_DELETE: ' . json_encode($log_data));
        }
    }

    /**
     * Изчиства кеша свързан с производители
     */
    private function clearCache() {
        if (method_exists($this, 'cache')) {
            $this->cache->delete('manufacturer');
            $this->cache->delete('product');
            $this->cache->delete('seo_url');
        }
    }

    /**
     * Получава детайлна информация за ограниченията
     */
    public function getDeleteRestrictions() {
        $json = [];

        $manufacturer_id = (int)$this->requestGet('manufacturer_id');
        
        if (!$manufacturer_id) {
            $json['error'] = 'Невалиден ID на производител!';
            $this->setJSONResponseOutput($json);
            return;
        }

        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel',
            'catalog/product' => 'productModel'
        ]);

        $manufacturer_info = $this->manufacturerModel->getManufacturer($manufacturer_id);
        
        if (!$manufacturer_info) {
            $json['error'] = 'Производителят не съществува!';
            $this->setJSONResponseOutput($json);
            return;
        }

        $restrictions = $this->canDeleteManufacturer($manufacturer_id);
        
        if ($restrictions === true) {
            $json['can_delete'] = true;
            $json['message'] = 'Производителят може да бъде изтрит безопасно.';
        } else {
            $json['can_delete'] = false;
            $json['restrictions'] = $restrictions;
            $json['message'] = 'Производителят не може да бъде изтрит поради следните ограничения:';
        }

        $this->setJSONResponseOutput($json);
    }
}
