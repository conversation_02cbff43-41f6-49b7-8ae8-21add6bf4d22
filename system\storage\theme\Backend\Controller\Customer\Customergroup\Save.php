<?php

namespace Theme25\Backend\Controller\Customer\Customergroup;

class Save extends \Theme25\ControllerSubMethods {

    private $error = [];

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява запазването на клиентска група
     */
    public function execute() {
        $json = [];

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->load->model('customer/customer_group');

            $customer_group_id = (int)$this->requestPost('customer_group_id', 0);

            if ($customer_group_id) {
                // Редактиране на съществуваща клиентска група
                $this->model_customer_customer_group->editCustomerGroup($customer_group_id, $this->request->post);
                $json['success'] = 'Клиентската група беше успешно обновена!';
            } else {
                // Добавяне на нова клиентска група
                $customer_group_id = $this->model_customer_customer_group->addCustomerGroup($this->request->post);
                $json['success'] = 'Клиентската група беше успешно добавена!';
            }

            $json['redirect'] = $this->getAdminLink('customer/customer_group');
        } else {
            $json['error'] = $this->error;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира формата за клиентска група
     */
    protected function validateForm() {
        if (!$this->hasPermission('modify', 'customer/customer_group')) {
            $this->error['warning'] = 'Нямате права за модифициране на клиентски групи!';
        }

        foreach ($this->requestPost('customer_group_description', []) as $language_id => $value) {
            if ((utf8_strlen($value['name']) < 3) || (utf8_strlen($value['name']) > 32)) {
                $this->error['name'][$language_id] = 'Името на групата трябва да бъде между 3 и 32 символа!';
            }
        }

        return !$this->error;
    }
}
