<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Unlock extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('customers.log');
    }

    /**
     * Изпълнява отключване на клиент
     */
    public function execute() {
        $json = [];
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за отключване на клиенти!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                $customer_info = $this->customerModel->getCustomer($customer_id);
                
                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    // Отключваме клиента като премахваме всички неуспешни опити за логин
                    // В OpenCart стандартната таблица се нарича customer_login, не customer_login_attempt
                    $this->dbQuery("DELETE FROM `" . DB_PREFIX . "customer_login` WHERE email = '" . $this->dbEscape($customer_info['email']) . "'");

                    // Премахваме всички IP блокирания за този клиент (ако таблицата съществува)
                    $this->safeDeleteFromTable('customer_ip', "customer_id = '" . (int)$customer_id . "'");

                    // Премахваме IP адресите на клиента от ban списъка
                    $this->removeCustomerIpsFromBanList($customer_id);

                    // Активираме клиента ако е неактивен
                    if (!$customer_info['status']) {
                        $this->dbQuery("UPDATE `" . DB_PREFIX . "customer` SET status = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                    }

                    // Одобряваме клиента ако не е одобрен
                    if (!$customer_info['approved']) {
                        $this->dbQuery("UPDATE `" . DB_PREFIX . "customer` SET approved = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                    }
                    
                    // Логваме действието
                    $this->loadModelAs('user/user', 'userModel');
                    $user_info = $this->userModel->getUser($this->user->getId());
                    
                    if ($user_info) {
                        $this->writeLog('Администратор ' . $user_info['username'] . ' отключи клиент ' . $customer_info['email']);
                    }
                    
                    $json['success'] = 'Клиентът беше успешно отключен!';
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Безопасно изтрива записи от таблица, ако тя съществува
     */
    private function safeDeleteFromTable($tableName, $whereCondition) {
        try {
            // Проверяваме дали таблицата съществува
            $checkTable = $this->dbQuery("SHOW TABLES LIKE '" . DB_PREFIX . $tableName . "'");

            if ($checkTable->num_rows > 0) {
                $this->dbQuery("DELETE FROM `" . DB_PREFIX . $tableName . "` WHERE " . $whereCondition);
            }
        } catch (Exception $e) {
            // Логваме грешката, но не спираме изпълнението
            $this->writeLog('Unlock: Грешка при изтриване от таблица ' . $tableName . ': ' . $e->getMessage());
        }
    }

    /**
     * Проверява дали таблица съществува в базата данни
     */
    private function tableExists($tableName) {
        try {
            $result = $this->dbQuery("SHOW TABLES LIKE '" . DB_PREFIX . $tableName . "'");
            return $result->num_rows > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Премахва IP адресите на клиента от ban списъка
     */
    private function removeCustomerIpsFromBanList($customer_id) {
        try {
            // Първо проверяваме дали таблиците съществуват
            if (!$this->tableExists('customer_ip') || !$this->tableExists('customer_ban_ip')) {
                return;
            }

            // Вземаме всички IP адреси на клиента
            $customerIps = $this->dbQuery("SELECT ip FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "'");

            // Премахваме всеки IP от ban списъка
            foreach ($customerIps->rows as $row) {
                $this->dbQuery("DELETE FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->dbEscape($row['ip']) . "'");
            }
        } catch (Exception $e) {
            // Логваме грешката, но не спираме изпълнението
            $this->writeLog('Unlock: Грешка при премахване на IP адреси от ban списъка: ' . $e->getMessage());
        }
    }
}
