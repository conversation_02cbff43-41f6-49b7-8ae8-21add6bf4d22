<?php

namespace Theme25\Backend\Controller\Catalog\Attribute;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Изтриване на атрибут(и)
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка за права за модификация
        if (!$this->user->hasPermission('modify', 'catalog/attribute')) {
            $json['error'] = 'Нямате права за изтриване на атрибути';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();
        $attribute_ids = [];

        // Получаване на ID-тата за изтриване
        if (isset($post['selected']) && is_array($post['selected'])) {
            // Множествено изтриване
            $attribute_ids = array_map('intval', $post['selected']);
        } elseif (isset($post['attribute_id'])) {
            // Единично изтриване
            $attribute_ids = [(int)$post['attribute_id']];
        } else {
            $json['error'] = 'Не са избрани атрибути за изтриване';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Филтриране на валидните ID-та
        $attribute_ids = array_filter($attribute_ids, function($id) {
            return $id > 0;
        });

        if (empty($attribute_ids)) {
            $json['error'] = 'Не са избрани валидни атрибути за изтриване';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel',
            'catalog/product' => 'productModel'
        ]);

        try {
            $deleted_count = 0;
            $skipped_attributes = [];

            foreach ($attribute_ids as $attribute_id) {
                // Проверка дали атрибутът съществува
                $attribute_info = $this->attributeModel->getAttribute($attribute_id);
                if (!$attribute_info) {
                    continue;
                }

                // Проверка дали атрибутът се използва от продукти
                if ($this->isAttributeUsedByProducts($attribute_id)) {
                    $skipped_attributes[] = $attribute_info['name'];
                    continue;
                }

                // Изтриване на атрибута
                $this->attributeModel->deleteAttribute($attribute_id);
                $deleted_count++;
            }

            // Подготвяне на отговора
            if ($deleted_count > 0) {
                if ($deleted_count == 1) {
                    $json['success'] = 'Атрибутът беше успешно изтрит';
                } else {
                    $json['success'] = "Бяха изтрити {$deleted_count} атрибута";
                }
            }

            if (!empty($skipped_attributes)) {
                $skipped_list = implode(', ', $skipped_attributes);
                if (isset($json['success'])) {
                    $json['warning'] = "Следните атрибути не бяха изтрити, защото се използват от продукти: {$skipped_list}";
                } else {
                    $json['error'] = "Атрибутите не могат да бъдат изтрити, защото се използват от продукти: {$skipped_list}";
                }
            }

            if ($deleted_count == 0 && empty($skipped_attributes)) {
                $json['error'] = 'Няма атрибути за изтриване';
            }

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при изтриване на атрибутите: ' . $e->getMessage();
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали атрибутът се използва от продукти
     */
    private function isAttributeUsedByProducts($attribute_id) {
        try {
            // Проверка в таблицата product_attribute
            $query = $this->db->query("
                SELECT COUNT(*) as total 
                FROM `" . DB_PREFIX . "product_attribute` 
                WHERE attribute_id = '" . (int)$attribute_id . "'
            ");

            return $query->row['total'] > 0;
        } catch (\Exception $e) {
            // В случай на грешка, считаме че атрибутът се използва за безопасност
            return true;
        }
    }

    /**
     * Получава списък с продукти, които използват атрибута
     */
    private function getProductsUsingAttribute($attribute_id) {
        $products = [];

        try {
            $query = $this->db->query("
                SELECT DISTINCT p.product_id, pd.name 
                FROM `" . DB_PREFIX . "product_attribute` pa
                LEFT JOIN `" . DB_PREFIX . "product` p ON (pa.product_id = p.product_id)
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE pa.attribute_id = '" . (int)$attribute_id . "'
                AND pd.language_id = '" . (int)$this->getLanguageId() . "'
                LIMIT 10
            ");

            foreach ($query->rows as $row) {
                $products[] = $row['name'];
            }
        } catch (\Exception $e) {
            // В случай на грешка, връщаме празен масив
        }

        return $products;
    }
}
