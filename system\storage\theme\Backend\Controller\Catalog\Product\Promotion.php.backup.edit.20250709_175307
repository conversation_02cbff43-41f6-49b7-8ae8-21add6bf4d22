<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Promotion extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    public function execute() {
        $this->setTitle('Промоции');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-promotion.js', 'footer');

        // Създаваме таблицата ако не съществува
        $this->createPromotionsTable();

        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_promotion');
    }

    public function prepareData() {
        // Зареждаме всички промоции за показване в листинга
        $promotions = $this->getAllPromotions();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product/promotion'),
            'promotions' => $promotions,
            // 'user_token' => $this->session->data['user_token'] ?? ''
        ]);
    }

    /**
     * Обработва AJAX заявките за промоции
     */
    public function submit() {
        $json = [];

        ob_start();
    
        $json = $this->save($this->requestPost());
    
        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Страница за добавяне на нова промоция
     */
    public function add() {
        $this->setTitle('Добавяне на промоция');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-promotion.js', 'footer');

        // Създаваме таблицата ако не съществува
        // $this->createPromotionsTable();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product/promotion'),
            'action_url' => $this->getAdminLink('catalog/product/promotion/submit')
        ]);

        $this->renderTemplateWithDataAndOutput('catalog/product_promotion_add');
    }
    
    /**
     * Обработва AJAX заявката за запазване на промоция
     * 
     * @param array $post POST данни от заявката
     * @return array
     */
    public function save($post) {
        $json = [];

        try {
            // Проверка за валидна AJAX заявка
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }
            
            // Валидация на входните данни
            $this->validatePromotionData($post);

            
            // Подготвяне на данните за промоцията
            $promotionData = $this->preparePromotionData($post);
            
            // Запазване на промоцията
            $this->savePromotion($promotionData);
            
            $json['success'] = 'Промоцията беше успешно приложена!';
            
        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        return $json;
    }
    
    /**
     * Валидира данните за промоцията
     *
     * @param array $post
     * @throws \Exception
     */
    private function validatePromotionData($post) {
        // Проверка на стойността
        if (empty($post['promotion_value'])) {
            throw new \Exception('Моля въведете стойност');
        }

        $value = (float)$post['promotion_value'];
        if ($value <= 0) {
            throw new \Exception('Стойността трябва да бъде по-голяма от 0');
        }

        // Проверка на типа
        if (empty($post['promotion_type']) || !in_array($post['promotion_type'], ['percentage', 'fixed'])) {
            throw new \Exception('Невалиден тип промоция');
        }

        // Специфична валидация за процент
        if ($post['promotion_type'] === 'percentage' && $value > 100) {
            throw new \Exception('Процентът не може да бъде по-голям от 100');
        }
        
        // Проверка на датите - поне една дата трябва да бъде въведена
        $hasStartDate = !empty($post['promotion_date_start']);
        $hasEndDate = !empty($post['promotion_date_end']);

        if (!$hasStartDate && !$hasEndDate) {
            throw new \Exception('Моля въведете поне една дата (начална или крайна)');
        }

        $startDate = null;
        $endDate = null;

        // Валидация на началната дата, ако е въведена
        if ($hasStartDate) {
            $startDate = strtotime($post['promotion_date_start']);
            if ($startDate === false) {
                throw new \Exception('Невалиден формат на началната дата');
            }
            if ($startDate < strtotime('today')) {
                throw new \Exception('Началната дата не може да бъде в миналото');
            }
        }

        // Валидация на крайната дата, ако е въведена
        if ($hasEndDate) {
            $endDate = strtotime($post['promotion_date_end']);
            if ($endDate === false) {
                throw new \Exception('Невалиден формат на крайната дата');
            }
        }

        // Ако и двете дати са въведени, проверяваме реда им
        if ($hasStartDate && $hasEndDate && $startDate >= $endDate) {
            throw new \Exception('Крайната дата трябва да бъде след началната');
        }
        
        // Проверка дали има избрана категория или продукти
        $hasCategory = !empty($post['promotion_category_id']);
        $hasProducts = !empty($post['selected_products']) && is_array($post['selected_products']);

        if (!$hasCategory && !$hasProducts) {
            throw new \Exception('Моля изберете категория или конкретни продукти за промоцията');
        }

        $this->loadModelsAs([
                'catalog/product' => 'productModel',
                'catalog/category' => 'categoryModel'
            ]);

        // Валидация на категорията, ако е избрана
        if ($hasCategory) {    
            $category = $this->categoryModel->getCategory((int)$post['promotion_category_id']);
            if (!$category) {
                throw new \Exception('Избраната категория не съществува');
            }
        }

        // Валидация на продуктите, ако са избрани
        if ($hasProducts) {
            $productIds = $post['selected_products'];
            if (!is_array($productIds)) {
                throw new \Exception('Невалиден формат на продуктите');
            }

            foreach ($productIds as $productId) {
                $product = $this->productModel->getProduct((int)$productId);
                if (!$product) {
                    throw new \Exception('Един от избраните продукти не съществува');
                }
            }
        }
    }
    
    /**
     * Подготвя данните за промоцията
     *
     * @param array $post
     * @return array
     */
    private function preparePromotionData($post) {
        $data = [
            'value' => (float)$post['promotion_value'],
            'type' => $post['promotion_type'],
            'date_start' => !empty($post['promotion_date_start']) ? $post['promotion_date_start'] : null,
            'date_end' => !empty($post['promotion_date_end']) ? $post['promotion_date_end'] : null,
            'category_id' => !empty($post['promotion_category_id']) ? (int)$post['promotion_category_id'] : null,
            'product_ids' => !empty($post['selected_products']) ? $post['selected_products'] : [],
            'created_by' => $this->getUserId(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $data;
    }
    
    /**
     * Запазва промоцията в базата данни
     *
     * @param array $data
     */
    private function savePromotion($data) {
        if (empty($data)) {
            throw new \Exception('Няма данни за запазване');
        }

        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");

            // 1. Създаваме таблицата ако не съществува
            $this->createPromotionsTable();

            // 2. Записваме промоцията в таблицата promotions с JSON данни
            $promotionId = $this->savePromotionToPromotionsTable($data);

            // 3. Получаваме пълните данни за промоцията (включително JSON данните)
            $promotion = $this->getPromotionById($promotionId);

            // 4. Синхронизираме с product_special таблицата
            $this->activatePromotionInProductSpecial($promotion);

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");

            // Логиране на действието
            $this->logPromotionAction($data);

        } catch (\Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            F()->log->developer("Грешка при запазване на промоция: " . $e->getMessage(), __FILE__, __LINE__);
            throw $e;
        }
    }











    /**
     * Логира действието за промоция
     *
     * @param array $data
     */
    private function logPromotionAction($data) {
        $discountText = $data['type'] === 'percentage'
            ? $data['value'] . '% отстъпка'
            : $data['value'] . ' лв. отстъпка';

        // Формираме съобщението за периода
        $periodText = '';
        if ($data['date_start'] && $data['date_end']) {
            $periodText = "от {$data['date_start']} до {$data['date_end']}";
        } elseif ($data['date_start']) {
            $periodText = "от {$data['date_start']}";
        } elseif ($data['date_end']) {
            $periodText = "до {$data['date_end']}";
        }

        $logMessage = sprintf(
            'Приложена промоция: %s %s',
            $discountText,
            $periodText
        );

        if ($data['category_id']) {
            $logMessage .= ' за категория ID: ' . $data['category_id'];
        }

        if (!empty($data['product_ids'])) {
            $logMessage .= ' за продукти: ' . implode(', ', $data['product_ids']);
        }

        F()->log->developer($logMessage, __FILE__, __LINE__);
    }
    
    /**
     * Получава ID на текущия потребител
     *
     * @return int
     */
    private function getUserId() {
        // Опитваме се да получим потребителския ID от сесията
        if (isset($this->session->data['user_id'])) {
            return (int)$this->session->data['user_id'];
        }

        // Ако няма сесия, връщаме 1 като default
        return 1;
    }











    /**
     * Създава таблицата за промоции ако не съществува и добавя нужните колони
     */
    private function createPromotionsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "promotions` (
            `promotion_id` INT(11) NOT NULL AUTO_INCREMENT,
            `data` TEXT NULL COMMENT 'JSON данни за промоцията',
            `discount_type` ENUM('percentage', 'fixed') NOT NULL,
            `discount_value` DECIMAL(10,2) NOT NULL,
            `start_date` DATE NOT NULL,
            `end_date` DATE NOT NULL,
            `status` TINYINT(1) DEFAULT 1,
            `date_added` DATETIME DEFAULT CURRENT_TIMESTAMP,
            `date_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`promotion_id`),
            KEY `idx_status` (`status`),
            KEY `idx_dates` (`start_date`, `end_date`),
            KEY `idx_product_status` (`product_id`, `status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci";

        try {
            $this->db->query($sql);
            // F()->log->developer("Таблицата promotions е създадена или вече съществува", __FILE__, __LINE__);

            // Проверяваме дали колоната data съществува и я добавяме ако не
            // $this->addDataColumnIfNotExists();

        } catch (\Exception $e) {
            F()->log->developer("Грешка при създаване на таблицата promotions: " . $e->getMessage(), __FILE__, __LINE__);
        }
    }

    /**
     * Добавя колоната data ако не съществува
     */
    private function addDataColumnIfNotExists() {
        try {
            // Проверяваме дали колоната data съществува
            $checkSql = "SHOW COLUMNS FROM `" . DB_PREFIX . "promotions` LIKE 'data'";
            $result = $this->db->query($checkSql);

            if ($result->num_rows == 0) {
                // Колоната не съществува, добавяме я
                $addColumnSql = "ALTER TABLE `" . DB_PREFIX . "promotions`
                                ADD COLUMN `data` TEXT NULL COMMENT 'JSON данни за промоцията'
                                AFTER `product_id`";

                $this->db->query($addColumnSql);
                F()->log->developer("Добавена колона data в таблицата promotions", __FILE__, __LINE__);
            }

            // Модифицираме product_id да може да бъде NULL
            $modifyProductIdSql = "ALTER TABLE `" . DB_PREFIX . "promotions`
                                  MODIFY COLUMN `product_id` INT(11) DEFAULT NULL COMMENT 'ID на продукта (NULL за групови промоции)'";

            $this->db->query($modifyProductIdSql);
            F()->log->developer("Модифицирана колона product_id да позволява NULL стойности", __FILE__, __LINE__);

        } catch (\Exception $e) {
            F()->log->developer("Грешка при добавяне на колона data: " . $e->getMessage(), __FILE__, __LINE__);
        }
    }

    /**
     * Получава всички промоции с декодирани JSON данни
     *
     * @return array
     */
    private function getAllPromotions() {
        $sql = "SELECT
                    p.promotion_id,
                    p.data,
                    p.discount_type,
                    p.discount_value,
                    p.start_date,
                    p.end_date,
                    p.status,
                    p.date_added,
                    p.date_modified
                FROM `" . DB_PREFIX . "promotions` p
                ORDER BY p.date_added DESC";

        try {
            $result = $this->db->query($sql);
            $promotions = [];

            foreach ($result->rows as $row) {
                $promotion = $row;

                // Декодираме JSON данните
                $jsonData = json_decode($row['data'], true);
                if ($jsonData) {
                    $promotion['json_data'] = $jsonData;
                    $promotion['category_id'] = $jsonData['category_id'] ?? null;
                    $promotion['category_name'] = $jsonData['category_name'] ?? null;
                    $promotion['product_ids'] = $jsonData['product_ids'] ?? [];
                    $promotion['product_names'] = $jsonData['product_names'] ?? [];

                    // Подготвяме описанието на промоцията
                    $promotion['promotion_description'] = $this->buildPromotionDescription($jsonData);
                    $promotion['products_count'] = count($jsonData['product_ids'] ?? []);
                } else {
                    // За стари записи без JSON данни
                    $promotion['json_data'] = null;
                    $promotion['category_id'] = null;
                    $promotion['category_name'] = null;
                    $promotion['product_ids'] = $row['product_id'] ? [$row['product_id']] : [];
                    $promotion['product_names'] = [];
                    $promotion['promotion_description'] = 'Стар запис (без JSON данни)';
                    $promotion['products_count'] = $row['product_id'] ? 1 : 0;

                    // Ако има product_id, получаваме името на продукта
                    // if ($row['product_id']) {
                    //     $productName = $this->getProductNameById($row['product_id']);
                    //     $promotion['product_names'] = [$productName];
                    //     $promotion['promotion_description'] = $productName;
                    // }
                }

                $promotions[] = $promotion;
            }

            return $promotions;
        } catch (\Exception $e) {
            F()->log->developer("Грешка при получаване на промоции: " . $e->getMessage(), __FILE__, __LINE__);
            return [];
        }
    }

    /**
     * Страница за редактиране на промоция
     */
    public function edit() {
        $promotion_id = (int)$this->requestGet('promotion_id');

        if (!$promotion_id) {
            $this->redirect($this->getAdminLink('catalog/product/promotion'));
            return;
        }

        $this->setTitle('Редактиране на промоция');
        $this->initAdminData();
        $this->addBackendScriptWithVersion('product-promotion.js', 'footer');

        // Създаваме таблицата ако не съществува
        $this->createPromotionsTable();

        // Получаваме данните за промоцията
        $promotion = $this->getPromotionById($promotion_id);

        if (!$promotion) {
            $this->redirect($this->getAdminLink('catalog/product/promotion'));
            return;
        }

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product/promotion'),
            // 'user_token' => $this->session->data['user_token'] ?? '',
            'action_url' => $this->getAdminLink('catalog/product/promotion/update'),
            'promotion' => $promotion
        ]);

        $this->renderTemplateWithDataAndOutput('catalog/product_promotion_edit');
    }

    /**
     * Обработва AJAX заявки за обновяване на промоция
     */
    public function update() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestPost('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            // Валидация на входните данни
            $this->validatePromotionData($this->requestPost());

            // Подготвяне на данните за промоцията
            $promotionData = $this->preparePromotionData($this->requestPost());
            $promotionData['promotion_id'] = $promotion_id;

            // Обновяване на промоцията
            $this->updatePromotion($promotionData);

            $json['success'] = 'Промоцията беше успешно обновена!';

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }



    /**
     * Обработва AJAX заявки за изтриване на промоция
     */
    public function delete() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestPost('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            // Изтриване на промоцията
            $this->deletePromotion($promotion_id);

            $json['success'] = 'Промоцията беше успешно изтрита!';

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва AJAX заявки за промяна на статуса на промоция
     */
    public function toggleStatus() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestPost('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            // Промяна на статуса
            $newStatus = $this->togglePromotionStatus($promotion_id);

            $json['success'] = $newStatus ? 'Промоцията беше активирана!' : 'Промоцията беше деактивирана!';
            $json['new_status'] = $newStatus;

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава данни за конкретна промоция
     */
    public function getPromotion() {
        $json = [];

        try {
            if (!$this->isAjaxRequest()) {
                throw new \Exception('Невалидна заявка');
            }

            $promotion_id = (int)$this->requestGet('promotion_id');
            if (!$promotion_id) {
                throw new \Exception('Невалиден ID на промоция');
            }

            $promotion = $this->getPromotionById($promotion_id);
            if (!$promotion) {
                throw new \Exception('Промоцията не е намерена');
            }

            $json['promotion'] = $promotion;

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обновява промоция в базата данни
     *
     * @param array $data
     */
    private function updatePromotion($data) {
        $sql = "UPDATE `" . DB_PREFIX . "promotions` SET
                `discount_type` = '{$data['type']}',
                `discount_value` = '{$data['value']}',
                `start_date` = '{$data['date_start']}',
                `end_date` = '{$data['date_end']}',
                `date_modified` = NOW()
                WHERE `promotion_id` = '{$data['promotion_id']}'";

        $this->db->query($sql);

        // Обновяваме и записа в product_special таблицата
        $this->updateProductSpecial($data);

        F()->log->developer("Обновена промоция с ID: {$data['promotion_id']}", __FILE__, __LINE__);
    }

    /**
     * Изтрива промоция от базата данни
     *
     * @param int $promotion_id
     */
    private function deletePromotion($promotion_id) {
        // Първо получаваме данните за промоцията
        $promotion = $this->getPromotionById($promotion_id);
        if (!$promotion) {
            throw new \Exception('Промоцията не е намерена');
        }

        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");

            // Първо деактивираме промоцията в product_special
            $this->deactivatePromotionInProductSpecial($promotion);

            // След това изтриваме от promotions таблицата
            $sql = "DELETE FROM `" . DB_PREFIX . "promotions` WHERE `promotion_id` = '{$promotion_id}'";
            $this->db->query($sql);

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");

            F()->log->developer("Изтрита промоция с ID: {$promotion_id}", __FILE__, __LINE__);

        } catch (\Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            F()->log->developer("Грешка при изтриване на промоция: " . $e->getMessage(), __FILE__, __LINE__);
            throw $e;
        }
    }

    /**
     * Променя статуса на промоция и синхронизира с product_special таблицата
     *
     * @param int $promotion_id
     * @return int Новия статус
     */
    private function togglePromotionStatus($promotion_id) {
        // Получаваме пълните данни за промоцията
        $promotion = $this->getPromotionById($promotion_id);

        if (!$promotion) {
            throw new \Exception('Промоцията не е намерена');
        }

        $currentStatus = (int)$promotion['status'];
        $newStatus = $currentStatus ? 0 : 1;

        try {
            // Започваме транзакция
            $this->db->query("START TRANSACTION");

            // Обновяваме статуса в promotions таблицата
            $sql = "UPDATE `" . DB_PREFIX . "promotions` SET
                    `status` = '{$newStatus}',
                    `date_modified` = NOW()
                    WHERE `promotion_id` = '{$promotion_id}'";

            $this->db->query($sql);

            // Синхронизираме с product_special таблицата
            if ($newStatus == 1) {
                // Активираме промоцията - добавяме в product_special
                $this->activatePromotionInProductSpecial($promotion);
            } else {
                // Деактивираме промоцията - премахваме от product_special
                $this->deactivatePromotionInProductSpecial($promotion);
            }

            // Потвърждаваме транзакцията
            $this->db->query("COMMIT");

            F()->log->developer("Променен статус на промоция {$promotion_id}: {$currentStatus} -> {$newStatus} и синхронизирано с product_special", __FILE__, __LINE__);

            return $newStatus;

        } catch (\Exception $e) {
            // Отменяме транзакцията при грешка
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * Получава промоция по ID с декодирани JSON данни
     *
     * @param int $promotion_id
     * @return array|false
     */
    private function getPromotionById($promotion_id) {
        $sql = "SELECT * FROM `" . DB_PREFIX . "promotions`
                WHERE promotion_id = '{$promotion_id}'";

        $result = $this->db->query($sql);

        if ($result->num_rows == 0) {
            return false;
        }

        $promotion = $result->row;

        // Декодираме JSON данните
        $jsonData = json_decode($promotion['data'], true);
        if ($jsonData) {
            $promotion['json_data'] = $jsonData;
            $promotion['category_id'] = $jsonData['category_id'] ?? null;
            $promotion['category_name'] = $jsonData['category_name'] ?? null;
            $promotion['product_ids'] = $jsonData['product_ids'] ?? [];
            $promotion['product_names'] = $jsonData['product_names'] ?? [];
            $promotion['promotion_description'] = $this->buildPromotionDescription($jsonData);
            $promotion['products_count'] = count($jsonData['product_ids'] ?? []);
        } else {
            // За стари записи без JSON данни
            $promotion['json_data'] = null;
            $promotion['category_id'] = null;
            $promotion['category_name'] = null;
            $promotion['product_ids'] = $promotion['product_id'] ? [$promotion['product_id']] : [];
            $promotion['product_names'] = [];
            $promotion['promotion_description'] = 'Стар запис (без JSON данни)';
            $promotion['products_count'] = $promotion['product_id'] ? 1 : 0;

            // Ако има product_id, получаваме името на продукта
            if ($promotion['product_id']) {
                $productName = $this->getProductNameById($promotion['product_id']);
                $promotion['product_names'] = [$productName];
                $promotion['promotion_description'] = $productName;

                // Получаваме и допълнителна информация за продукта
                $productInfo = $this->getProductInfoById($promotion['product_id']);
                if ($productInfo) {
                    $promotion['product_name'] = $productInfo['name'];
                    $promotion['product_model'] = $productInfo['model'];
                    $promotion['product_price'] = $productInfo['price'];
                }
            }
        }

        return $promotion;
    }







    /**
     * Записва промоцията в таблицата promotions като един запис с JSON данни
     *
     * @param array $data
     */
    private function savePromotionToPromotionsTable($data) {
        // Подготвяме JSON данните
        $jsonData = $this->preparePromotionJsonData($data);

        // Записваме един запис в таблицата promotions
        $sql = "INSERT INTO `" . DB_PREFIX . "promotions` SET
                `data` = '{$this->db->escape(json_encode($jsonData))}',
                `discount_type` = '{$data['type']}',
                `discount_value` = '{$data['value']}',
                `start_date` = '{$data['date_start']}',
                `end_date` = '{$data['date_end']}',
                `status` = '1',
                `date_added` = NOW()";

        $this->db->query($sql);
        $promotionId = $this->db->getLastId();

        F()->log->developer("Записана промоция с ID: {$promotionId} и JSON данни: " . json_encode($jsonData), __FILE__, __LINE__);

        return $promotionId;
    }

    /**
     * Подготвя JSON данните за промоцията
     *
     * @param array $data
     * @return array
     */
    private function preparePromotionJsonData($data) {
        $jsonData = [
            'category_id' => null,
            'product_ids' => [],
            'category_name' => null,
            'product_names' => []
        ];

        // Ако има избрана категория
        if (!empty($data['category_id'])) {
            $jsonData['category_id'] = (int)$data['category_id'];

            // Получаваме името на категорията
            $categoryName = $this->getCategoryName($data['category_id']);
            $jsonData['category_name'] = $categoryName;

            // Получаваме всички продукти в категорията
            $categoryProducts = $this->getProductsInCategory($data['category_id']);
            $jsonData['product_ids'] = array_column($categoryProducts, 'product_id');
            $jsonData['product_names'] = array_column($categoryProducts, 'name');
        }

        // Ако има конкретни продукти
        if (!empty($data['product_ids'])) {
            // Ако няма категория, използваме само избраните продукти
            if (empty($data['category_id'])) {
                $jsonData['product_ids'] = array_map('intval', $data['product_ids']);
                $jsonData['product_names'] = $this->getProductNames($data['product_ids']);
            } else {
                // Ако има и категория, добавяме избраните продукти към тези от категорията
                $additionalProductIds = array_map('intval', $data['product_ids']);
                $additionalProductNames = $this->getProductNames($additionalProductIds);

                $jsonData['product_ids'] = array_unique(array_merge($jsonData['product_ids'], $additionalProductIds));
                $jsonData['product_names'] = array_unique(array_merge($jsonData['product_names'], $additionalProductNames));
            }
        }

        return $jsonData;
    }

    /**
     * Получава името на категория по ID
     *
     * @param int $category_id
     * @return string
     */
    private function getCategoryName($category_id) {
        $sql = "SELECT name FROM `" . DB_PREFIX . "category_description`
                WHERE category_id = '{$category_id}'
                AND language_id = '{$this->getLanguageId()}'";

        $result = $this->db->query($sql);

        return $result->num_rows > 0 ? $result->row['name'] : '';
    }

    /**
     * Получава всички продукти в категория
     *
     * @param int $category_id
     * @return array
     */
    private function getProductsInCategory($category_id) {
        $sql = "SELECT p.product_id, pd.name
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_to_category` ptc ON (p.product_id = ptc.product_id)
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE ptc.category_id = '{$category_id}'
                AND p.status = '1'
                AND pd.language_id = '{$this->getLanguageId()}'";

        $result = $this->db->query($sql);

        return $result->rows;
    }

    /**
     * Получава имената на продукти по техните ID-та
     *
     * @param array $product_ids
     * @return array
     */
    private function getProductNames($product_ids) {
        if (empty($product_ids)) {
            return [];
        }

        $productIdsStr = implode(',', array_map('intval', $product_ids));

        $sql = "SELECT name FROM `" . DB_PREFIX . "product_description`
                WHERE product_id IN ({$productIdsStr})
                AND language_id = '{$this->getLanguageId()}'
                ORDER BY name";

        $result = $this->db->query($sql);

        return array_column($result->rows, 'name');
    }

    /**
     * Изгражда описанието на промоцията от JSON данните
     *
     * @param array $jsonData
     * @return string
     */
    private function buildPromotionDescription($jsonData) {
        $description = '';

        if (!empty($jsonData['category_name'])) {
            $description = "Категория: " . $jsonData['category_name'];

            if (!empty($jsonData['product_names'])) {
                $productCount = count($jsonData['product_names']);
                $description .= " ({$productCount} продукта)";
            }
        } elseif (!empty($jsonData['product_names'])) {
            $productCount = count($jsonData['product_names']);

            if ($productCount == 1) {
                $description = $jsonData['product_names'][0];
            } elseif ($productCount <= 3) {
                $description = implode(', ', $jsonData['product_names']);
            } else {
                $firstThree = array_slice($jsonData['product_names'], 0, 3);
                $description = implode(', ', $firstThree) . " и още " . ($productCount - 3) . " продукта";
            }
        } else {
            $description = 'Няма данни за продукти';
        }

        return $description;
    }

    /**
     * Получава името на продукт по ID
     *
     * @param int $product_id
     * @return string
     */
    private function getProductNameById($product_id) {
        $sql = "SELECT name FROM `" . DB_PREFIX . "product_description`
                WHERE product_id = '{$product_id}'
                AND language_id = '{$this->getLanguageId()}'";

        $result = $this->db->query($sql);

        return $result->num_rows > 0 ? $result->row['name'] : 'Неизвестен продукт';
    }

    /**
     * Получава пълна информация за продукт по ID
     *
     * @param int $product_id
     * @return array|false
     */
    private function getProductInfoById($product_id) {
        $sql = "SELECT
                    p.product_id,
                    p.model,
                    p.price,
                    pd.name
                FROM `" . DB_PREFIX . "product` p
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE p.product_id = '{$product_id}'
                AND pd.language_id = '{$this->getLanguageId()}'";

        $result = $this->db->query($sql);

        return $result->num_rows > 0 ? $result->row : false;
    }

    /**
     * Получава списък с ID-та на продуктите от промоцията
     *
     * @param array $promotion
     * @return array
     */
    private function getProductIdsFromPromotion($promotion) {
        // Ако има JSON данни, използваме тях
        if (!empty($promotion['product_ids'])) {
            return $promotion['product_ids'];
        }

        // Ако има JSON данни в полето data, декодираме ги
        if (!empty($promotion['data'])) {
            $jsonData = json_decode($promotion['data'], true);
            if ($jsonData && !empty($jsonData['product_ids'])) {
                return $jsonData['product_ids'];
            }
        }

        // За стари записи с product_id
        if (!empty($promotion['product_id'])) {
            return [$promotion['product_id']];
        }

        return [];
    }

    /**
     * Изтрива записи от product_special за конкретна промоция
     *
     * @param int $product_id
     * @param string $start_date
     * @param string $end_date
     */
    private function deleteProductSpecialForPromotion($product_id, $start_date, $end_date) {
        $sql = "DELETE FROM `" . DB_PREFIX . "product_special`
                WHERE `product_id` = '{$product_id}'
                AND `date_start` = '{$start_date}'
                AND `date_end` = '{$end_date}'";

        $this->db->query($sql);

        F()->log->developer("Изтрити записи от product_special за продукт {$product_id} в период {$start_date} - {$end_date}", __FILE__, __LINE__);
    }



    /**
     * Активира промоция в product_special таблицата за всички продукти от JSON данните
     *
     * @param array $promotion Данни за промоцията
     */
    private function activatePromotionInProductSpecial($promotion) {
        // Получаваме продуктите от JSON данните
        $productIds = $this->getProductIdsFromPromotion($promotion);

        if (empty($productIds)) {
            F()->log->developer("Няма продукти за активиране в промоция {$promotion['promotion_id']}", __FILE__, __LINE__);
            return;
        }

        $activatedCount = 0;

        foreach ($productIds as $productId) {
            try {
                // Първо премахваме съществуващи записи за този продукт в този период
                $this->deleteProductSpecialForPromotion($productId, $promotion['start_date'], $promotion['end_date']);

                // Получаваме оригиналната цена на продукта
                $sql = "SELECT price FROM `" . DB_PREFIX . "product` WHERE product_id = '{$productId}'";
                $result = $this->db->query($sql);

                if ($result->num_rows == 0) {
                    F()->log->developer("Продукт {$productId} не е намерен", __FILE__, __LINE__);
                    continue;
                }

                $originalPrice = $result->row['price'];

                // Изчисляваме промоционалната цена
                if ($promotion['discount_type'] === 'percentage') {
                    $specialPrice = $originalPrice * (1 - $promotion['discount_value'] / 100);
                } else {
                    $specialPrice = max(0, $originalPrice - $promotion['discount_value']);
                }

                // Форматираме датите
                $dateStart = $promotion['start_date'] ? $promotion['start_date'] : '0000-00-00';
                $dateEnd = $promotion['end_date'] ? $promotion['end_date'] : '0000-00-00';

                // Добавяме записа в product_special
                $sql = "INSERT INTO `" . DB_PREFIX . "product_special` SET
                        `product_id` = '{$productId}',
                        `customer_group_id` = '1',
                        `priority` = '1',
                        `price` = '{$specialPrice}',
                        `date_start` = '{$dateStart}',
                        `date_end` = '{$dateEnd}'";

                $this->db->query($sql);
                $activatedCount++;

                F()->log->developer("Активирана промоция за продукт {$productId}: {$originalPrice} -> {$specialPrice}", __FILE__, __LINE__);

            } catch (\Exception $e) {
                F()->log->developer("Грешка при активиране на промоция за продукт {$productId}: " . $e->getMessage(), __FILE__, __LINE__);
            }
        }

        F()->log->developer("Активирани промоции за {$activatedCount} от " . count($productIds) . " продукта", __FILE__, __LINE__);
    }

    /**
     * Деактивира промоция в product_special таблицата за всички продукти от JSON данните
     *
     * @param array $promotion Данни за промоцията
     */
    private function deactivatePromotionInProductSpecial($promotion) {
        // Получаваме продуктите от JSON данните
        $productIds = $this->getProductIdsFromPromotion($promotion);

        if (empty($productIds)) {
            F()->log->developer("Няма продукти за деактивиране в промоция {$promotion['promotion_id']}", __FILE__, __LINE__);
            return;
        }

        $deactivatedCount = 0;

        foreach ($productIds as $productId) {
            try {
                // Премахваме записите от product_special за този продукт в този период
                $this->deleteProductSpecialForPromotion($productId, $promotion['start_date'], $promotion['end_date']);
                $deactivatedCount++;

            } catch (\Exception $e) {
                F()->log->developer("Грешка при деактивиране на промоция за продукт {$productId}: " . $e->getMessage(), __FILE__, __LINE__);
            }
        }

        F()->log->developer("Деактивирани промоции за {$deactivatedCount} от " . count($productIds) . " продукта", __FILE__, __LINE__);
    }
}
