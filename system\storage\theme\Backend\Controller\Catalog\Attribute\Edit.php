<?php

namespace Theme25\Backend\Controller\Catalog\Attribute;

class Edit extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'attribute-form.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Редактиране на атрибут');
        
        // Инициализиране на данните
        $this->initAdminData();
        
        $this->prepareAttributeForm();
        
        $this->renderTemplateWithDataAndOutput('catalog/attribute_form');
    }
    
    /**
     * Подготвя формата за редактиране на атрибут
     */
    private function prepareAttributeForm() {
        $attribute_id = (int)$this->requestGet('attribute_id', 0);
        
        if (!$attribute_id) {
            // Пренасочване към списъка ако няма ID
            $this->response->redirect($this->getAdminLink('catalog/attribute'));
            return;
        }

        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel',
            'catalog/attribute_group' => 'attributeGroupModel',
            'localisation/language' => 'languageModel'
        ]);

        // Зареждане на данните за атрибута
        $attribute_info = $this->attributeModel->getAttribute($attribute_id);
        
        if (!$attribute_info) {
            // Пренасочване към списъка ако атрибутът не съществува
            $this->response->redirect($this->getAdminLink('catalog/attribute'));
            return;
        }

        // Подготвяне на данните
        $this->prepareBasicData($attribute_info)
             ->prepareLanguageData($attribute_id)
             ->prepareAttributeGroupData()
             ->prepareFormUrls($attribute_id);

        return $this;
    }

    /**
     * Подготвя основните данни за формата
     */
    private function prepareBasicData($attribute_info) {
        $this->setData([
            'attribute_id' => $attribute_info['attribute_id'],
            'sort_order' => $attribute_info['sort_order'],
            'attribute_group_id' => $attribute_info['attribute_group_id']
        ]);

        return $this;
    }

    /**
     * Подготвя данните за езиците
     */
    private function prepareLanguageData($attribute_id) {
        $languages = $this->languages;
        $this->setData('languages', $languages);

        // Зареждане на описанията за всички езици
        $attribute_descriptions = $this->attributeModel->getAttributeDescriptions($attribute_id);
        
        // Подготвяне на описанията за всички езици
        $attribute_description = [];
        foreach ($languages as $language) {
            $attribute_description[$language['language_id']] = [
                'name' => $attribute_descriptions[$language['language_id']]['name'] ?? ''
            ];
        }
        
        $this->setData('attribute_description', $attribute_description);

        return $this;
    }

    /**
     * Подготвя данните за групите атрибути
     */
    private function prepareAttributeGroupData() {
        $attribute_groups = $this->attributeGroupModel->getAttributeGroups();
        
        // Подготвяне на данните за dropdown
        $attribute_group_options = [];
        foreach ($attribute_groups as $group) {
            $attribute_group_options[] = [
                'attribute_group_id' => $group['attribute_group_id'],
                'name' => $group['name']
            ];
        }
        
        $this->setData('attribute_groups', $attribute_group_options);

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls($attribute_id) {
        $this->setData([
            'action' => $this->getAdminLink('catalog/attribute/save'),
            'cancel' => $this->getAdminLink('catalog/attribute'),
            'back_url' => $this->getAdminLink('catalog/attribute'),
            'delete_url' => $this->getAdminLink('catalog/attribute/delete', ['attribute_id' => $attribute_id])
        ]);

        return $this;
    }

    /**
     * Валидира данните от формата
     */
    public function validateForm($data) {
        $errors = [];

        // Проверка за ID на атрибута при редактиране
        if (empty($data['attribute_id']) || !is_numeric($data['attribute_id'])) {
            $errors['attribute_id'] = 'Невалиден ID на атрибут';
        }

        // Проверка за група атрибути
        if (empty($data['attribute_group_id']) || !is_numeric($data['attribute_group_id'])) {
            $errors['attribute_group'] = 'Моля, изберете група атрибути';
        }

        // Проверка за описания на атрибута
        if (empty($data['attribute_description']) || !is_array($data['attribute_description'])) {
            $errors['attribute_description'] = 'Липсват описания на атрибута';
        } else {
            foreach ($data['attribute_description'] as $language_id => $description) {
                if (empty($description['name']) || strlen(trim($description['name'])) < 1) {
                    $errors['name'][$language_id] = 'Името на атрибута е задължително';
                } elseif (strlen($description['name']) > 64) {
                    $errors['name'][$language_id] = 'Името на атрибута не може да бъде по-дълго от 64 символа';
                }
            }
        }

        // Проверка за sort_order
        if (isset($data['sort_order']) && !is_numeric($data['sort_order'])) {
            $errors['sort_order'] = 'Подредбата трябва да бъде число';
        }

        return $errors;
    }
}
