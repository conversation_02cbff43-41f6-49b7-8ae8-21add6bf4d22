[19-Jul-2025 15:14:09 UTC] PHP Fatal error:  Uncaught TypeError: Argument 1 passed to Theme25\Controller::__construct() must be an instance of Registry, instance of Theme25\Backend\Controller\Customer\Customer given, called in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php on line 8 and defined in /home/<USER>/storage_theme25/theme/Controller.php:14
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php(8): Theme25\Controller->__construct(Object(Theme25\Backend\Controller\Customer\Customer), 'customer/custom...')
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(201): Theme25\Backend\Controller\Customer\Customergroup->__construct(Object(Theme25\Backend\Controller\Customer\Customer))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/home/<USER>/storage_theme25/theme/Controller.php on line 14
[19-Jul-2025 15:17:00 UTC] PHP Fatal error:  Uncaught TypeError: Argument 1 passed to Theme25\Controller::__construct() must be an instance of Registry, instance of Theme25\Backend\Controller\Customer\Customer given, called in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php on line 8 and defined in /home/<USER>/storage_theme25/theme/Controller.php:14
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php(8): Theme25\Controller->__construct(Object(Theme25\Backend\Controller\Customer\Customer), 'customer/custom...')
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(209): Theme25\Backend\Controller\Customer\Customergroup->__construct(Object(Theme25\Backend\Controller\Customer\Customer))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/home/<USER>/storage_theme25/theme/Controller.php on line 14
[19-Jul-2025 15:17:12 UTC] PHP Fatal error:  Uncaught TypeError: Argument 1 passed to Theme25\Controller::__construct() must be an instance of Registry, instance of Theme25\Backend\Controller\Customer\Customer given, called in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php on line 8 and defined in /home/<USER>/storage_theme25/theme/Controller.php:14
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php(8): Theme25\Controller->__construct(Object(Theme25\Backend\Controller\Customer\Customer), 'customer/custom...')
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(209): Theme25\Backend\Controller\Customer\Customergroup->__construct(Object(Theme25\Backend\Controller\Customer\Customer))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/home/<USER>/storage_theme25/theme/Controller.php on line 14
[19-Jul-2025 18:17:44 Europe/Sofia] PHP Fatal error:  Uncaught TypeError: Argument 1 passed to Theme25\Controller::__construct() must be an instance of Registry, instance of Theme25\Backend\Controller\Customer\Customer given, called in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php on line 8 and defined in /home/<USER>/storage_theme25/theme/Controller.php:14
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customergroup.php(8): Theme25\Controller->__construct(Object(Theme25\Backend\Controller\Customer\Customer), 'customer/custom...')
#1 /home/<USER>/storage_theme25/theme/RequestProcessor.php(209): Theme25\Backend\Controller\Customer\Customergroup->__construct(Object(Theme25\Backend\Controller\Customer\Customer))
#2 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Theme25/Backend...', 'index', Array)
#3 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'index', Array)
#4 /home/<USER>/home/<USER>/storage_theme25/theme/Controller.php on line 14
[19-Jul-2025 15:49:51 UTC] PHP Fatal error:  Uncaught Exception: Error: Table 'rakla_test.oc_customer_login_attempt' doesn't exist<br />Error No: 1146<br />DELETE FROM `oc_customer_login_attempt` WHERE email = '<EMAIL>' in /home/<USER>/theme25/system/library/db/mysqli.php:40
Stack trace:
#0 /home/<USER>/theme25/system/library/db.php(16): DB\MySQLi->query('DELETE FROM `oc...', Array)
#1 /home/<USER>/storage_theme25/theme/SecondDB.php(64): DB->query('DELETE FROM `oc...', Array)
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Unlock.php(32): Theme25\SecondDB->query('DELETE FROM `oc...')
#3 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(139): Theme25\Backend\Controller\Customer\Customer\Unlock->execute()
#4 [internal function]: Theme25\Backend\Controller\Customer\Customer->unlock()
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#6 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('Control in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
