<?php

namespace Theme25\Backend\Controller\Catalog\Option;

class Save extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Запазване на опция
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();

        // Валидация на данните
        $validation_errors = $this->validateOptionData($post);
        if (!empty($validation_errors)) {
            $json['errors'] = $validation_errors;
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/option' => 'optionModel'
        ]);

        try {
            $option_id = (int)($post['option_id'] ?? 0);

            // Подготвяне на данните за запазване
            $prepared_data = $this->prepareDataForSave($post);

            if ($option_id > 0) {
                // Редактиране на съществуваща опция
                $this->optionModel->editOption($option_id, $prepared_data);
                $json['success'] = 'Опцията беше успешно актуализирана';
                $json['option_id'] = $option_id;
            } else {
                // Добавяне на нова опция
                $option_id = $this->optionModel->addOption($prepared_data);
                $json['success'] = 'Опцията беше успешно добавена';
                $json['option_id'] = $option_id;
            }

            $json['redirect'] = $this->getAdminLink('catalog/option');

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при запазване на опцията: ' . $e->getMessage();
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира данните за опцията
     */
    private function validateOptionData($data) {
        $errors = [];

        // Проверка за права за модификация
        if (!$this->user->hasPermission('modify', 'catalog/option')) {
            $errors['permission'] = 'Нямате права за модификация на опции';
            return $errors;
        }

        // Проверка за тип опция
        if (empty($data['type'])) {
            $errors['type'] = 'Моля, изберете тип опция';
        }

        // Проверка за описания на опцията
        if (empty($data['option_description']) || !is_array($data['option_description'])) {
            $errors['option_description'] = 'Липсват описания на опцията';
        } else {
            $has_valid_name = false;
            foreach ($data['option_description'] as $language_id => $description) {
                if (!empty($description['name']) && strlen(trim($description['name'])) >= 1) {
                    $has_valid_name = true;
                    
                    // Проверка за дължина на името
                    if (strlen($description['name']) > 128) {
                        $errors['name'][$language_id] = 'Името на опцията не може да бъде по-дълго от 128 символа';
                    }
                }
            }
            
            if (!$has_valid_name) {
                $errors['name']['general'] = 'Трябва да въведете име на опцията поне за един език';
            }
        }

        // Проверка за sort_order
        if (isset($data['sort_order'])) {
            if (!is_numeric($data['sort_order'])) {
                $errors['sort_order'] = 'Подредбата трябва да бъде число';
            } else {
                $data['sort_order'] = (int)$data['sort_order'];
            }
        } else {
            $data['sort_order'] = 0;
        }

        // Проверка за стойности на опцията (за select, radio, checkbox)
        if (in_array($data['type'], ['select', 'radio', 'checkbox'])) {
            if (empty($data['option_value']) || !is_array($data['option_value'])) {
                $errors['option_value'] = 'Опциите от тип ' . $data['type'] . ' трябва да имат поне една стойност';
            } else {
                $valid_values = 0;
                foreach ($data['option_value'] as $key => $option_value) {
                    if (empty($option_value['option_value_description']) || !is_array($option_value['option_value_description'])) {
                        $errors['option_value'][$key] = 'Липсват описания на стойността';
                        continue;
                    }
                    
                    $has_valid_value_name = false;
                    foreach ($option_value['option_value_description'] as $language_id => $description) {
                        if (!empty($description['name']) && strlen(trim($description['name'])) >= 1) {
                            $has_valid_value_name = true;
                            
                            // Проверка за дължина на името на стойността
                            if (strlen($description['name']) > 255) {
                                $errors['option_value'][$key]['name'][$language_id] = 'Името на стойността не може да бъде по-дълго от 255 символа';
                            }
                        }
                    }
                    
                    if ($has_valid_value_name) {
                        $valid_values++;
                    } else {
                        $errors['option_value'][$key] = 'Стойността трябва да има име поне за един език';
                    }
                    
                    // Проверка за sort_order на стойността
                    if (isset($option_value['sort_order']) && !is_numeric($option_value['sort_order'])) {
                        $errors['option_value'][$key]['sort_order'] = 'Подредбата на стойността трябва да бъде число';
                    }
                }
                
                if ($valid_values == 0) {
                    $errors['option_value']['general'] = 'Трябва да има поне една валидна стойност';
                }
            }
        }

        // Проверка за дублиращи се имена
        if (empty($errors['name'])) {
            $this->loadModelsAs(['catalog/option' => 'optionModel']);
            
            $option_id = (int)($data['option_id'] ?? 0);
            $existing_options = $this->optionModel->getOptions();

            foreach ($data['option_description'] as $language_id => $description) {
                if (!empty($description['name'])) {
                    foreach ($existing_options as $existing) {
                        if ($existing['option_id'] != $option_id && 
                            strtolower($existing['name']) == strtolower($description['name'])) {
                            $errors['name'][$language_id] = 'Опция с това име вече съществува';
                            break;
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Подготвя данните за запазване
     */
    private function prepareDataForSave($data) {
        $prepared_data = [
            'type' => $data['type'],
            'sort_order' => (int)($data['sort_order'] ?? 0),
            'option_description' => [],
            'option_value' => []
        ];

        // Подготвяне на описанията
        if (isset($data['option_description']) && is_array($data['option_description'])) {
            foreach ($data['option_description'] as $language_id => $description) {
                if (!empty($description['name'])) {
                    $prepared_data['option_description'][(int)$language_id] = [
                        'name' => trim($description['name'])
                    ];
                }
            }
        }

        // Подготвяне на стойностите (за select, radio, checkbox)
        if (in_array($data['type'], ['select', 'radio', 'checkbox']) && 
            isset($data['option_value']) && is_array($data['option_value'])) {
            
            foreach ($data['option_value'] as $key => $option_value) {
                if (isset($option_value['option_value_description']) && is_array($option_value['option_value_description'])) {
                    $value_descriptions = [];
                    foreach ($option_value['option_value_description'] as $language_id => $description) {
                        if (!empty($description['name'])) {
                            $value_descriptions[(int)$language_id] = [
                                'name' => trim($description['name'])
                            ];
                        }
                    }
                    
                    if (!empty($value_descriptions)) {
                        $prepared_data['option_value'][] = [
                            'option_value_id' => (int)($option_value['option_value_id'] ?? 0),
                            'option_value_description' => $value_descriptions,
                            'image' => $option_value['image'] ?? '',
                            'sort_order' => (int)($option_value['sort_order'] ?? 0)
                        ];
                    }
                }
            }
        }

        return $prepared_data;
    }

    /**
     * Зареждане на модели с псевдоними
     */
    private function loadModelsAs($models) {
        foreach ($models as $model => $alias) {
            $this->load->model($model);
            $this->{$alias} = $this->{'model_' . str_replace('/', '_', $model)};
        }
    }
}
