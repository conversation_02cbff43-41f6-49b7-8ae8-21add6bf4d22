<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($parent_controller) {
        parent::__construct($parent_controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    
	/**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
 
        $categoriesUrl = $base . 'backend_js/categories.js';
        $categoriesPath = DIR_THEME . 'Backend/View/Javascript/categories.js';

        if (file_exists($categoriesPath)) {
            $lastModified = filemtime($categoriesPath);
            $categoriesUrl .= '?v=' . $lastModified;
            $this->document->addScript($categoriesUrl, 'footer');
        }
    }

    /**
     * Изпълнява подготовката на данните за списъка с категории
     */
    public function execute() {
        $this->setTitle('Категории');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/category');
    }

    /**
     * Подготвя всички данни за страницата с категории
     */
    public function prepareData() {
        $this->prepareCategoryListData()
             ->prepareFilterOptions()
             ->prepareCategoryItems()
             ->prepareAjaxUrls();

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/category')
        ]);
    }

    /**
     * Подготвя основните данни за списъка с категории
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCategoryListData() {
        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/category' => 'categories',
            'tool/image' => 'image'
        ]);

        // URL адреси - използваме getAdminLinks за групово извличане
        $routes = [
            'add_new_url' => 'catalog/category/add',
            'delete_url' => 'catalog/category/delete&category_id=CATEGORY_ID',
            'edit_url' => 'catalog/category/edit&category_id=CATEGORY_ID',
            'copy_url' => 'catalog/category/copy&category_id=CATEGORY_ID'
        ];

        // Добавяне на URL адресите към данните
        $this->setData($this->getAdminLinks($routes));

        return $this;
    }

    /**
     * Подготвя опциите за филтриране и сортиране
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareFilterOptions() {
        // Получаване на данните за филтрите
        $filter_data = $this->getFilterData();

        // Опции за сортиране
        $sort_options = [
            ['value' => 'cd.name-ASC', 'text' => 'Име (А-Я)'],
            ['value' => 'cd.name-DESC', 'text' => 'Име (Я-А)'],
            ['value' => 'c.sort_order-ASC', 'text' => 'Позиция (възх.)'],
            ['value' => 'c.sort_order-DESC', 'text' => 'Позиция (низх.)'],
            ['value' => 'c.date_added-DESC', 'text' => 'Последно добавени'],
            ['value' => 'c.date_added-ASC', 'text' => 'Първо добавени']
        ];

        // URL адреси за филтри
        $filter_urls = $this->getAdminLinks([
            'sort_url' => 'catalog/category',
            'limit_url' => 'catalog/category',
            'filter_active_url' => 'catalog/category',
            'filter_parent_url' => 'catalog/category'
        ], [
            'sort_url' => '&sort=SORT_VALUE',
            'limit_url' => '&limit=LIMIT_VALUE',
            'filter_active_url' => '&filter_status=FILTER_VALUE',
            'filter_parent_url' => '&filter_parent_id=FILTER_VALUE'
        ]);

        // Текущи филтри
        $current_filters = [
            'filter_active' => isset($filter_data['filter_status']) && $filter_data['filter_status'] == 1,
            'filter_parent' => isset($filter_data['filter_parent_id']) ? $filter_data['filter_parent_id'] : '',
            'view_type' => $this->requestGet('view') ?: 'list',
            'limit' => $filter_data['limit']
        ];

        // Добавяне на данните към $this->data
        $this->setData([
            'sort_options' => $sort_options,
            'filter_data' => $filter_data
        ])
        ->setData($filter_urls)
        ->setData($current_filters);

        return $this;
    }

    /**
     * Подготвя списъка с категории в йерархичен вид
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareCategoryItems() {
        // Получаване на данните за филтрите
        $filter_data = $this->data['filter_data'];

        // За йерархичен изглед показваме само основните категории (parent_id = 0)
        $filter_data['filter_parent_id'] = 0;

        // Премахваме пагинацията за основните категории
        unset($filter_data['start']);
        unset($filter_data['limit']);

        // Получаване на основните категории
        $results = $this->categories->getCategories($filter_data);

        // Подготовка на данните за категориите
        $categories = [];

        foreach ($results as $result) {
            // Броене на продуктите в категорията
            $product_count = $this->getProductCountForCategory($result['category_id']);

            // Проверка дали категорията има подкатегории
            $has_subcategories = $this->hasSubcategories($result['category_id']);

            // Подготовка на данните за категорията
            $categories[] = [
                'category_id' => $result['category_id'],
                'name' => $result['name'],
                'parent_id' => $result['parent_id'],
                'sort_order' => $result['sort_order'],
                'status' => $result['status'],
                'status_text' => $result['status'] ? 'Активна' : 'Неактивна',
                'status_class' => $result['status'] ? 'status-active' : 'status-inactive',
                'product_count' => $product_count,
                'has_subcategories' => $has_subcategories,
                'level' => 0, // Основните категории са на ниво 0
                'edit' => str_replace('CATEGORY_ID', $result['category_id'], $this->data['edit_url']),
                'delete' => str_replace('CATEGORY_ID', $result['category_id'], $this->data['delete_url']),
                'products_url' => $this->getAdminLink('catalog/product', 'filter_category_id=' . $result['category_id'])
            ];
        }

        // Подобрена логика за сортиране комбинираща sort_order и автоматичен index
        $categories = $this->applySortOrderLogic($categories);
        
        // Добавяне на категориите към данните
        $this->setData('categories', $categories);

        return $this;
    }

    /**
     * Подготвя URL адресите за AJAX заявки
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareAjaxUrls() {
        // URL адреси за AJAX заявки
        $ajax_urls = [
            'ajax_load_subcategories' => $this->getAdminLink('catalog/category/ajax/loadSubcategories'),
            'ajax_update_sort_order' => $this->getAdminLink('catalog/category/ajax/updateSortOrder'),
            'ajax_get_category_info' => $this->getAdminLink('catalog/category/ajax/getCategoryInfo'),
            'ajax_delete_category' => $this->getAdminLink('catalog/category/delete')
        ];

        // Добавяне на URL адресите към данните
        $this->setData($ajax_urls);

        // Общ брой основни категории (за информация)
        $main_categories_total = $this->categories->getTotalCategories(['filter_parent_id' => 0]);
        $this->setData([
            'category_total' => $main_categories_total,
            'start' => 1,
            'end' => $main_categories_total
        ]);

        return $this;
    }

    /**
     * Получава данните за филтрите от заявката
     *
     * @return array Масив с данни за филтрите
     */
    private function getFilterData() {
        $filter_data = [];

        // Сортиране - по подразбиране сортираме по sort_order за йерархичен изглед
        $sort = $this->requestGet('sort') ?: 'c.sort_order';
        $order = $this->requestGet('order') ?: 'ASC';

        // Ако сортирането е във формат 'field-ORDER'
        if (strpos($sort, '-') !== false) {
            list($sort, $order) = explode('-', $sort);
        }

        $filter_data['sort'] = $sort;
        $filter_data['order'] = $order;

        // Филтри
        $filter_fields = [
            'filter_name',
            'filter_status'
        ];

        foreach ($filter_fields as $field) {
            if ($value = $this->requestGet($field)) {
                $filter_data[$field] = $value;
            }
        }

        return $filter_data;
    }

    /**
     * Получава броя продукти в дадена категория (рекурсивно включва подкатегории)
     *
     * @param int $category_id ID на категорията
     * @return int Брой продукти включително от подкатегории
     */
    private function getProductCountForCategory($category_id) {
        // Зареждане на модела за продукти, ако не е зареден
        if (!isset($this->products)) {
            $this->loadModelAs('catalog/product', 'products');
        }

        // Получаваме всички подкатегории рекурсивно
        $all_category_ids = $this->getAllSubcategoryIds($category_id);

        // Добавяме и самата категория
        $all_category_ids[] = $category_id;

        // Смятаме общия брой продукти от всички категории
        $total_products = 0;
        foreach ($all_category_ids as $cat_id) {
            $total_products += $this->products->getTotalProducts(['filter_category_id' => $cat_id]);
        }

        return $total_products;
    }

    /**
     * Получава всички ID-та на подкатегории рекурсивно
     *
     * @param int $parent_id ID на родителската категория
     * @return array Масив с всички ID-та на подкатегории
     */
    private function getAllSubcategoryIds($parent_id) {
        $all_ids = [];

        // Получаваме директните подкатегории
        $subcategories = $this->categories->getCategories([
            'filter_parent_id' => $parent_id
        ]);

        foreach ($subcategories as $subcategory) {
            $subcategory_id = $subcategory['category_id'];
            $all_ids[] = $subcategory_id;

            // Рекурсивно получаваме подкатегориите на тази подкатегория
            $nested_ids = $this->getAllSubcategoryIds($subcategory_id);
            $all_ids = array_merge($all_ids, $nested_ids);
        }

        return $all_ids;
    }

    /**
     * Получава нивото на категорията в йерархията
     *
     * @param int $category_id ID на категорията
     * @return int Ниво на категорията
     */
    private function getCategoryLevel($category_id) {
        $path = $this->categories->getCategoryPath($category_id);
        return count($path) - 1; // Минус 1, защото включва и самата категория
    }

    /**
     * Проверява дали категорията има подкатегории
     *
     * @param int $category_id ID на категорията
     * @return bool True ако има подкатегории
     */
    private function hasSubcategories($category_id) {
        $subcategories = $this->categories->getCategories([
            'filter_parent_id' => $category_id,
            'start' => 0,
            'limit' => 1
        ]);

        return !empty($subcategories);
    }



    /**
     * Прилага подобрена логика за сортиране комбинираща sort_order и автоматичен index
     *
     * @param array $categories Масив с категории
     * @return array Сортирани категории
     */
    private function applySortOrderLogic($categories) {
        // Разделяме категориите на две групи:
        // 1. Категории със зададен sort_order (различен от 0)
        // 2. Категории без зададен sort_order (равен на 0)

        $categoriesWithSortOrder = [];
        $categoriesWithoutSortOrder = [];

        foreach ($categories as $category) {
            if ($category['sort_order'] > 0) {
                $categoriesWithSortOrder[] = $category;
            } else {
                $categoriesWithoutSortOrder[] = $category;
            }
        }

        // Сортираме категориите със зададен sort_order по техния sort_order
        usort($categoriesWithSortOrder, function($a, $b) {
            return $a['sort_order'] - $b['sort_order'];
        });

        // За категориите без зададен sort_order, присвояваме автоматичен index
        // започвайки от най-високия sort_order + 1
        $maxSortOrder = 0;
        if (!empty($categoriesWithSortOrder)) {
            $maxSortOrder = max(array_column($categoriesWithSortOrder, 'sort_order'));
        }

        $autoIndex = $maxSortOrder + 1;
        foreach ($categoriesWithoutSortOrder as &$category) {
            $category['auto_sort_order'] = $autoIndex;
            $autoIndex++;
        }

        // Сортираме категориите без sort_order по име за консистентност
        usort($categoriesWithoutSortOrder, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });

        // Обединяваме двете групи - първо тези със зададен sort_order, после останалите
        $sortedCategories = array_merge($categoriesWithSortOrder, $categoriesWithoutSortOrder);

        $this->logDev('Приложена подобрена логика за сортиране на ' . count($sortedCategories) . ' категории');

        return $sortedCategories;
    }

    /**
     * Логва съобщение за разработчици
     *
     * @param mixed $message Съобщението за логване
     * @param string $file Файлът от който се извиква
     * @param int $line Редът от който се извиква
     */
    private function logDev($message, $file = '', $line = 0) {
        if (function_exists('F') && F()->log) {
            F()->log->developer($message, $file, $line);
        }
    }
}
