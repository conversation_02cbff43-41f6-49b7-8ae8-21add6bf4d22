<?php

namespace Theme25\Backend\Controller\Common;

class GlobalSearch extends \Theme25\Controller {

    protected $cacheDir = 'search/';
    
    /**
     * Време на валидност на кеша в секунди (1 час)
     */
    protected $cacheExpiry = 3600;

    public function __construct($registry) {
        parent::__construct($registry, 'common/globalsearch');
        $this->prepareLanguageData();
        $this->setCacheDir();
    }

    /**
     * Главен endpoint за глобалното търсене - показва HTML страница с общи резултати
     */
    public function index() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderAllSearchResults($query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        // Получаваме резултати от всички групи
        $allResults = $this->performSearch($query, false); // false = не ограничаваме до 5 резултата

        // Изчисляваме общия брой резултати
        $totalCount = 0;
        $groupCounts = [];
        foreach ($allResults as $type => $results) {
            $count = count($results);
            $totalCount += $count;
            $groupCounts[$type] = $count;
        }

        // Прилагаме пагинация на всеки тип резултати
        $offset = ($page - 1) * $limit;
        $remainingLimit = $limit;
        $paginatedResults = [];

        foreach ($allResults as $type => $results) {
            if ($remainingLimit <= 0) break;

            if ($offset > 0) {
                $typeOffset = min($offset, count($results));
                $results = array_slice($results, $typeOffset);
                $offset -= $typeOffset;
            }

            if (!empty($results) && $remainingLimit > 0) {
                $typeLimit = min($remainingLimit, count($results));
                $paginatedResults[$type] = array_slice($results, 0, $typeLimit);
                $remainingLimit -= $typeLimit;
            }
        }

        $this->renderAllSearchResults($query, $paginatedResults, $totalCount, $page, $limit, null, $groupCounts);
    }

    /**
     * AJAX endpoint за глобалното търсене - връща JSON резултати
     */
    public function search() {
        $json = [];

        ob_start();

        $query = $this->requestGet('query');

        $this->logDev("GlobalSearch::search - Query: " . $query);

        if (!$query || strlen(trim($query)) < 2) {
            $json['error'] = 'Търсеният израз трябва да бъде поне 2 символа';
        } else {
            $json = $this->performSearch($query, true); // true = ограничаваме до 5 резултата
        }

        $output = ob_get_clean();
        if ($output) {
            $json['error'] = $output;
        }

        $this->logDev("GlobalSearch::search - Final results: " . print_r($json, true));

        $this->setJSONResponseOutput($json);
    }

    /**
     * Извършва търсенето и връща резултатите
     */
    private function performSearch($query, $limitResults = true) {
        // Проверяваме дали търсенето е число за интелигентно приоритизиране
        $isNumericSearch = is_numeric(trim($query));
        $this->logDev("GlobalSearch::performSearch - Is numeric search: " . ($isNumericSearch ? 'yes' : 'no'));

        // Зареждане на под-контролерите за различните типове търсене
        $results = [];

        // При числово търсене приоритизираме поръчките
        if ($isNumericSearch) {
            // Първо търсим в поръчки (най-висок приоритет за числа)
            $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
            if ($orderController) {
                if ($limitResults) {
                    $orderResults = $orderController->search($query);
                } else {
                    $searchData = $orderController->searchAll($query, 1, 1000);
                    $orderResults = $searchData['results'] ?? [];
                }
                if (!empty($orderResults)) {
                    $results['orders'] = $orderResults;
                    $this->logDev("GlobalSearch::performSearch - Found order results for numeric search");
                }
            }

            // Ако намерим поръчка, търсим клиента който я е направил
            if (!empty($results['orders'])) {
                $this->logDev("GlobalSearch::performSearch - Searching for customer of found order");
                // Вземаме първата поръчка и търсим нейния клиент
                $firstOrder = $results['orders'][0];
                if (!empty($firstOrder['email'])) {
                    $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
                    if ($customerController) {
                        if ($limitResults) {
                            $customerResults = $customerController->search($firstOrder['email']);
                        } else {
                            $searchData = $customerController->searchAll($firstOrder['email'], 1, 1000);
                            $customerResults = $searchData['results'] ?? [];
                        }
                        if (!empty($customerResults)) {
                            $results['customers'] = $customerResults;
                            $this->logDev("GlobalSearch::performSearch - Found customer for order email: " . $firstOrder['email']);
                        }
                    }
                }
            } else {
                // Ако няма поръчки, търсим в продукти
                $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
                if ($productController) {
                    if ($limitResults) {
                        $productResults = $productController->search($query);
                    } else {
                        $searchData = $productController->searchAll($query, 1, 1000);
                        $productResults = $searchData['results'] ?? [];
                    }
                    if (!empty($productResults)) {
                        $results['products'] = $productResults;
                    }
                }
            }
        } else {
            // При не-числово търсене използваме стандартния ред
            // Търсене в продукти
            $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);

            $this->logDev("GlobalSearch::performSearch - Product controller: " . get_class($productController));

            if ($productController) {
                if ($limitResults) {
                    $productResults = $productController->search($query);
                } else {
                    $searchData = $productController->searchAll($query, 1, 1000);
                    $productResults = $searchData['results'] ?? [];
                }

                $this->logDev("GlobalSearch::performSearch - Product results: " . print_r($productResults, true));

                if (!empty($productResults)) {
                    $results['products'] = $productResults;
                }
            }

            // Търсене в категории
            $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
            if ($categoryController) {
                if ($limitResults) {
                    $categoryResults = $categoryController->search($query);
                } else {
                    $searchData = $categoryController->searchAll($query, 1, 1000);
                    $categoryResults = $searchData['results'] ?? [];
                }
                if (!empty($categoryResults)) {
                    $results['categories'] = $categoryResults;
                }
            }

            // Търсене в поръчки (само ако не е числово търсене)
            $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
            if ($orderController) {
                if ($limitResults) {
                    $orderResults = $orderController->search($query);
                } else {
                    $searchData = $orderController->searchAll($query, 1, 1000);
                    $orderResults = $searchData['results'] ?? [];
                }
                if (!empty($orderResults)) {
                    $results['orders'] = $orderResults;
                }
            }

            // Търсене в клиенти
            $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
            if ($customerController) {
                if ($limitResults) {
                    $customerResults = $customerController->search($query);
                } else {
                    $searchData = $customerController->searchAll($query, 1, 1000);
                    $customerResults = $searchData['results'] ?? [];
                }
                if (!empty($customerResults)) {
                    $results['customers'] = $customerResults;
                }
            }
        }

        return $results;
    }

    /**
     * Рендериране на резултатите от всички групи търсения
     */
    private function renderAllSearchResults($query, $results, $total, $page, $limit, $error = null, $groupCounts = null) {
        $data = [
            'header' => $this->load->controller('common/header'),
            'sidebar' => $this->load->controller('common/sidebar'),
            'footer' => $this->load->controller('common/footer'),
            'user_token' => $this->session->data['user_token'],
            'query' => $query,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'error' => $error,
            'title' => 'Всички резултати'
        ];

        // Ако резултатите са масив с типове (orders, customers, products, categories)
        if (is_array($results) && !empty($results) && (isset($results['orders']) || isset($results['customers']) || isset($results['products']) || isset($results['categories']))) {
            // Разделяме резултатите по типове
            $data['results'] = [
                'orders' => $results['orders'] ?? [],
                'customers' => $results['customers'] ?? [],
                'products' => $results['products'] ?? [],
                'categories' => $results['categories'] ?? []
            ];
        } else {
            // Стари резултати - смесени в един масив
            $data['results'] = $results;
        }

        // Групиране на резултатите по тип за показване на линкове "Всички резултати"
        if ($groupCounts !== null) {
            // Използваме подадените групови броячи
            $data['group_counts'] = $groupCounts;
        } else if (!$error && !empty($results)) {
            // Получаваме броя резултати за всяка група
            $allResults = $this->performSearch($query, false);
            $calculatedGroupCounts = [];
            foreach ($allResults as $type => $typeResults) {
                $calculatedGroupCounts[$type] = count($typeResults);
            }
            $data['group_counts'] = $calculatedGroupCounts;
        } else {
            $data['group_counts'] = [];
        }

        // Пагинация
        if ($total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch', '&query=' . urlencode($query) . '&page={page}');
            $pagination->setProductText('резултата');

            $data['pagination'] = $pagination->render();
        }

        $this->response->setOutput($this->load->view('common/global_search_all_results', $data));
    }

    /**
     * Търсене във всички продукти
     */
    public function products() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('products', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
        if ($productController) {
            $data = $productController->searchAll($query, $page, $limit);
            $this->renderSearchResults('products', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('products', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за продукти');
        }
    }

    /**
     * Търсене във всички категории
     */
    public function categories() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('categories', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
        if ($categoryController) {
            $data = $categoryController->searchAll($query, $page, $limit);
            $this->renderSearchResults('categories', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('categories', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за категории');
        }
    }

    /**
     * Търсене във всички поръчки
     */
    public function orders() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('orders', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
        if ($orderController) {
            $data = $orderController->searchAll($query, $page, $limit);
            $this->renderSearchResults('orders', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('orders', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за поръчки');
        }
    }

    /**
     * Търсене във всички клиенти
     */
    public function customers() {
        $query = $this->requestGet('query');
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderSearchResults('customers', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
        if ($customerController) {
            $data = $customerController->searchAll($query, $page, $limit);
            $this->renderSearchResults('customers', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderSearchResults('customers', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за клиенти');
        }
    }



    /**
     * Рендериране на резултатите от търсенето
     */
    private function renderSearchResults($type, $query, $results, $total, $page, $limit, $error = null) {
        $data = [
            'header' => $this->load->controller('common/header'),
            'sidebar' => $this->load->controller('common/sidebar'),
            'footer' => $this->load->controller('common/footer'),
            'user_token' => $this->session->data['user_token'],
            'type' => $type,
            'query' => $query,
            'results' => $results,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'error' => $error
        ];

        // Заглавия за различните типове
        $titles = [
            'products' => 'Продукти',
            'categories' => 'Категории',
            'orders' => 'Поръчки',
            'customers' => 'Клиенти'
        ];

        $data['title'] = $titles[$type] ?? 'Резултати';

        // Пагинация с използване на Theme25\Pagination класа
        if ($total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/' . $type, '&query=' . urlencode($query) . '&page={page}');

            // Задаваме подходящ текст според типа
            $productTexts = [
                'products' => 'продукта',
                'categories' => 'категории',
                'orders' => 'поръчки',
                'customers' => 'клиента'
            ];
            $pagination->setProductText($productTexts[$type] ?? 'резултата');

            $data['pagination'] = $pagination->render();
        }

        $this->response->setOutput($this->load->view('common/global_search_results', $data));
    }

     /**
     * Подготвя данните за езиците.
     */
    private function prepareLanguageData() {
        $this->loadModelsAs([
            'localisation/language'   => 'languageModel',
        ]);
        $active_language_id = $this->getConfig('config_language_id');
        $active_language_id_sdb = $this->getConfigFromSecondDB('config_language_id');

        if (!empty($active_language_id_sdb)) {
            $active_language_id = $active_language_id_sdb;
        }

        $languages_data = $this->languageModel->getLanguages();
        $languages = [];
        $language_exists = false;

        foreach ($languages_data as $language) {
            $languages[] = [
                'language_id' => $language['language_id'],
                'code' => $language['code'],
                'name' => $language['name'],
                'css' => $language['code'] == 'bg-bg' ? ' bg-languagebg' : ' bg-languageen'
            ];
            if ($language['language_id'] == $active_language_id) {
                $language_exists = true;
            }
        }

        $this->setData('languages', $languages);

        if (!$language_exists && !empty($languages)) {
            $active_language_id = $languages[0]['language_id'];
        }

        $this->setData('active_language_id', $active_language_id);
    }

    /**
     * Изчиства кеша за търсенето
     */
    public function clearCache() {
        $json = ['success' => false];

        try {
            // Изчистваме кеша за всички под-контролери
            $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
            if ($productController) {
                $productController->clearCache();
            }

            $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
            if ($categoryController) {
                $categoryController->clearCache();
            }

            $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
            if ($orderController) {
                $orderController->clearCache();
            }

            $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
            if ($customerController) {
                $customerController->clearCache();
            }

            $json['success'] = true;
            $json['message'] = 'Кешът за търсенето е изчистен успешно';

        } catch (Exception $e) {
            $json['error'] = 'Грешка при изчистване на кеша: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    private function setCacheDir() {
        $this->cacheDir = DIR_CACHE . $this->cacheDir;
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

}
