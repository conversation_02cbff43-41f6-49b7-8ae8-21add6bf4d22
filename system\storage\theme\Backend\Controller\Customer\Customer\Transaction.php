<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Transaction extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява управление на транзакции - връща HTML за tab view
     */
    public function execute() {
        try {
            $customer_id = (int)$this->requestGet('customer_id', 0);

            if (!$customer_id) {
                echo '<p class="text-gray-500">Невалиден клиент!</p>';
                return;
            }

            if (!$this->hasPermission('modify', 'customer/customer')) {
                echo '<p class="text-red-500">Нямате права за управление на транзакции!</p>';
                return;
            }

            $this->loadModelAs('customer/customer', 'customerModel');
            $customer_info = $this->customerModel->getCustomer($customer_id);

            if (!$customer_info) {
                echo '<p class="text-red-500">Клиентът не съществува!</p>';
                return;
            }

            // Получаваме транзакциите
            $transaction_data = $this->getTransactionsData($customer_id);

            // Генерираме HTML
            echo $this->generateTransactionListHtml($transaction_data);

        } catch (Exception $e) {
            echo '<p class="text-red-500">Грешка при зареждане на транзакции: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }

    /**
     * API метод за JSON операции
     */
    public function api() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за управление на транзакции!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            $action = $this->requestGet('action', '');

            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                $customer_info = $this->customerModel->getCustomer($customer_id);

                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    switch ($action) {
                        case 'add':
                            $json = $this->addTransaction($customer_id);
                            break;
                        case 'list':
                            $json = $this->getTransactions($customer_id);
                            break;
                        case 'delete':
                            $json = $this->deleteTransaction();
                            break;
                        default:
                            $json = $this->getTransactions($customer_id);
                            break;
                    }
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Добавя нова транзакция
     */
    private function addTransaction($customer_id) {
        $json = [];
        
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $amount = (float)$this->requestPost('amount', 0);
            $description = trim($this->requestPost('description', ''));
            
            if ($amount == 0) {
                $json['error'] = 'Сумата трябва да бъде различна от нула!';
            } elseif (empty($description)) {
                $json['error'] = 'Описанието е задължително!';
            } else {
                // Използваме стандартния OpenCart модел
                $this->loadModelAs('customer/customer', 'customerModel');

                $order_id = (int)$this->requestPost('order_id', 0);

                $this->customerModel->addTransaction($customer_id, $description, $amount, $order_id);
                
                // Логваме действието
                $this->loadModelAs('user/user', 'userModel');
                $user_info = $this->userModel->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' добави транзакция за клиент ID: ' . $customer_id . ' - Сума: ' . $amount);
                }
                
                $json['success'] = 'Транзакцията беше успешно добавена!';
            }
        } else {
            $json['error'] = 'Невалидна заявка!';
        }
        
        return $json;
    }

    /**
     * Връща списък с транзакции
     */
    /**
     * Генерира HTML за списъка с транзакции
     */
    private function generateTransactionListHtml($transaction_data) {
        if (empty($transaction_data['transactions'])) {
            return '<p class="text-gray-500">Няма записани транзакции за този клиент.</p>';
        }

        $html = '<div class="space-y-3">';
        $balance = 0;

        foreach ($transaction_data['transactions'] as $transaction) {
            $balance += (float)$transaction['amount'];
            $amountClass = $transaction['amount'] >= 0 ? 'text-green-600' : 'text-red-600';
            $amountIcon = $transaction['amount'] >= 0 ? 'ri-add-circle-line' : 'ri-subtract-line';

            $html .= '
                <div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <i class="' . $amountIcon . ' ' . $amountClass . '"></i>
                        <div>
                            <div class="font-medium text-gray-900">' . htmlspecialchars($transaction['description']) . '</div>
                            <div class="text-sm text-gray-500">
                                ' . htmlspecialchars($transaction['date_added']) .
                                ($transaction['order_id'] ? ' | Поръчка #' . (int)$transaction['order_id'] : '') . '
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium ' . $amountClass . '">' .
                        $this->currency->format($transaction['amount'], $this->getConfig('config_currency')) . '</div>
                    </div>
                </div>';
        }

        $html .= '</div>';

        // Добавяме баланс информация
        $balanceClass = $balance >= 0 ? 'text-green-600' : 'text-red-600';
        $html .= '<div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex justify-between items-center">
                <span class="font-medium text-gray-700">Общ баланс:</span>
                <span class="font-bold text-lg ' . $balanceClass . '">' .
                $this->currency->format($balance, $this->getConfig('config_currency')) . '</span>
            </div>
        </div>';

        // Добавяме информация за общия брой
        if ($transaction_data['total'] > count($transaction_data['transactions'])) {
            $html .= '<div class="mt-4 text-sm text-gray-500 text-center">
                Показани ' . count($transaction_data['transactions']) . ' от ' . $transaction_data['total'] . ' транзакции
            </div>';
        }

        return $html;
    }

    /**
     * Получава данните за транзакциите (за HTML генериране)
     */
    private function getTransactionsData($customer_id) {
        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);

        $this->loadModelAs('customer/customer', 'customerModel');

        $transactions = $this->customerModel->getTransactions($customer_id, $start, $limit);
        $total = $this->customerModel->getTotalTransactions($customer_id);

        $transaction_data = [];
        foreach ($transactions as $transaction) {
            $transaction_data[] = [
                'customer_transaction_id' => $transaction['customer_transaction_id'],
                'order_id' => $transaction['order_id'],
                'description' => $transaction['description'],
                'amount' => (float)$transaction['amount'],
                'date_added' => date('d.m.Y H:i', strtotime($transaction['date_added']))
            ];
        }

        return [
            'transactions' => $transaction_data,
            'total' => $total
        ];
    }

    /**
     * Връща списък с транзакции (за JSON API)
     */
    private function getTransactions($customer_id) {
        // Използваме стандартния OpenCart модел
        $this->loadModelAs('customer/customer', 'customerModel');

        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);

        $transactions = $this->customerModel->getTransactions($customer_id, $start, $limit);
        $total = $this->customerModel->getTotalTransactions($customer_id);
        
        $transaction_data = [];
        foreach ($transactions as $transaction) {
            $transaction_data[] = [
                'customer_transaction_id' => $transaction['customer_transaction_id'],
                'order_id' => $transaction['order_id'],
                'description' => $transaction['description'],
                'amount' => $this->currency->format($transaction['amount'], $this->getConfig('config_currency')),
                'date_added' => date('d.m.Y H:i', strtotime($transaction['date_added']))
            ];
        }
        
        return [
            'transactions' => $transaction_data,
            'total' => $total
        ];
    }

    /**
     * Изтрива транзакция
     */
    private function deleteTransaction() {
        $json = [];
        
        $transaction_id = (int)$this->requestGet('transaction_id', 0);
        
        if (!$transaction_id) {
            $json['error'] = 'Невалидна транзакция!';
        } else {
            $this->load->model('customer/customer_transaction');
            
            // Проверяваме дали транзакцията съществува
            $transaction_info = $this->model_customer_customer_transaction->getTransaction($transaction_id);
            
            if (!$transaction_info) {
                $json['error'] = 'Транзакцията не съществува!';
            } else {
                $this->model_customer_customer_transaction->deleteTransaction($transaction_id);
                
                // Логваме действието
                $this->load->model('user/user');
                $user_info = $this->model_user_user->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' изтри транзакция ID: ' . $transaction_id);
                }
                
                $json['success'] = 'Транзакцията беше успешно изтрита!';
            }
        }
        
        return $json;
    }
}
