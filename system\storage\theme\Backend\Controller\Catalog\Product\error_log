[15-Jul-2025 14:43:02 Europe/Sofia] PHP Fatal error:  Allowed memory size of 134217728 bytes exhausted (tried to allocate 65015808 bytes) in /home/<USER>/storage_theme25/theme/Backend/Model/Catalog/Productexportdata.php on line 22
[15-Jul-2025 14:46:16 Europe/Sofia] Обработка на batch 1 от 1 (1 продукта)
[15-Jul-2025 14:46:16 Europe/Sofia] Обработка на batch 1 от 1 (1 продукта)
[15-Jul-2025 14:47:41 Europe/Sofia] Обработка на batch 1 от 1 (1 продукта)
[15-Jul-2025 14:47:41 Europe/Sofia] Обработка на batch 1 от 1 (1 продукта)
[15-Jul-2025 14:47:41 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to undefined method Theme25\Backend\Model\Catalog\Productexportxml::cleanText() in /home/<USER>/storage_theme25/theme/Backend/Model/Catalog/Productexportxml.php:201
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Model/Catalog/Productexportxml.php(82): Theme25\Backend\Model\Catalog\Productexportxml->createProductElement(Object(DOMDocument), Array, Array)
#1 [internal function]: Theme25\Backend\Model\Catalog\Productexportxml->generateFile(Array, '/tmp/export_8Ly...', 2, Array)
#2 /home/<USER>/storage_theme25/theme/ModelProcessor.php(396): call_user_func_array(Array, Array)
#3 /home/<USER>/storage_theme25/theme/ModelProcessor.php(270): Theme25\ModelProcessor->callModelMethod(Object(Registry), Array, 'catalog/product...', Array)
#4 /home/<USER>/theme25/system/engine/proxy.php(25): Theme25\ModelProcessor->Theme25\{closure}(Array, Array)
#5 /home/<USER>/storage_theme25/theme/Backend/Controller/Catalog/Product/Export.php(490): Proxy->__call('generateFile', Array)
#6 /home/<USER>/storage_the in /home/<USER>/storage_theme25/theme/Backend/Model/Catalog/Productexportxml.php on line 201
