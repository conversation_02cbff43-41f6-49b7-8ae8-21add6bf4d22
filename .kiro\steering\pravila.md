# 📘 Правила за работа на AI агента

## 🗣️ Език
1. **Винаги отговаряй на български език.**

## 💻 Основни технологии
2. **Основният език за програмиране е PHP.**
3. **При работа с JavaScript използвай само чист (vanilla) JavaScript**, освен ако не е изрично поискано друго.
4. **Операционната система на файловете е Windows 10.**
5. **Работа с MySQL се извършва чрез PHPMyAdmin.**

## 🛠️ Работа с код и промени
6. **При изпълнение на задача за добавяне или промяна на код:**
   - Не редактирай, премахвай или подобрявай други части на кода, освен ако не е изрично указано.
   - Ако откриеш възможна грешка или подобрение, предложи го отделно, **без да го прилагаш в работния код.**
7. **Винаги използвай директна кирилица.** Не използвай Unicode escape формати (напр. `\u041f` е грешно).

## 🗄️ Работа с бази данни
### 📌 Общи насоки:
- Използвай **нови редове** за по-добра четимост на SQL кода.
- Използвай **кавички** за SQL заявки и синтаксис `{$variable}` за променливи.

### 📌 WordPress:
- Използвай **вградената функционалност (ВФ)** на WordPress за заявки към базата данни.
- За плъгини използвай `{{prefix}}` като префикс на таблиците.
- Използвай директно кодиране на стойностите (не използвай escape с функции от типа на `addslashes` и т.н., ако ВФ го поддържа сама).

### 📌 OpenCart:
- Използвай константата `DB_PREFIX` за префикс на таблиците.

## 🧩 Организация на кода
9. **При генериране на по-сложна логика, я разбивай на подметоди (функции/методи)** за по-добра четимост и поддръжка.
10. **Ако файлът е голям, прави промените на по-малки стъпки**, заради ограничения в размера на файловете.
11. **Следвай принципа DRY (Don't Repeat Yourself).**
12. **Придържай се към добрите практики в разработката** – цел: поддържан, стабилен и четим код.
13. **Използвай съкратения синтаксис за масиви:**
```php
// Добре:
$data = ['key' => 'value'];
// Не:
$data = array('key' => 'value');
```

## 🛡️ Архивиране и документация
14. **ВИНАГИ преди да промениш даден файл, създай резервно копие**, като в името му добавиш дата и час (напр. `filename_2025-07-17_1530.php`).
15. **Ако в основната директория има файл `instrukcii-i-pravila.md`, прочети го.**
16. **Ако в основната директория има файл `project-conventions.md`, прочети го**, за да разбереш структурата и конвенциите на проекта.

## 🧪 Среда за работа и тестване
17. **Не използвай локалната машина с Windows 10 за тестване.** На нея няма инсталирана подходяща платформа.

## 📋 Организация на задачите
18. **Винаги организирай работата си чрез списък със задачи (task-ове)** – за яснота и за да не се пропусне нищо.