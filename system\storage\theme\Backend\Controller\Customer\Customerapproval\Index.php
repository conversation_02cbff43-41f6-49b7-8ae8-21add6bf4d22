<?php

namespace Theme25\Backend\Controller\Customer\Customerapproval;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме customer-approval.js, ако съществува
        $customerApprovalJsUrl = $base . 'backend_js/customer-approval.js';
        $customerApprovalJsPath = DIR_THEME . 'Backend/View/Javascript/customer-approval.js';
        
        if (file_exists($customerApprovalJsPath)) {
            $lastModified = filemtime($customerApprovalJsPath);
            $customerApprovalJsUrl .= '?v=' . $lastModified;
            $this->document->addScript($customerApprovalJsUrl, 'footer');
        }
    }

    /**
     * Изпълнява подготовката на данните за списъка с одобрения на клиенти
     */
    public function execute() {
        $this->setTitle('Одобрения на клиенти');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('customer/customer_approval');
    }

    /**
     * Подготвя данните за списъка с одобрения на клиенти
     */
    public function prepareData() {
        $this->prepareFilterData()
             ->prepareCustomerApprovalData()
             ->preparePagination()
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за филтриране
     */
    private function prepareFilterData() {
        // Филтри от GET заявката
        $filter_name = $this->requestGet('filter_name', '');
        $filter_email = $this->requestGet('filter_email', '');
        $filter_customer_group_id = $this->requestGet('filter_customer_group_id', '');
        $filter_type = $this->requestGet('filter_type', '');
        $filter_date_added = $this->requestGet('filter_date_added', '');

        // Сортиране и подредба
        $sort = $this->requestGet('sort', 'ca.date_added');
        $order = $this->requestGet('order', 'DESC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'filter_name' => $filter_name,
            'filter_email' => $filter_email,
            'filter_customer_group_id' => $filter_customer_group_id,
            'filter_type' => $filter_type,
            'filter_date_added' => $filter_date_added,
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        return $this;
    }

    /**
     * Подготвя данните за одобренията на клиенти
     */
    private function prepareCustomerApprovalData() {
        $this->load->model('customer/customer_approval');
        $this->load->model('customer/customer_group');

        $filter_data = [
            'filter_name' => $this->data['filter_name'],
            'filter_email' => $this->data['filter_email'],
            'filter_customer_group_id' => $this->data['filter_customer_group_id'],
            'filter_type' => $this->data['filter_type'],
            'filter_date_added' => $this->data['filter_date_added'],
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        $customer_approval_total = $this->model_customer_customer_approval->getTotalCustomerApprovals($filter_data);
        $results = $this->model_customer_customer_approval->getCustomerApprovals($filter_data);

        $customer_approvals = [];
        foreach ($results as $result) {
            $customer_approvals[] = [
                'customer_id' => $result['customer_id'],
                'name' => $result['name'],
                'email' => $result['email'],
                'customer_group' => $result['customer_group'],
                'type' => $result['type'] == 'customer' ? 'Клиент' : 'Партньор',
                'date_added' => date('d.m.Y H:i', strtotime($result['date_added'])),
                'approve' => $this->getAdminLink('customer/customer_approval/approve', 'customer_id=' . $result['customer_id'] . '&type=' . $result['type']),
                'deny' => $this->getAdminLink('customer/customer_approval/deny', 'customer_id=' . $result['customer_id'] . '&type=' . $result['type'])
            ];
        }

        $this->setData([
            'customer_approvals' => $customer_approvals,
            'customer_approval_total' => $customer_approval_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['customer_approval_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('customer/customer_approval', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('customer/customer_approval', $filter_params . '&limit={limit}'));
        $pagination->setProductText('одобрения');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)', 
                ($this->data['page'] - 1) * $limit + 1, 
                min($this->data['customer_approval_total'], $this->data['page'] * $limit), 
                $this->data['customer_approval_total'], 
                ceil($this->data['customer_approval_total'] / $limit)
            )
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        // Зареждане на клиентските групи за филтъра
        $this->load->model('customer/customer_group');
        $customer_groups = $this->model_customer_customer_group->getCustomerGroups();

        $this->setData([
            'customer_groups' => $customer_groups,
            'user_token' => $this->session->data['user_token']
        ]);

        return $this;
    }

    /**
     * Генерира параметрите за филтриране в URL
     */
    private function buildFilterParams() {
        $params = [];

        if (!empty($this->data['filter_name'])) {
            $params[] = 'filter_name=' . urlencode($this->data['filter_name']);
        }

        if (!empty($this->data['filter_email'])) {
            $params[] = 'filter_email=' . urlencode($this->data['filter_email']);
        }

        if (!empty($this->data['filter_customer_group_id'])) {
            $params[] = 'filter_customer_group_id=' . $this->data['filter_customer_group_id'];
        }

        if (!empty($this->data['filter_type'])) {
            $params[] = 'filter_type=' . $this->data['filter_type'];
        }

        if (!empty($this->data['filter_date_added'])) {
            $params[] = 'filter_date_added=' . urlencode($this->data['filter_date_added']);
        }

        if (!empty($this->data['sort'])) {
            $params[] = 'sort=' . $this->data['sort'];
        }

        if (!empty($this->data['order'])) {
            $params[] = 'order=' . $this->data['order'];
        }

        return implode('&', $params);
    }
}
