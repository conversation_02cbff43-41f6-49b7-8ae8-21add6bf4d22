<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Prepairform extends \Theme25\ControllerSubMethods {

    private $product_id = 0;
    private $product_info = [];

    public function execute() {
        $this->initialize();
        $this->setupPage();
        $this->prepareLanguageData();
        $this->prepareGeneralAndDescriptionData();
        $this->prepareImageData();
        $this->prepareLinksData();
        $this->prepareSpecialData();
        $this->prepareAttributeData();
        $this->prepareOptionData();
        $this->prepareSeoData();
        $this->prepareRelatedProductsData();
        $this->prepareViewData();
    }

    /**
     * Инициализира основни данни и модели.
     */
    private function initialize() {
        $this->loadLanguage('catalog/product');
        $this->initAdminData();

        $this->loadModelsAs([
            'catalog/attribute'       => 'attributeModel',
            'catalog/product'         => 'productModel',
            'catalog/category'        => 'categoryModel',
            'catalog/manufacturer'    => 'manufacturerModel',
            'catalog/option'          => 'optionModel',
            'localisation/language'   => 'languageModel',
            'setting/store'           => 'storeModel',
            'tool/image'              => 'imageModel',
            'tool/Imageservice'       => 'imageService'
        ]);

        $this->product_id = $this->requestGet('product_id', 0);

        if ($this->product_id && $this->request->server['REQUEST_METHOD'] != 'POST') {
            $this->product_info = $this->productModel->getProduct($this->product_id);
        }
    }

    /**
     * Настройва основните елементи на страницата като заглавие, URL за действие и др.
     */
    private function setupPage() {
        if ($this->product_id) {
            $this->setTitle('Редактиране на продукт');
            $this->setData('action', $this->getAdminLink('catalog/product/edit', 'product_id=' . $this->product_id, true));
        } else {
            $this->setTitle('Добавяне на продукт');
            $this->setData('action', $this->getAdminLink('catalog/product/add', '', true));
        }

        $this->setData('site_url', HTTPS_CATALOG);
        $this->setData('product_id', $this->product_id);
        $this->setBackUrl();
        $this->setData('catalog_controller', $this->getAdminLink('catalog/product/edit', '', true));
    }

    /**
     * Подготвя данните за езиците.
     */
    private function prepareLanguageData() {
        $active_language_id = $this->getConfig('config_language_id');
        $active_language_id_sdb = $this->getConfigFromSecondDB('config_language_id');

        if (!empty($active_language_id_sdb)) {
            $active_language_id = $active_language_id_sdb;
        }

        $languages_data = $this->languageModel->getLanguages();
        $languages = [];
        $language_exists = false;

        foreach ($languages_data as $language) {
            $languages[] = [
                'language_id' => $language['language_id'],
                'code' => $language['code'],
                'name' => $language['name'],
                'css' => $language['code'] == 'bg-bg' ? ' bg-languagebg' : ' bg-languageen'
            ];
            if ($language['language_id'] == $active_language_id) {
                $language_exists = true;
            }
        }

        $this->setData('languages', $languages);

        if (!$language_exists && !empty($languages)) {
            $active_language_id = $languages[0]['language_id'];
        }

        $this->setData('active_language_id', $active_language_id);
    }

    /**
     * Подготвя основните данни за продукта и многоезичните описания.
     */
    private function prepareGeneralAndDescriptionData() {
        // Стойности по подразбиране
        $default_values = [
            'model'             => '', 'sku'               => '', 'upc'               => '',
            'ean'               => '', 'jan'               => '', 'isbn'              => '',
            'mpn'               => '', 'location'          => '', 'price'             => '0.00',
            'tax_class_id'      => '0', 'quantity'          => 1, 'minimum'           => 1,
            'subtract'          => 1, 'stock_status_id'   => $this->getConfig('config_stock_status_id'),
            'shipping'          => 1, 'date_available'    => date('Y-m-d'),
            'length'            => '0.00', 'width'             => '0.00', 'height'            => '0.00',
            'length_class_id'   => $this->getConfig('config_length_class_id'),
            'weight'            => '0.00', 'weight_class_id'   => $this->getConfig('config_weight_class_id'),
            'status'            => 1, 'sort_order'        => 1, 'manufacturer_id'   => 0,
            'image'             => '', 'main_category_id'  => 0
        ];

        $product_data = !empty($this->product_info) ? array_merge($default_values, $this->product_info) : $default_values;
        $float_fields = ['price', 'length', 'width', 'height', 'weight'];

        foreach ($product_data as $field => $value) {
            $current_value = $this->requestPost($field, $value);
            if (in_array($field, $float_fields)) {
                $current_value = number_format((float)str_replace(',', '.', (string)$current_value), 2, '.', '');
            }
            $this->setData($field, $current_value);
        }

        // Product Description (многоезични полета)
        $product_description_post = $this->requestPost('product_description');
        if ($product_description_post !== null) {
            $this->setData('product_description', $product_description_post);
        } elseif ($this->product_id) {
            $this->setData('product_description', $this->productModel->getProductDescriptions($this->product_id));
        } else {
            $product_description = [];
            foreach ($this->data['languages'] as $language) {
                $product_description[$language['language_id']] = [
                    'name' => '', 'description' => '', 'tag' => '',
                    'meta_title' => '', 'meta_description' => '', 'meta_keyword' => ''
                ];
            }
            $this->setData('product_description', $product_description);
        }
    }

    /**
     * Подготвя основно и допълнителни изображения.
     */
    private function prepareImageData() {
        $this->setData('placeholder', $this->imageModel->resize('no_image.png', 192, 192));
        
        // Основно изображение
        $main_image_path = $this->requestPost('image', $this->product_info['image'] ?? '');
        $this->setData('image', $main_image_path);

        if ($main_image_path && is_file(ThemeData()->getImageServerPath() . $main_image_path)) {
            $image_details = $this->imageService->getImageDetailsByPath($main_image_path, 192, 192);
            $this->setData('thumb', $image_details['resized_image_url']);
        } else {
            $this->setData('thumb', $this->imageModel->resize('no_image.png', 192, 192));
        }

        // Допълнителни изображения
        $product_images_data = $this->requestPost('product_image');
        if ($product_images_data === null && $this->product_id) {
            $product_images_data = $this->productModel->getProductImages($this->product_id);
        } elseif ($product_images_data === null) {
            $product_images_data = [];
        }

        $product_images = [];
        foreach ($product_images_data as $product_image_item) {
            if (empty($product_image_item['image'])) continue;

            $thumb = $this->imageService->getImageDetailsByPath($product_image_item['image'], 192, 192);

            $product_images[] = [
                'image'      => $product_image_item['image'],
                'thumb'      => $thumb,
                'sort_order' => $product_image_item['sort_order'] ?? count($product_images),
            ];
        }

        usort($product_images, fn($a, $b) => (int)$a['sort_order'] - (int)$b['sort_order']);
        $this->setData('product_images', $product_images);
    }
    
    /**
     * Подготвя данни за връзки - категории и производители.
     */
    private function prepareLinksData() {
        // Категории
        $categories_data = $this->categoryModel->getCategories(['sort' => 'name', 'order' => 'ASC']);
        $this->setData('categories', $this->getAllCategoriesFlat($categories_data));
        
        $product_categories = $this->requestPost('product_category');
        if ($product_categories === null && $this->product_id) {
            $product_categories = $this->productModel->getProductCategories($this->product_id);
        }
        $this->setData('product_category', $product_categories ?? []);
        
        // Основна категория
        $this->setData('main_category_id', $this->requestPost('main_category_id', $this->product_info['main_category_id'] ?? 0));

        // Производители
        $manufacturers = $this->manufacturerModel->getManufacturers(['sort' => 'name']);
        $this->setData('manufacturers', $manufacturers);
        $this->setData('manufacturer_id', $this->requestPost('manufacturer_id', $this->product_info['manufacturer_id'] ?? 0));
        
        // Име на производител за бадж
        $manufacturer_name = '';
        if ($this->data['manufacturer_id']) {
            $manufacturer_name = $this->getManufacturerName($this->data['manufacturer_id']);
        }
        $this->setData('manufacturer_name', $manufacturer_name);
    }

    /**
     * Подготвя данните за промоционални цени.
     */
    private function prepareSpecialData() {
        $product_specials = $this->requestPost('product_special');
        if ($product_specials === null && $this->product_id) {
            $product_specials = $this->productModel->getProductSpecials($this->product_id);
        }
        $this->setData('product_special', $product_specials ?? []);
    }
    
    /**
     * Подготвя данните за атрибути.
     */
    private function prepareAttributeData() {
        $_attributes = $this->attributeModel->getAttributes();
        $attributes = [];
        foreach ($_attributes as $attribute) {
            $attributes[$attribute['attribute_id']][$attribute['language_id']] = [
                'name' => $attribute['name'],
                'attribute_id' => $attribute['attribute_id'],
            ];
        }
        $this->setData('attributes', $attributes);

        $product_attributes = $this->requestPost('product_attribute');
        if ($product_attributes === null && $this->product_id) {
            $product_attributes = $this->productModel->getProductAttributes($this->product_id);
        }
        $this->setData('product_attribute', $product_attributes ?? []);
    }

    /**
     * Подготвя данните за опции.
     */
    private function prepareOptionData() {
        $product_options = $this->requestPost('product_option');
        if ($product_options === null && $this->product_id) {
            $product_options = $this->productModel->getProductOptions($this->product_id);
        }
        $this->setData('product_option', $product_options ?? []);
    }
    
    /**
     * Подготвя данните за SEO URL.
     */
    private function prepareSeoData() {
        $product_seo_url_post = $this->requestPost('product_seo_url');
        if ($product_seo_url_post !== null) {
            $this->setData('product_seo_url', $product_seo_url_post);
        } elseif ($this->product_id) {
            $_product_seo_urls = $this->productModel->getProductSeoUrls($this->product_id);
            $product_seo_urls = [];
            foreach ($_product_seo_urls as $seo_url_per_store) {
                foreach ($seo_url_per_store as $language_id => $keyword) {
                    $product_seo_urls[$language_id] = $keyword;
                }
            }
            $this->setData('product_seo_url', $product_seo_urls);
        } else {
            $this->setData('product_seo_url', []);
        }
    }

    /**
     * Подготвя данните за свързани продукти.
     */
    private function prepareRelatedProductsData() {
        $product_related_ids = $this->requestPost('product_related');
        if ($product_related_ids === null && $this->product_id) {
            $product_related_ids = $this->productModel->getProductRelated($this->product_id);
        } elseif ($product_related_ids === null) {
            $product_related_ids = [];
        }

        $product_related = [];
        if (!empty($product_related_ids)) {
            $current_lang_id = $this->getConfig('config_language_id');
            foreach ($product_related_ids as $related_id) {
                $related_info = $this->productModel->getProduct($related_id);
                if ($related_info) {
                    $description = $this->productModel->getProductDescriptions($related_info['product_id']);
                    $name = $description[$current_lang_id]['name'] ?? reset($description)['name'] ?? '';
                    
                    $thumb = !empty($related_info['image'])
                        ? $this->imageService->getImageDetailsByPath($related_info['image'], 48, 48)['resized_image_url']
                        : $this->imageModel->resize('no_image.png', 48, 48);
                    
                    $product_related[] = [
                        'product_id' => $related_info['product_id'],
                        'name'       => $name,
                        'model'      => $related_info['model'],
                        'thumb'      => $thumb
                    ];
                }
            }
        }
        $this->setData('product_related', $product_related);
    }
    
    /**
     * Зарежда финални данни за изгледа като статуси, класове и др.
     */
    private function prepareViewData() {
        $this->loadModelsAs([
            'localisation/stock_status' => 'stockStatusModel',
            'localisation/tax_class'    => 'taxClassModel',
            'localisation/length_class' => 'lengthClassModel',
            'localisation/weight_class' => 'weightClassModel'
        ]);

        $this->setData([
            'stock_statuses' => $this->stockStatusModel->getStockStatuses(),
            'tax_classes'    => $this->taxClassModel->getTaxClasses(),
            'length_classes' => $this->lengthClassModel->getLengthClasses(),
            'weight_classes' => $this->weightClassModel->getWeightClasses(),
            'option_values'  => $this->getOptionValues(),
            'all_options'    => $this->getAllOptions(),
        ]);
    }
    
    private function setBackUrl() {
        $this->setData('back_url', $this->getAdminLink('catalog/product', '', true, ['product_id']));
    }
    
    private function getAllCategoriesFlat($categories) {
        $category_data = [];
        foreach ($categories as $category) {
            $category_data[$category['parent_id']][] = $category;
        }
        return $this->buildCategoryFlatList($category_data);
    }

    private function buildCategoryFlatList($categories_data, $parent_id = 0, $path = '') {
        $output = [];
        if (isset($categories_data[$parent_id])) {
            foreach ($categories_data[$parent_id] as $category) {
                $current_path = $path . $category['name'];
                $output[$category['category_id']] = [
                    'category_id' => $category['category_id'],
                    'name'        => $current_path,
                    'original_name' => $category['name'] // Добавяме оригиналното име
                ];
                $children = $this->buildCategoryFlatList($categories_data, $category['category_id'], $current_path . ' &gt; ');
                $output += $children;
            }
        }
        return $output;
    }

    private function getManufacturerName($manufacturer_id) {
        foreach ($this->data['manufacturers'] as $manufacturer) {
            if ($manufacturer['manufacturer_id'] == $manufacturer_id) {
                return $manufacturer['name'];
            }
        }
        return '';
    }

    private function getOptionValues() {
        $option_values = [];
        $options = $this->optionModel->getOptions();

        foreach ($options as $option) {
            $values = $this->optionModel->getOptionValues($option['option_id']);
            foreach ($values as $value) {
                $option_values[] = [
                    'option_value_id' => $value['option_value_id'],
                    'option_id'       => $option['option_id'],
                    'option_name'     => $option['name'],
                    'name'            => $value['name'],
                    'sort_order'      => $value['sort_order']
                ];
            }
        }
        
        usort($option_values, function($a, $b) {
            $option_compare = strcmp($a['option_name'], $b['option_name']);
            return $option_compare === 0 ? strcmp($a['name'], $b['name']) : $option_compare;
        });

        return $option_values;
    }

    private function getAllOptions() {
        $options = $this->optionModel->getOptions();
        $all_options = [];

        foreach ($options as $option) {
            $all_options[] = [
                'option_id' => $option['option_id'],
                'name'      => $option['name'],
                'type'      => $option['type']
            ];
        }

        return $all_options;
    }
}