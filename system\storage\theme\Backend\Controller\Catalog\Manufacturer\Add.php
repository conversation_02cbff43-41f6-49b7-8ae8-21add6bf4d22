<?php

namespace Theme25\Backend\Controller\Catalog\Manufacturer;

class Add extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'manufacturer-form.js',
            'image-manager.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Добавяне на производител');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareManufacturerForm();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/manufacturer_form');
    }

    /**
     * Подготвя формата за добавяне на производител
     */
    private function prepareManufacturerForm() {
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel',
            'localisation/language' => 'languageModel',
            'setting/store' => 'storeModel'
        ]);

        // Подготвяне на основните данни
        $this->prepareBasicData()
             ->prepareLanguageData()
             ->prepareStoreData()
             ->prepareSeoUrls()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя основните данни за формата
     */
    private function prepareBasicData() {
        $this->setData([
            'manufacturer_id' => 0,
            'name' => '',
            'image' => '',
            'sort_order' => 0,
            'manufacturer_store' => [0] // По подразбиране всички магазини
        ]);

        return $this;
    }

    /**
     * Подготвя данните за езиците
     */
    private function prepareLanguageData() {
        $languages = $this->languageModel->getLanguages();
        
        $this->setData([
            'languages' => $languages,
            'language_id' => $this->getLanguageId()
        ]);

        return $this;
    }

    /**
     * Подготвя данните за магазините
     */
    private function prepareStoreData() {
        $stores = $this->storeModel->getStores();
        
        // Добавяне на основния магазин
        $store_data = [];
        $store_data[] = [
            'store_id' => 0,
            'name' => $this->getLanguageText('text_default')
        ];
        
        foreach ($stores as $store) {
            $store_data[] = [
                'store_id' => $store['store_id'],
                'name' => $store['name']
            ];
        }

        $this->setData([
            'stores' => $store_data
        ]);

        return $this;
    }

    /**
     * Подготвя SEO URL данните
     */
    private function prepareSeoUrls() {
        $manufacturer_seo_url = [];
        
        foreach ($this->data['stores'] as $store) {
            $manufacturer_seo_url[$store['store_id']] = [];
            
            foreach ($this->data['languages'] as $language) {
                $manufacturer_seo_url[$store['store_id']][$language['language_id']] = '';
            }
        }

        $this->setData([
            'manufacturer_seo_url' => $manufacturer_seo_url
        ]);

        return $this;
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        $this->setData([
            'action' => $this->getAdminLink('catalog/manufacturer/save'),
            'cancel' => $this->getAdminLink('catalog/manufacturer'),
            'image_manager' => $this->getAdminLink('common/filemanager'),
            'placeholder' => ThemeData()->getImageServerUrl() . 'placeholder.png'
        ]);

        return $this;
    }

    /**
     * Подготвя данните за валидация
     */
    private function prepareValidationData() {
        $this->setData([
            'error_warning' => '',
            'error_name' => '',
            'error_keyword' => []
        ]);

        return $this;
    }

    /**
     * Подготвя данните за изображения
     */
    private function prepareImageData() {
        $image_thumb = '';
        
        if ($this->data['image'] && is_file(DIR_IMAGE . $this->data['image'])) {
            $image_thumb = $this->model_tool_image->resize($this->data['image'], 100, 100);
        } else {
            $image_thumb = $this->model_tool_image->resize('no_image.png', 100, 100);
        }

        $this->setData([
            'thumb' => $image_thumb
        ]);

        return $this;
    }

    /**
     * Подготвя данните за следващия sort_order
     */
    private function prepareNextSortOrder() {
        // Получаване на следващия sort_order
        $query = $this->db->query("SELECT MAX(sort_order) as max_sort FROM " . DB_PREFIX . "manufacturer");
        $next_sort_order = $query->row['max_sort'] ? $query->row['max_sort'] + 1 : 0;

        $this->setData([
            'sort_order' => $next_sort_order
        ]);

        return $this;
    }

    /**
     * Подготвя данните за мултиезичност
     */
    private function prepareMultiLanguageData() {
        // За manufacturer в OpenCart няма мултиезични полета освен SEO URL
        // Но можем да подготвим структурата за бъдещи разширения
        
        $manufacturer_description = [];
        
        foreach ($this->data['languages'] as $language) {
            $manufacturer_description[$language['language_id']] = [
                'meta_title' => '',
                'meta_description' => '',
                'meta_keyword' => '',
                'description' => ''
            ];
        }

        $this->setData([
            'manufacturer_description' => $manufacturer_description
        ]);

        return $this;
    }

    /**
     * Подготвя данните за grid layout
     */
    private function prepareGridLayout() {
        $this->setData([
            'grid_columns' => 2, // Две колони за по-добра организация
            'form_groups' => [
                'basic' => [
                    'title' => 'Основни данни',
                    'fields' => ['name', 'image', 'sort_order']
                ],
                'stores' => [
                    'title' => 'Магазини',
                    'fields' => ['manufacturer_store']
                ],
                'seo' => [
                    'title' => 'SEO настройки',
                    'fields' => ['manufacturer_seo_url']
                ]
            ]
        ]);

        return $this;
    }
}
