<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Delete extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Изтриване на категория
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'catalog/product' => 'productModel'
        ]);

        $category_id = $this->requestPost('category_id', 0);

        if (!$category_id) {
            $json['error'] = 'Невалиден ID на категория';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка дали категорията съществува
        $category_info = $this->categoryModel->getCategory($category_id);
        if (!$category_info) {
            $json['error'] = 'Категорията не съществува';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка дали категорията има подкатегории
        $subcategories = $this->categoryModel->getCategories(['filter_parent_id' => $category_id]);
        if (!empty($subcategories)) {
            $json['error'] = 'Не можете да изтриете категория, която има подкатегории. Първо изтрийте или преместете подкатегориите.';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка дали категорията има продукти
        $products_count = $this->productModel->getTotalProducts(['filter_category_id' => $category_id]);
        if ($products_count > 0) {
            $json['error'] = 'Не можете да изтриете категория, която съдържа продукти (' . $products_count . ' продукта). Първо преместете или изтрийте продуктите.';
            $this->setJSONResponseOutput($json);
            return;
        }

        try {
            // Изтриване на категорията
            $this->categoryModel->deleteCategory($category_id);
            
            $json['success'] = 'Категорията "' . $category_info['name'] . '" беше успешно изтрита';
            $json['redirect'] = $this->getAdminLink('catalog/category');

        } catch (Exception $e) {
            $json['error'] = 'Грешка при изтриване на категорията: ' . $e->getMessage();
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Масово изтриване на категории
     */
    public function deleteMultiple() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        $category_ids = $this->requestPost('selected', []);

        if (empty($category_ids) || !is_array($category_ids)) {
            $json['error'] = 'Моля изберете категории за изтриване';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'catalog/product' => 'productModel'
        ]);

        $deleted_count = 0;
        $errors = [];

        foreach ($category_ids as $category_id) {
            $category_id = (int)$category_id;
            
            if (!$category_id) {
                continue;
            }

            // Проверка дали категорията съществува
            $category_info = $this->categoryModel->getCategory($category_id);
            if (!$category_info) {
                $errors[] = 'Категория с ID ' . $category_id . ' не съществува';
                continue;
            }

            // Проверка дали категорията има подкатегории
            $subcategories = $this->categoryModel->getCategories(['filter_parent_id' => $category_id]);
            if (!empty($subcategories)) {
                $errors[] = 'Категория "' . $category_info['name'] . '" има подкатегории и не може да бъде изтрита';
                continue;
            }

            // Проверка дали категорията има продукти
            $products_count = $this->productModel->getTotalProducts(['filter_category_id' => $category_id]);
            if ($products_count > 0) {
                $errors[] = 'Категория "' . $category_info['name'] . '" съдържа ' . $products_count . ' продукта и не може да бъде изтрита';
                continue;
            }

            try {
                // Изтриване на категорията
                $this->categoryModel->deleteCategory($category_id);
                $deleted_count++;

            } catch (Exception $e) {
                $errors[] = 'Грешка при изтриване на категория "' . $category_info['name'] . '": ' . $e->getMessage();
            }
        }

        if ($deleted_count > 0) {
            $json['success'] = 'Успешно изтрити ' . $deleted_count . ' категории';
            
            if (!empty($errors)) {
                $json['warning'] = 'Някои категории не бяха изтрити: ' . implode('; ', $errors);
            }
        } else {
            $json['error'] = 'Нито една категория не беше изтрита. ' . implode('; ', $errors);
        }

        $json['redirect'] = $this->getAdminLink('catalog/category');

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }
}
