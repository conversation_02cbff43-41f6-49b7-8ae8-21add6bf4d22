<?php

namespace Theme25\Backend\Controller\Catalog\Attribute;

class Add extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'attribute-form.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Добавяне на атрибут');
        
        // Инициализиране на данните
        $this->initAdminData();
        
        $this->prepareAttributeForm();
        
        $this->renderTemplateWithDataAndOutput('catalog/attribute_form');
    }
    
    /**
     * Подготвя формата за добавяне на атрибут
     */
    private function prepareAttributeForm() {
        $this->loadModelsAs([
            'catalog/attribute_group' => 'attributeGroupModel',
            'localisation/language' => 'languageModel'
        ]);

        // Подготвяне на основните данни
        $this->prepareBasicData()
             ->prepareLanguageData()
             ->prepareAttributeGroupData()
             ->prepareFormUrls();

        return $this;
    }

    /**
     * Подготвя основните данни за формата
     */
    private function prepareBasicData() {
        $this->setData([
            'attribute_id' => 0,
            'sort_order' => 0,
            'attribute_group_id' => 0
        ]);

        return $this;
    }

    /**
     * Подготвя данните за езиците
     */
    private function prepareLanguageData() {
        $languages = $this->languages;
        $this->setData('languages', $languages);

        // Подготвяне на празни описания за всички езици
        $attribute_description = [];
        foreach ($languages as $language) {
            $attribute_description[$language['language_id']] = [
                'name' => ''
            ];
        }
        
        $this->setData('attribute_description', $attribute_description);

        return $this;
    }

    /**
     * Подготвя данните за групите атрибути
     */
    private function prepareAttributeGroupData() {
        $attribute_groups = $this->attributeGroupModel->getAttributeGroups();
        
        // Подготвяне на данните за dropdown
        $attribute_group_options = [];
        foreach ($attribute_groups as $group) {
            $attribute_group_options[] = [
                'attribute_group_id' => $group['attribute_group_id'],
                'name' => $group['name']
            ];
        }
        
        $this->setData('attribute_groups', $attribute_group_options);

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls() {
        $this->setData([
            'action' => $this->getAdminLink('catalog/attribute/save'),
            'cancel' => $this->getAdminLink('catalog/attribute'),
            'back_url' => $this->getAdminLink('catalog/attribute')
        ]);

        return $this;
    }

    /**
     * Валидира данните от формата
     */
    public function validateForm($data) {
        $errors = [];

        // Проверка за група атрибути
        if (empty($data['attribute_group_id']) || !is_numeric($data['attribute_group_id'])) {
            $errors['attribute_group'] = 'Моля, изберете група атрибути';
        }

        // Проверка за описания на атрибута
        if (empty($data['attribute_description']) || !is_array($data['attribute_description'])) {
            $errors['attribute_description'] = 'Липсват описания на атрибута';
        } else {
            foreach ($data['attribute_description'] as $language_id => $description) {
                if (empty($description['name']) || strlen(trim($description['name'])) < 1) {
                    $errors['name'][$language_id] = 'Името на атрибута е задължително';
                } elseif (strlen($description['name']) > 64) {
                    $errors['name'][$language_id] = 'Името на атрибута не може да бъде по-дълго от 64 символа';
                }
            }
        }

        // Проверка за sort_order
        if (isset($data['sort_order']) && !is_numeric($data['sort_order'])) {
            $errors['sort_order'] = 'Подредбата трябва да бъде число';
        }

        return $errors;
    }
}
