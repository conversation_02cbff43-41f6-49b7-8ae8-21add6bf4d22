<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class TestTables extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Тества дали всички нужни таблици съществуват
     */
    public function execute() {
        $json = [];
        
        if (!$this->hasPermission('access', 'customer/customer')) {
            $json['error'] = 'Нямате права за достъп!';
        } else {
            $tables = [
                'customer' => 'Основна таблица за клиенти',
                'customer_login' => 'Таблица за login опити',
                'customer_ip' => 'Таблица за IP адреси на клиенти',
                'customer_ban_ip' => 'Таблица за блокирани IP адреси',
                'customer_login_token' => 'Таблица за login токени',
                'customer_transaction' => 'Таблица за транзакции'
            ];
            
            $results = [];
            
            foreach ($tables as $table => $description) {
                $exists = $this->checkTableExists($table);
                $results[$table] = [
                    'exists' => $exists,
                    'description' => $description,
                    'full_name' => DB_PREFIX . $table
                ];
            }
            
            $json['success'] = 'Проверката на таблиците завърши успешно';
            $json['tables'] = $results;
            $json['db_prefix'] = DB_PREFIX;
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали таблица съществува
     */
    private function checkTableExists($tableName) {
        try {
            $result = $this->db->query("SHOW TABLES LIKE '" . DB_PREFIX . $tableName . "'");
            return $result->num_rows > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Създава всички нужни таблици
     */
    public function createTables() {
        $json = [];
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за модификация!';
        } else {
            $created = [];
            $errors = [];
            
            // customer_ip таблица
            try {
                $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_ip` (
                    `customer_ip_id` int(11) NOT NULL AUTO_INCREMENT,
                    `customer_id` int(11) NOT NULL,
                    `ip` varchar(40) NOT NULL,
                    `date_added` datetime NOT NULL,
                    PRIMARY KEY (`customer_ip_id`),
                    KEY `customer_id` (`customer_id`),
                    KEY `ip` (`ip`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
                $created[] = 'customer_ip';
            } catch (Exception $e) {
                $errors[] = 'customer_ip: ' . $e->getMessage();
            }
            
            // customer_ban_ip таблица
            try {
                $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_ban_ip` (
                    `customer_ban_ip_id` int(11) NOT NULL AUTO_INCREMENT,
                    `ip` varchar(40) NOT NULL,
                    `date_added` datetime NOT NULL,
                    PRIMARY KEY (`customer_ban_ip_id`),
                    UNIQUE KEY `ip` (`ip`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
                $created[] = 'customer_ban_ip';
            } catch (Exception $e) {
                $errors[] = 'customer_ban_ip: ' . $e->getMessage();
            }
            
            // customer_login_token таблица
            try {
                $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_login_token` (
                    `customer_login_token_id` int(11) NOT NULL AUTO_INCREMENT,
                    `customer_id` int(11) NOT NULL,
                    `token` varchar(64) NOT NULL,
                    `date_added` datetime NOT NULL,
                    `expires` datetime NOT NULL,
                    PRIMARY KEY (`customer_login_token_id`),
                    UNIQUE KEY `token` (`token`),
                    KEY `customer_id` (`customer_id`),
                    KEY `expires` (`expires`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
                $created[] = 'customer_login_token';
            } catch (Exception $e) {
                $errors[] = 'customer_login_token: ' . $e->getMessage();
            }
            
            if (count($created) > 0) {
                $json['success'] = 'Създадени таблици: ' . implode(', ', $created);
            }
            
            if (count($errors) > 0) {
                $json['errors'] = $errors;
            }
            
            if (count($created) == 0 && count($errors) == 0) {
                $json['info'] = 'Всички таблици вече съществуват';
            }
        }
        
        $this->setJSONResponseOutput($json);
    }
}
