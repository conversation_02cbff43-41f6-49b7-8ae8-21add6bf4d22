<?php

namespace Theme25\Backend\Controller\Catalog\Review;

class Autocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * AJAX метод за autocomplete на коментари
     */
    public function autocomplete($request_data = []) {
        $json = [];

        try {
            $this->loadModelsAs([
                'catalog/review' => 'reviewModel'
            ]);

            $filter_name = isset($request_data['filter_name']) ? $request_data['filter_name'] : '';
            $filter_author = isset($request_data['filter_author']) ? $request_data['filter_author'] : '';
            $filter_product = isset($request_data['filter_product']) ? $request_data['filter_product'] : '';
            $limit = min(10, max(1, (int)(isset($request_data['limit']) ? $request_data['limit'] : 10)));

            $filter_data = [
                'start' => 0,
                'limit' => $limit,
                'sort' => 'r.date_added',
                'order' => 'DESC'
            ];

            // Добавяне на филтри ако са зададени
            if (!empty($filter_name)) {
                $filter_data['filter_product'] = $filter_name;
            }

            if (!empty($filter_author)) {
                $filter_data['filter_author'] = $filter_author;
            }

            if (!empty($filter_product)) {
                $filter_data['filter_product'] = $filter_product;
            }

            $reviews = $this->reviewModel->getReviews($filter_data);

            foreach ($reviews as $review) {
                $json[] = [
                    'review_id' => $review['review_id'],
                    'product_name' => strip_tags(html_entity_decode($review['name'] ?? 'Неизвестен продукт', ENT_QUOTES, 'UTF-8')),
                    'author' => strip_tags(html_entity_decode($review['author'], ENT_QUOTES, 'UTF-8')),
                    'rating' => (int)$review['rating'],
                    'status' => (int)$review['status'],
                    'status_text' => $review['status'] ? 'Активен' : 'Неактивен',
                    'date_added' => date('d.m.Y H:i', strtotime($review['date_added']))
                ];
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при търсене: ' . $e->getMessage()];
        }

        return $json;
    }

    /**
     * AJAX метод за търсене на коментари по продукт
     */
    public function searchByProduct() {
        $json = [];

        ob_start();

        try {
            $this->loadModelsAs([
                'catalog/review' => 'reviewModel'
            ]);

            $product_id = (int)$this->requestGet('product_id', 0);
            $limit = min(20, max(1, (int)$this->requestGet('limit', 10)));

            if (!$product_id) {
                $json = ['error' => 'Не е зададен продукт'];
            } else {
                $filter_data = [
                    'filter_product_id' => $product_id,
                    'start' => 0,
                    'limit' => $limit,
                    'sort' => 'r.date_added',
                    'order' => 'DESC'
                ];

                $reviews = $this->reviewModel->getReviews($filter_data);

                foreach ($reviews as $review) {
                    $json[] = [
                        'review_id' => $review['review_id'],
                        'author' => strip_tags(html_entity_decode($review['author'], ENT_QUOTES, 'UTF-8')),
                        'text' => strip_tags(html_entity_decode($review['text'], ENT_QUOTES, 'UTF-8')),
                        'rating' => (int)$review['rating'],
                        'status' => (int)$review['status'],
                        'status_text' => $review['status'] ? 'Активен' : 'Неактивен',
                        'date_added' => date('d.m.Y H:i', strtotime($review['date_added']))
                    ];
                }
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при търсене: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json = ['error' => 'Unexpected output: ' . $output];
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX метод за търсене на коментари по автор
     */
    public function searchByAuthor() {
        $json = [];

        ob_start();

        try {
            $this->loadModelsAs([
                'catalog/review' => 'reviewModel'
            ]);

            $author = trim($this->requestGet('author', ''));
            $limit = min(20, max(1, (int)$this->requestGet('limit', 10)));

            if (empty($author)) {
                $json = ['error' => 'Не е зададен автор'];
            } else {
                $filter_data = [
                    'filter_author' => $author,
                    'start' => 0,
                    'limit' => $limit,
                    'sort' => 'r.date_added',
                    'order' => 'DESC'
                ];

                $reviews = $this->reviewModel->getReviews($filter_data);

                foreach ($reviews as $review) {
                    $json[] = [
                        'review_id' => $review['review_id'],
                        'product_name' => strip_tags(html_entity_decode($review['name'] ?? 'Неизвестен продукт', ENT_QUOTES, 'UTF-8')),
                        'author' => strip_tags(html_entity_decode($review['author'], ENT_QUOTES, 'UTF-8')),
                        'text' => mb_substr(strip_tags(html_entity_decode($review['text'], ENT_QUOTES, 'UTF-8')), 0, 100) . '...',
                        'rating' => (int)$review['rating'],
                        'status' => (int)$review['status'],
                        'status_text' => $review['status'] ? 'Активен' : 'Неактивен',
                        'date_added' => date('d.m.Y H:i', strtotime($review['date_added']))
                    ];
                }
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при търсене: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json = ['error' => 'Unexpected output: ' . $output];
        }

        $this->setJSONResponseOutput($json);
    }
}
