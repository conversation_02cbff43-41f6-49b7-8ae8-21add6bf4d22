<?php

namespace Theme25\Backend\Controller\Catalog\Option;

class Autocomplete extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Автозавършване за опции
     */
    public function autocomplete($get) {
        $json = [];

        // Зареждане на модела за опции
        $this->loadModelsAs([
            'catalog/option' => 'optionModel'
        ]);

        // Получаване на параметрите от заявката
        $filter_name = $get['filter_name'] ?? '';
        $filter_type = $get['filter_type'] ?? '';
        $limit = (int)($get['limit'] ?? 10);

        // Ограничаваме лимита между 1 и 50
        $limit = max(1, min(50, $limit));

        // Подготвяне на данните за филтриране
        $filter_data = [
            'filter_name' => $filter_name,
            'start' => 0,
            'limit' => $limit
        ];

        // Филтриране по тип опция ако е зададен
        if (!empty($filter_type)) {
            $filter_data['filter_type'] = $filter_type;
        }

        // Ако няма филтър по име, зареждаме първите опции
        if (empty($filter_name)) {
            $filter_data['sort'] = 'od.name';
            $filter_data['order'] = 'ASC';
        }

        try {
            // Зареждане на опциите
            $options = $this->optionModel->getOptions($filter_data);

            // Подготвяне на резултата
            foreach ($options as $option) {
                $json[] = [
                    'option_id' => $option['option_id'],
                    'name' => strip_tags(html_entity_decode($option['name'], ENT_QUOTES, 'UTF-8')),
                    'type' => $option['type'],
                    'type_text' => $this->getOptionTypeText($option['type']),
                    'sort_order' => $option['sort_order'] ?? 0
                ];
            }

            // Сортиране по име ако има търсене
            if (!empty($filter_name)) {
                usort($json, function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });
            }

        } catch (\Exception $e) {
            // В случай на грешка, връщаме празен резултат
            $json = [];
        }

        return $json;
    }

    /**
     * Получаване на следващата стойност за sort_order според типа опция
     */
    public function getNextSortOrder() {
        $json = [];

        ob_start();

        try {
            $option_type = $this->requestGet('option_type', '');
            
            if (!$option_type) {
                $json = ['error' => 'Невалиден тип опция'];
            } else {
                // Заявка за максималната стойност на sort_order за този тип
                $query = $this->db->query("
                    SELECT MAX(sort_order) as max_sort_order 
                    FROM `" . DB_PREFIX . "option` 
                    WHERE type = '" . $this->db->escape($option_type) . "'
                ");

                $max_sort_order = (int)($query->row['max_sort_order'] ?? 0);
                $next_sort_order = $max_sort_order + 1;

                $json = [
                    'success' => true,
                    'next_sort_order' => $next_sort_order,
                    'max_sort_order' => $max_sort_order,
                    'option_type' => $option_type
                ];
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при изчисляване на sort_order: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json = ['error' => 'Unexpected output: ' . $output];
        }

        return $json;
    }

    /**
     * Търсене на опции с разширени опции
     */
    public function search($get) {
        $json = [];

        // Зареждане на модела за опции
        $this->loadModelsAs([
            'catalog/option' => 'optionModel'
        ]);

        $search_term = $get['search'] ?? '';
        $option_type = $get['option_type'] ?? '';
        $limit = (int)($get['limit'] ?? 15);
        $page = max(1, (int)($get['page'] ?? 1));

        $limit = max(1, min(100, $limit));
        $start = ($page - 1) * $limit;

        $filter_data = [
            'filter_name' => $search_term,
            'sort' => 'od.name',
            'order' => 'ASC',
            'start' => $start,
            'limit' => $limit
        ];

        if (!empty($option_type)) {
            $filter_data['filter_type'] = $option_type;
        }

        try {
            $options = $this->optionModel->getOptions($filter_data);
            $total = $this->optionModel->getTotalOptions($filter_data);

            $results = [];
            foreach ($options as $option) {
                $results[] = [
                    'option_id' => $option['option_id'],
                    'name' => strip_tags(html_entity_decode($option['name'], ENT_QUOTES, 'UTF-8')),
                    'type' => $option['type'],
                    'type_text' => $this->getOptionTypeText($option['type']),
                    'sort_order' => $option['sort_order'] ?? 0
                ];
            }

            $json = [
                'results' => $results,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($total / $limit)
            ];

        } catch (\Exception $e) {
            $json = [
                'results' => [],
                'total' => 0,
                'page' => 1,
                'limit' => $limit,
                'total_pages' => 0,
                'error' => 'Грешка при търсене на опции'
            ];
        }

        return $json;
    }

    /**
     * Получава текстовото представяне на типа опция
     */
    private function getOptionTypeText($type) {
        $types = [
            'select' => 'Select',
            'radio' => 'Radio',
            'checkbox' => 'Checkbox',
            'text' => 'Text',
            'textarea' => 'Textarea',
            'file' => 'File',
            'date' => 'Date',
            'time' => 'Time',
            'datetime' => 'Date & Time'
        ];
        
        return $types[$type] ?? $type;
    }

    /**
     * Зареждане на модели с псевдоними
     */
    private function loadModelsAs($models) {
        foreach ($models as $model => $alias) {
            $this->load->model($model);
            $this->{$alias} = $this->{'model_' . str_replace('/', '_', $model)};
        }
    }
}
