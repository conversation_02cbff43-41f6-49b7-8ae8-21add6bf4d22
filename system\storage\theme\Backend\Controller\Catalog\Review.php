<?php

namespace Theme25\Backend\Controller\Catalog;

class Review extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/review');
    }

    /**
     * Главна страница с коментари - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Catalog/Review/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Коментари');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/review');
        }
    }

    /**
     * Добавяне на нов коментар - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Catalog/Review/Add', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на коментар');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/review_form');
        }
    }

    /**
     * Редактиране на коментар - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Catalog/Review/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на коментар');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/review_form');
        }
    }

    /**
     * Запазване на коментар - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Catalog/Review/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Изтриване на коментар - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Catalog/Review/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Автозавършване за коментари - dispatcher метод
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Review/Autocomplete', $this);
        
        if ($subController && is_callable([$subController, 'autocomplete'])) {
            $json = $subController->autocomplete($this->requestGet());
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX търсене за autocomplete - dispatcher метод
     */
    public function ajaxSearch() {
        $subController = $this->setBackendSubController('Catalog/Review/Index', $this);
        if ($subController && is_callable([$subController, 'ajaxSearch'])) {
            return $subController->ajaxSearch();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }
}
