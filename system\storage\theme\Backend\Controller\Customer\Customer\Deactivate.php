<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Deactivate extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('customers.log');
    }

    /**
     * Изпълнява деактивирането на избрани клиенти
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за деактивиране на клиенти!';
        } else {

            $post = $this->requestPost();

            if (isset($post['selected']) && is_array($post['selected'])) {
                $this->loadModelAs('customer/customer', 'customerModel');

                $deactivated_count = 0;
                $errors = [];
                $deactivated_customers = [];

                foreach ($post['selected'] as $customer_id) {
                    $customer_id = (int)$customer_id;
                    
                    if ($customer_id > 0) {
                        $customer_info = $this->customerModel->getCustomer($customer_id);
                        
                        if ($customer_info) {
                            // Деактивираме клиента
                            $this->dbQuery("UPDATE `" . DB_PREFIX . "customer` SET status = '0' WHERE customer_id = '" . (int)$customer_id . "'");
                            
                            $deactivated_count++;
                            $deactivated_customers[] = [
                                'id' => $customer_id,
                                'name' => $customer_info['firstname'] . ' ' . $customer_info['lastname'],
                                'email' => $customer_info['email']
                            ];
                        } else {
                            $errors[] = 'Клиент с ID ' . $customer_id . ' не съществува!';
                        }
                    }
                }

                if ($deactivated_count > 0) {
                    // Логваме действието
                    $this->logBulkDeactivation($deactivated_customers);
                    
                    $json['success'] = sprintf('Успешно деактивирани %d клиента!', $deactivated_count);
                    $json['deactivated_count'] = $deactivated_count;
                    $json['deactivated_customers'] = $deactivated_customers;
                }

                if (!empty($errors)) {
                    $json['warnings'] = $errors;
                }

                if ($deactivated_count == 0) {
                    $json['error'] = 'Няма клиенти за деактивиране или всички избрани клиенти са невалидни!';
                }
            } else {
                $json['error'] = 'Не са избрани клиенти за деактивиране!';
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Логва bulk деактивиране на клиенти
     */
    private function logBulkDeactivation($deactivated_customers) {
        try {
            $this->loadModelAs('user/user', 'userModel');
            $user_info = $this->userModel->getUser($this->user->getId());
            
            if ($user_info) {
                $customer_names = array_map(function($customer) {
                    return $customer['name'] . ' (' . $customer['email'] . ')';
                }, $deactivated_customers);
                
                $log_message = sprintf(
                    'Администратор %s деактивира %d клиента: %s',
                    $user_info['username'],
                    count($deactivated_customers),
                    implode(', ', $customer_names)
                );
                
                $this->writeLog($log_message);
            }
        } catch (Exception $e) {
            // Не спираме изпълнението при грешка в логването
            $this->writeLog('Грешка при логване на bulk деактивиране: ' . $e->getMessage());
        }
    }

    /**
     * Валидира заявката за деактивиране
     */
    private function validateDeactivationRequest() {
        $errors = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $errors[] = 'Нямате права за деактивиране на клиенти!';
        }

        $post = $this->requestPost();

        if (!isset($post['selected']) || !is_array($post['selected']) || empty($post['selected'])) {
            $errors[] = 'Не са избрани клиенти за деактивиране!';
        }

        return $errors;
    }

    /**
     * Проверява дали клиент съществува
     */
    private function customerExists($customer_id) {
        if(!$this->hasModel('customerModel')) $this->loadModelAs('customer/customer', 'customerModel');
        $customer_info = $this->customerModel->getCustomer($customer_id);
        return !empty($customer_info);
    }

    /**
     * Проверява дали клиент вече е неактивен
     */
    private function isCustomerInactive($customer_id) {
        $query = $this->dbQuery("SELECT status FROM `" . DB_PREFIX . "customer` WHERE customer_id = '" . (int)$customer_id . "'");
        
        if ($query->num_rows) {
            return !(bool)$query->row['status'];
        }
        
        return false;
    }

    /**
     * Получава информация за клиент
     */
    private function getCustomerInfo($customer_id) {
        if(!$this->hasModel('customerModel')) $this->loadModelAs('customer/customer', 'customerModel');
        return $this->customerModel->getCustomer($customer_id);
    }

    /**
     * Деактивира единичен клиент
     */
    private function deactivateCustomer($customer_id) {
        try {
            $this->dbQuery("UPDATE `" . DB_PREFIX . "customer` SET status = '0' WHERE customer_id = '" . (int)$customer_id . "'");
            return true;
        } catch (Exception $e) {
            $this->writeLog('Грешка при деактивиране на клиент ' . $customer_id . ': ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверява дали клиентът има активни поръчки
     */
    private function hasActiveOrders($customer_id) {
        try {
            $this->loadModel('sale/order');
            $orders = $this->model_sale_order->getOrdersByCustomerId($customer_id);
            
            // Проверяваме за активни поръчки (не завършени/отказани)
            foreach ($orders as $order) {
                if (in_array($order['order_status_id'], [1, 2, 3, 15])) { // Pending, Processing, Shipped, Processing
                    return true;
                }
            }
            
            return false;
        } catch (Exception $e) {
            // При грешка предполагаме, че няма активни поръчки
            return false;
        }
    }

    /**
     * Получава статистика за клиента
     */
    private function getCustomerStats($customer_id) {
        try {
            $this->loadModel('sale/order');
            $orders = $this->model_sale_order->getOrdersByCustomerId($customer_id);
            
            return [
                'total_orders' => count($orders),
                'active_orders' => $this->hasActiveOrders($customer_id) ? 1 : 0,
                'last_login' => $this->getLastLoginDate($customer_id)
            ];
        } catch (Exception $e) {
            return [
                'total_orders' => 0,
                'active_orders' => 0,
                'last_login' => null
            ];
        }
    }

    /**
     * Получава датата на последния логин
     */
    private function getLastLoginDate($customer_id) {
        try {
            $query = $this->dbQuery("SELECT date_modified FROM `" . DB_PREFIX . "customer` WHERE customer_id = '" . (int)$customer_id . "'");
            
            if ($query->num_rows) {
                return $query->row['date_modified'];
            }
            
            return null;
        } catch (Exception $e) {
            return null;
        }
    }
}
