<?php

namespace Theme25\Backend\Controller\Customer\Customergroup;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'customer-group.js'
        ], 'footer');
    }

    /**
     * Изпълнява подготовката на данните за списъка с клиентски групи
     */
    public function execute() {
        $this->setTitle('Клиентски групи');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('customer/customer_group');
    }

    /**
     * Подготвя данните за списъка с клиентски групи
     */
    public function prepareData() {
        $this->prepareFilterData()
             ->prepareCustomerGroupData()
             ->preparePagination()
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за филтриране
     */
    private function prepareFilterData() {
        // Сортиране и подредба
        $sort = $this->requestGet('sort', 'cgd.name');
        $order = $this->requestGet('order', 'ASC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        return $this;
    }

    /**
     * Подготвя данните за клиентските групи
     */
    private function prepareCustomerGroupData() {
        $this->load->model('customer/customer_group');

        $filter_data = [
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        $customer_group_total = $this->model_customer_customer_group->getTotalCustomerGroups();
        $results = $this->model_customer_customer_group->getCustomerGroups($filter_data);

        $customer_groups = [];
        foreach ($results as $result) {
            $customer_groups[] = [
                'customer_group_id' => $result['customer_group_id'],
                'name' => $result['name'],
                'description' => $result['description'],
                'approval' => $result['approval'] ? 'Да' : 'Не',
                'sort_order' => $result['sort_order'],
                'edit' => $this->getAdminLink('customer/customer_group/edit', 'customer_group_id=' . $result['customer_group_id']),
                'delete' => $this->getAdminLink('customer/customer_group/delete', 'customer_group_id=' . $result['customer_group_id'])
            ];
        }

        $this->setData([
            'customer_groups' => $customer_groups,
            'customer_group_total' => $customer_group_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['customer_group_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('customer/customer_group', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('customer/customer_group', $filter_params . '&limit={limit}'));
        $pagination->setProductText('клиентски групи');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)', 
                ($this->data['page'] - 1) * $limit + 1, 
                min($this->data['customer_group_total'], $this->data['page'] * $limit), 
                $this->data['customer_group_total'], 
                ceil($this->data['customer_group_total'] / $limit)
            )
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        $this->setData([
            'add_url' => $this->getAdminLink('customer/customer_group/add'),
            'delete_url' => $this->getAdminLink('customer/customer_group/delete'),
            'user_token' => $this->session->data['user_token']
        ]);

        return $this;
    }

    /**
     * Генерира параметрите за филтриране в URL
     */
    private function buildFilterParams() {
        $params = [];

        if (!empty($this->data['sort'])) {
            $params[] = 'sort=' . $this->data['sort'];
        }

        if (!empty($this->data['order'])) {
            $params[] = 'order=' . $this->data['order'];
        }

        return implode('&', $params);
    }
}
