<?php

namespace Theme25\Backend\Controller\Sale\Voucher;

/**
 * Sub-контролер за добавяне на подаръчен ваучер
 */
class Add extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява добавянето на ваучер
     */
    public function execute() {
        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'sale/voucher')) {
            $this->setError('Нямате права за добавяне на ваучери');
            $this->redirectResponse($this->getAdminLink('sale/voucher'));
            return;
        }

        if ($this->isPostRequest()) {
            $this->processAdd();
        } else {
            $this->prepareAddForm();
        }
    }

    /**
     * Обработва POST заявката за добавяне
     */
    private function processAdd() {
        try {
            $this->loadModelAs('sale/voucher', 'voucherModel');
            
            // Получаване на данните от формата
            $data = $this->requestPost();
            
            // Валидация на данните
            if ($this->validateAddData($data)) {
                // Подготвяне на данните за добавяне
                $voucher_data = [
                    'code' => strtoupper(trim($data['code'])),
                    'from_name' => trim($data['from_name']),
                    'from_email' => trim($data['from_email']),
                    'to_name' => trim($data['to_name']),
                    'to_email' => trim($data['to_email']),
                    'voucher_theme_id' => (int)$data['voucher_theme_id'],
                    'message' => trim($data['message'] ?? ''),
                    'amount' => (float)$data['amount'],
                    'status' => isset($data['status']) ? (int)$data['status'] : 1
                ];

                $voucher_id = $this->voucherModel->addVoucher($voucher_data);
                
                if ($voucher_id) {
                    $this->setSession('success', 'Ваучерът е добавен успешно');
                    $this->redirectResponse($this->getAdminLink('sale/voucher'));
                } else {
                    $this->setError('Грешка при добавяне на ваучера');
                }
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при обработка: ' . $e->getMessage());
        }

        // При грешка - показваме отново формата
        $this->prepareAddForm();
    }

    /**
     * Подготвя формата за добавяне
     */
    private function prepareAddForm() {
        try {
            $this->loadModelsAs([
                'sale/voucher_theme' => 'voucherThemeModel'
            ]);

            // Генериране на уникален код за ваучера
            $voucher_code = $this->generateVoucherCode();

            // Получаване на темите за ваучери
            $voucher_themes = $this->voucherThemeModel->getVoucherThemes();
            $theme_options = [];
            foreach ($voucher_themes as $theme) {
                $theme_options[] = [
                    'value' => $theme['voucher_theme_id'],
                    'text' => $theme['name']
                ];
            }

            // Подготвяне на данни за формата
            $form_data = [
                'voucher_id' => 0,
                'code' => $this->requestPost('code', $voucher_code),
                'from_name' => $this->requestPost('from_name', ''),
                'from_email' => $this->requestPost('from_email', ''),
                'to_name' => $this->requestPost('to_name', ''),
                'to_email' => $this->requestPost('to_email', ''),
                'voucher_theme_id' => $this->requestPost('voucher_theme_id', ''),
                'message' => $this->requestPost('message', ''),
                'amount' => $this->requestPost('amount', ''),
                'status' => $this->requestPost('status', 1)
            ];

            $this->setData([
                'form_data' => $form_data,
                'voucher_themes' => $theme_options,
                'action_url' => $this->getAdminLink('sale/voucher/add'),
                'cancel_url' => $this->getAdminLink('sale/voucher'),
                'form_title' => 'Добавяне на подаръчен ваучер',
                'button_save' => 'Запази',
                'button_cancel' => 'Отказ'
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при подготовка на формата: ' . $e->getMessage());
            $this->setData([
                'form_data' => [],
                'voucher_themes' => [],
                'action_url' => $this->getAdminLink('sale/voucher/add'),
                'cancel_url' => $this->getAdminLink('sale/voucher'),
                'form_title' => 'Добавяне на подаръчен ваучер',
                'button_save' => 'Запази',
                'button_cancel' => 'Отказ'
            ]);
        }
    }

    /**
     * Валидира данните за добавяне
     *
     * @param array $data
     * @return bool
     */
    private function validateAddData($data) {
        $errors = [];

        // Валидация на кода
        if (empty($data['code'])) {
            $errors['code'] = 'Кодът на ваучера е задължителен';
        } elseif (strlen($data['code']) < 3 || strlen($data['code']) > 10) {
            $errors['code'] = 'Кодът трябва да бъде между 3 и 10 символа';
        } else {
            // Проверка дали кодът вече съществува
            $this->loadModelAs('sale/voucher', 'voucherModel');
            $existing_voucher = $this->voucherModel->getVoucherByCode($data['code']);
            if ($existing_voucher) {
                $errors['code'] = 'Ваучер с този код вече съществува';
            }
        }

        // Валидация на имената
        if (empty($data['from_name'])) {
            $errors['from_name'] = 'Името на подарителя е задължително';
        }

        if (empty($data['to_name'])) {
            $errors['to_name'] = 'Името на получателя е задължително';
        }

        // Валидация на email адресите
        if (empty($data['from_email'])) {
            $errors['from_email'] = 'Email адресът на подарителя е задължителен';
        } elseif (!filter_var($data['from_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['from_email'] = 'Невалиден email адрес на подарителя';
        }

        if (empty($data['to_email'])) {
            $errors['to_email'] = 'Email адресът на получателя е задължителен';
        } elseif (!filter_var($data['to_email'], FILTER_VALIDATE_EMAIL)) {
            $errors['to_email'] = 'Невалиден email адрес на получателя';
        }

        // Валидация на сумата
        if (empty($data['amount']) || !is_numeric($data['amount'])) {
            $errors['amount'] = 'Сумата е задължителна и трябва да бъде число';
        } elseif ((float)$data['amount'] <= 0) {
            $errors['amount'] = 'Сумата трябва да бъде по-голяма от 0';
        }

        // Валидация на темата
        if (empty($data['voucher_theme_id'])) {
            $errors['voucher_theme_id'] = 'Темата на ваучера е задължителна';
        }

        // Запазване на грешките в данните
        if (!empty($errors)) {
            $this->setData('errors', $errors);
            return false;
        }

        return true;
    }

    /**
     * Генерира уникален код за ваучер
     *
     * @return string
     */
    private function generateVoucherCode() {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code_length = 8;
        
        do {
            $code = '';
            for ($i = 0; $i < $code_length; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }
            
            // Проверка дали кодът вече съществува
            $this->loadModelAs('sale/voucher', 'voucherModel');
            $existing = $this->voucherModel->getVoucherByCode($code);
        } while ($existing);
        
        return $code;
    }
}
