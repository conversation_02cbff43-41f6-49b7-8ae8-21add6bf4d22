<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Export extends \Theme25\ControllerSubMethods {

    private $supportedFormats = [];
    private $unavailableFormats = [];
    private $formatRequirements = [];
    private $selectedCategories = [];
    private $selectedProducts = [];
    private $categoryCache = [];
    private $languageMapping = [];
    private $dataModel;
    private $fieldHelper;

    public function __construct($registry, $parent_controller = null) {
        parent::__construct($registry, $parent_controller);

        // Инициализираме централизираните модели
        $this->initializeCentralizedModels();
    }

    /**
     * Инициализира централизираните модели за експорт
     */
    private function initializeCentralizedModels() {
        // Зареждаме централизираните модели
        $this->loadModelAs('catalog/Productexportdata', 'dataModel');
        $this->loadModelAs('catalog/Exportfieldshelper', 'fieldHelper');

        // Регистрираме ги в registry за достъп от export моделите
        $this->registry->set('exportDataModel', $this->dataModel);
        $this->registry->set('exportFieldHelper', $this->fieldHelper);

        // Задаваме текущия език
        if (is_callable([$this->dataModel, 'setLanguageId'])) {
            $this->dataModel->setLanguageId($this->getLanguageId());
        }
    }

    /**
     * Зарежда export модел за определен формат
     */
    private function loadExportModel($format) {
        $modelName = 'Productexport' . strtolower($format);
        $modelPath = 'catalog/' . $modelName;

        try {
            $this->loadModelAs($modelPath, $modelName);
            $exportModel = $this->{$modelName};

            // Задаваме централизираните модели
            if (is_callable([$exportModel, 'setDataModel'])) {
                $exportModel->setDataModel($this->dataModel);
            }
            if (is_callable([$exportModel, 'setFieldHelper'])) {
                $exportModel->setFieldHelper($this->fieldHelper);
            }

            return $exportModel;
        } catch (\Exception $e) {
            F()->log->developer("Грешка при зареждане на export модел {$format}: " . $e->getMessage(), __FILE__, __LINE__);
            return null;
        }
    }

    /**
     * Проверява наличните формати за експорт
     */
    private function checkAvailableFormats() {
        $this->supportedFormats = [];
        $this->unavailableFormats = [];
        $this->formatRequirements = [];

        // CSV - винаги наличен (вграден в PHP)
        $this->supportedFormats[] = 'csv';
        $this->formatRequirements['csv'] = [
            'name' => 'CSV',
            'description' => 'Comma Separated Values (.csv)',
            'icon' => 'ri-file-text-line',
            'available' => true,
            'reason' => 'Вграден в PHP'
        ];

        // XML - проверка за simplexml разширение
        if (extension_loaded('simplexml')) {
            $this->supportedFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-file-code-line',
                'available' => true,
                'reason' => 'SimpleXML разширение'
            ];
        } else {
            $this->unavailableFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-file-code-line',
                'available' => false,
                'reason' => 'Липсва SimpleXML разширение'
            ];
        }

        // XLSX - проверка за PhpSpreadsheet или алтернативни библиотеки
        $xlsxAvailable = false;
        $xlsxReason = '';

        if (file_exists(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/IOFactory.php')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet';
        }
        else {
            $xlsxReason = 'Липсва PhpOffice\\PhpSpreadsheet библиотека';
        }

        if ($xlsxAvailable) {
            $this->supportedFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => true,
                'reason' => $xlsxReason
            ];

            require_once(VENDORS_DIR . 'phpoffice/PhpSpreadsheet/autoload.php');
        } else {
            $this->unavailableFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => false,
                'reason' => $xlsxReason
            ];
        }

        // Логиране на резултатите
        F()->log->developer('Available export formats: ' . implode(', ', $this->supportedFormats), __FILE__, __LINE__);
        if (!empty($this->unavailableFormats)) {
            F()->log->developer('Unavailable export formats: ' . implode(', ', $this->unavailableFormats), __FILE__, __LINE__);
        }
    }

    /**
     * Инициализира mapping на езиците
     */
    private function initializeLanguageMapping() {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        $this->languageMapping = [];
        foreach ($languages as $language) {
            $this->languageMapping[$language['language_id']] = [
                'name' => $language['name'],
                'code' => $language['code'],
                'locale' => $language['locale']
            ];
        }
    }

    /**
     * Инициализира кеша за категории
     */
    private function initializeCategoryCache() {
        $this->loadModelAs('catalog/category', 'categoryModel');
        $categories = $this->categoryModel->getCategories();

        $this->categoryCache = [];
        foreach ($categories as $category) {
            $this->categoryCache[$category['category_id']] = $category['name'];
        }
    }

    /**
     * Основен метод за показване на формата за експорт
     */
    public function execute() {
        // Проверяваме дали има AJAX заявка за запазване на настройки
        if (isset($_POST['action']) && $_POST['action'] === 'save_export_settings') {
            $this->saveExportSettings();
            return;
        }

        $this->setTitle('Експорт на продукти');
        $this->initAdminData();
        $this->checkAvailableFormats();
        $this->initializeLanguageMapping();
        $this->initializeCategoryCache();
        $this->addBackendScriptWithVersion('product-export.js', 'footer');
        $this->addBackendScriptWithVersion('product-export-fields.js', 'footer');
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_export');
    }

    /**
     * Подготвя данните за template-а
     */
    private function prepareData() {
        // Основни данни
        $this->data['supported_formats'] = $this->supportedFormats;
        $this->data['format_requirements'] = $this->formatRequirements;
        $this->data['unavailable_formats'] = $this->unavailableFormats;
        $this->data['language_mapping'] = $this->languageMapping;

        // Зареждаме полетата за експорт от helper-а
        $this->data['available_fields'] = $this->fieldHelper->getAvailableFields();
        $this->data['group_labels'] = $this->fieldHelper->getGroupLabels();

        // Зареждаме запазените настройки
        $this->data['saved_settings'] = $this->loadExportSettings();

        // Статистики
        $this->loadModelAs('catalog/product', 'productModel');
        $this->data['total_products'] = $this->productModel->getTotalProducts();

        // Категории за autocomplete
        $this->data['categories'] = $this->getCategoriesForAutocomplete();

        // Текущ език
        $this->data['current_language_id'] = $this->getLanguageId();
        $this->data['current_language'] = $this->languageMapping[$this->getLanguageId()] ?? null;
    }

    /**
     * Връща категориите за autocomplete
     */
    private function getCategoriesForAutocomplete() {
        $this->loadModelAs('catalog/category', 'categoryModel');
        $categories = $this->categoryModel->getCategories();

        $result = [];
        foreach ($categories as $category) {
            $result[] = [
                'category_id' => $category['category_id'],
                'name' => $category['name'],
                'parent_id' => $category['parent_id']
            ];
        }

        return $result;
    }

    /**
     * Основен метод за обработка на експорт заявки
     */
    public function process() {
        try {
            // Валидация на заявката
            if (!$this->validateExportRequest()) {
                throw new \Exception('Невалидна заявка за експорт');
            }

            // Получаване на параметрите за експорт
            $exportFormat = $_POST['export_format'] ?? 'csv';
            $this->selectedCategories = $_POST['selected_categories'] ?? [];
            $this->selectedProducts = $_POST['selected_products'] ?? [];

            // Получаване на опциите за експорт (основни категории)
            $exportOptions = [
                'include_quantity' => isset($_POST['include_quantity']) && $_POST['include_quantity'] == '1',
                'include_price' => isset($_POST['include_price']) && $_POST['include_price'] == '1',
                'include_promo_price' => isset($_POST['include_promo_price']) && $_POST['include_promo_price'] == '1',
                'include_descriptions' => isset($_POST['include_descriptions']) && $_POST['include_descriptions'] == '1',
                'include_categories' => isset($_POST['include_categories']) && $_POST['include_categories'] == '1',
                'include_options' => isset($_POST['include_options']) && $_POST['include_options'] == '1',
                'include_images' => isset($_POST['include_images']) && $_POST['include_images'] == '1',
                'include_seo' => isset($_POST['include_seo']) && $_POST['include_seo'] == '1',
                'include_shipping' => isset($_POST['include_shipping']) && $_POST['include_shipping'] == '1'
            ];

            // Получаване на детайлните полета
            $detailedFields = [];
            if (isset($_POST['detailed_fields']) && is_array($_POST['detailed_fields'])) {
                $detailedFields = $_POST['detailed_fields'];
            }

            // Обединяване на данните за експорт
            $exportData = [
                'type' => 'detailed',
                'basic_options' => $exportOptions,
                'detailed_fields' => $detailedFields
            ];

            // Логиране на заявката
            F()->log->developer('Export request: Format=' . $exportFormat . ', Categories=' . count($this->selectedCategories) . ', Products=' . count($this->selectedProducts), __FILE__, __LINE__);

            // Получаване на продуктите за експорт
            $products = $this->getProductsForExport();

            if (empty($products)) {
                throw new \Exception('Няма продукти за експорт');
            }

            F()->log->developer('Found ' . count($products) . ' products for export', __FILE__, __LINE__);

            // Проверяваме дали е голям експорт (над 1000 продукта)
            if (count($products) > 1000) {
                F()->log->developer('Large export detected (' . count($products) . ' products) - using incremental approach', __FILE__, __LINE__);
                $this->processLargeExport($products, $exportFormat, $exportData);
                return;
            }

            // За малки експорти използваме директния подход
            $this->processDirectExport($products, $exportFormat, $exportData);

        } catch (\Exception $e) {
            F()->log->developer('Export error: ' . $e->getMessage(), __FILE__, __LINE__);
            $this->setJSONResponseOutput([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    // BACKUP MARKER - Тук започва логиката която ще бъде изнесена в моделите
    // Следващите методи ще бъдат рефакторирани и изнесени в съответните модели
