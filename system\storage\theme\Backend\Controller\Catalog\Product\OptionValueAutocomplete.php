<?php

namespace Theme25\Backend\Controller\Catalog\Product;

/**
 * Суб-контролер за автозавършване на option values в продуктовата форма
 */
class ControllerCatalogProductOptionValueAutocomplete extends \Theme25\ControllerSubMethods {

    /**
     * Зареждане на option values
     */
    public function index() {
        $json = [];

        // Зареждане на модела за опции
        $this->loadModelsAs([
            'catalog/option' => 'optionModel'
        ]);

        // Зареждаме всички опции
        $options = $this->optionModel->getOptions();
        
        foreach ($options as $option) {
            // Зареждаме стойностите за всяка опция
            $values = $this->optionModel->getOptionValues($option['option_id']);
            foreach ($values as $value) {
                $json[] = [
                    'option_value_id' => $value['option_value_id'],
                    'option_id' => $option['option_id'],
                    'option_name' => $option['name'],
                    'name' => $value['name'],
                    'sort_order' => $value['sort_order']
                ];
            }
        }

        // Сортираме по име на опцията и след това по име на стойността
        usort($json, function($a, $b) {
            $option_compare = strcmp($a['option_name'], $b['option_name']);
            if ($option_compare === 0) {
                return strcmp($a['name'], $b['name']);
            }
            return $option_compare;
        });

        // Задаване на правилните headers за JSON отговор
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Зареждане на модели с псевдоними
     */
    private function loadModelsAs($models) {
        foreach ($models as $model => $alias) {
            $this->load->model($model);
            $this->{$alias} = $this->{'model_' . str_replace('/', '_', $model)};
        }
    }
}
