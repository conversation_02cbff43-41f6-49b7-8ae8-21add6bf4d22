<?php

namespace Theme25\Backend\Controller\Catalog;

class Attribute extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/attribute');
    }

    /**
     * Главна страница с атрибути - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Catalog/Attribute/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Атрибути');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/attribute');
        }
    }

    /**
     * Добавяне на нов атрибут - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Catalog/Attribute/Add', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на атрибут');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/attribute_form');
        }
    }

    /**
     * Редактиране на атрибут - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Catalog/Attribute/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на атрибут');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/attribute_form');
        }
    }

    /**
     * Запазване на атрибут - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Catalog/Attribute/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Изтриване на атрибут - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Catalog/Attribute/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Автозавършване за атрибути - dispatcher метод
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Attribute/Autocomplete', $this);
        
        if ($subController && is_callable([$subController, 'autocomplete'])) {
            $json = $subController->autocomplete($this->requestGet());
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX търсене за autocomplete - dispatcher метод
     */
    public function ajaxSearch() {
        $subController = $this->setBackendSubController('Catalog/Attribute/Index', $this);
        if ($subController && is_callable([$subController, 'ajaxSearch'])) {
            return $subController->ajaxSearch();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Получаване на следващата стойност за sort_order - dispatcher метод
     */
    public function getNextSortOrder() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Attribute/Autocomplete', $this);

        if ($subController && is_callable([$subController, 'getNextSortOrder'])) {
            $json = $subController->getNextSortOrder();
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }
}
