<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Edit extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
        $this->loadStyles();
    }

    public function execute() {
        $this->setTitle('Редакция на продукт');
        // Инициализиране на данните
		$this->initAdminData();
        $this->prepareProductForm();
        $this->renderTemplateWithDataAndOutput('catalog/product_form');
    }
    
    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'product-form.js',
            'AdvancedRichTextEditor.js',
            'image-manager.js'
        ], 'footer');
    }
      
    protected function loadStyles() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;

        $RichEditorUrl = $base . 'backend_css/rich-text-editor.css';
        $RichEditorPath = DIR_THEME . 'Backend/View/Css/rich-text-editor.css';
        
        if (file_exists($RichEditorPath)) {
            $lastModified = filemtime($RichEditorPath);
            $RichEditorUrl .= '?v=' . $lastModified;
            $this->document->addStyle($RichEditorUrl);
        }

        $this->document->addStyle('https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css');
        
    }

    public function searchCategories() {
        $json = [];

        $this->loadModelsAs([
            'catalog/category' => 'categoryModel'
        ]);

        if (isset($this->request->get['filter_name'])) {
            $filter_data = [
                'filter_name' => $this->request->get['filter_name'],
                'sort'        => 'name',
                'order'       => 'ASC',
                'start'       => 0,
                'limit'       => 10
            ];

            $results = $this->categoryModel->getCategories($filter_data);

            foreach ($results as $result) {
                // Извличаме само последното име от пътя (след последния ' > ')
                $category_name = $result['name'];
                if (strpos($category_name, ' > ') !== false) {
                    $parts = explode(' > ', $category_name);
                    $category_name = end($parts);
                }

                $json[] = [
                    'id'   => $result['category_id'],
                    'name' => strip_tags(html_entity_decode($category_name, ENT_QUOTES, 'UTF-8'))
                ];
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function searchManufacturers() {
        $json = [];
        
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel'
        ]);
        
        if (isset($this->request->get['filter_name'])) {
            $filter_data = [
                'filter_name' => $this->request->get['filter_name'],
                'start'       => 0,
                'limit'       => 10
            ];
            
            $results = $this->manufacturerModel->getManufacturers($filter_data);
            
            foreach ($results as $result) {
                $json[] = [
                    'id'   => $result['manufacturer_id'],
                    'name' => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
                ];
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }



    public function prepareProductForm() {

        $subController = new Prepairform($this->_controller);
        $subController->execute();

    }

}