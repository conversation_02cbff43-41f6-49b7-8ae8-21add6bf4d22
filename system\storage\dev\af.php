<?php


function Log_($content, $file = false, $line = 'unknown', $log_file = 'logs/log.txt')
	{
		new Write_log($content, $file, $line, $log_file);
	}

function LogData_($content, $file = false, $line = 'unknown', $log_file = 'logs/data.txt')
	{
		new Write_log($content, $file, $line, $log_file);
	}

function LogDeveloper_($content, $file = false, $line = 'unknown', $log_file = 'logs/dev_log.txt')
	{
		$f = new avramov_funcs;
		if($f->is_developer()) new Write_log($content, $file, $line, $log_file);
	}

function LogDevData_($content, $file = false, $line = 'unknown', $log_file = 'logs/dev_data_log.txt')
	{
		$f = new avramov_funcs;
		if($f->is_developer()) new Write_log($content, $file, $line, $log_file);
	}

class Logger {
	public function __call($name, $arguments) {
		if (method_exists($this, $name)) {
			return call_user_func_array([$this, $name], $arguments);
		}
		return $this;
	}

	public function __invoke($content, $file = false, $line = 'unknown', $log_file = 'logs/log.txt') {
		Log_($content, $file, $line, $log_file);
	}

	public function data($content, $file = false, $line = 'unknown', $log_file = 'logs/data.txt') {
		LogData_($content, $file, $line, $log_file);
	}

	public function developer($content, $file = false, $line = 'unknown', $log_file = 'logs/dev_log.txt') {
		LogDeveloper_($content, $file, $line, $log_file);
	}

	public function dev_data($content, $file = false, $line = 'unknown', $log_file = 'logs/dev_data_log.txt') {
		LogDevData_($content, $file, $line, $log_file);
	}
}

function F() {
	static $instance = null;
	if ($instance === null) {
		$instance = new avramov_funcs();
	}
	return $instance;
}



function jd($array,$method = 'obj' ) {
		return ($method == 'obj') ? json_decode($array,false) : json_decode($array,true);
	}

function je($array) {
	return raw_json_encode($array);
}

function ex($val,$sep) {
	return explode($sep, $val);
}

function raw_json_encode($input) {
	return preg_replace_callback('/\\\\u([0-9a-zA-Z]{4})/',
					'matches',
					json_encode($input)
	);
}

function isDeveloper()
	{
		$f = new avramov_funcs;
		return $f->is_developer();
	}



function matches($matches) {
		return mb_convert_encoding(pack('H*',$matches[1]),'UTF-8','UTF-16');
};

class Write_log
{
	public function __construct($content, $file = false, $line = 'unknown', $log_file = 'logs.txt')
	{
	$f = new avramov_funcs;
	$JSON = (object)array();
	$JSON->Request = false;
	$JSON->Content = '';
	$Reffer = isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : '';


	if ((is_array($content)) or (is_object($content))) {
		$JSON->Content = $f->ArrayToPrint($content);
	}
	else {
		$JSON->Content = $content;
	}

	if ($file) {
		$JSON->Request = (object)array();
		$JSON->Request->file = $file;
		$JSON->Request->line = $line;
	}
	else {
		$JSON->Request->file = $Reffer;
		$JSON->Request->line = ' без!';
	}

	$JSON->_Date = ex($f->DateStamp(),"|");
	$string = "\n" . je($JSON);

	$log_file = DEV . $log_file;
	if(!is_file($log_file)) {
		file_put_contents($log_file,'');
	}

	$fp = fopen($log_file, 'a') or die("Failed to open file for writing. => " . $log_file );
	return (fwrite($fp, $string)) ? TRUE : FALSE;
	}
}


class avramov_funcs {

	public $ips_to_allow = array('**************');
	public $log;

	function __construct() {
		$this->log = new Logger();
	}

	function log($content, $file = false, $line = 'unknown', $log_file = 'logs/log.txt') {
		$this->log->__invoke($content, $file, $line, $log_file);
	}

	function ArrayToPrint ($array_to_string) {
		ob_start();
		$this->ToPrint($array_to_string);
		$output = ob_get_clean();
		return $output;
	}

	function ToPrint($array) {
		print_r($array);
	}

	function DateStamp() {
		date_default_timezone_set('Europe/Sofia');
		$Obj = date("d-m-Y H:i");
		$Obj=str_replace("-", ".", $Obj);
		return str_replace(" ", "|", $Obj);
	}

	function save_file($file, $action, $content)
	{
	$fp = fopen($file, $action) or die("Failed to open file: $file for writing.");
	  // return (fwrite($fp, $content)) ? true : die("Error -> $file");
	  fwrite($fp, $content);
	}

	function check_developer() {
		if( !$this->is_developer() )  die('Access Denided!');
	}

	function is_developer() {
		if (isset($_SERVER['REMOTE_ADDR'])) return in_array($_SERVER['REMOTE_ADDR'], $this->ips_to_allow);
		else return false;
	}

}


class AF_Controller {

	public
	$loader,
	$registry,
	$config,
	$cache,
	$db
	;

	function __construct() {
		global $loader, $registry, $cache, $db, $config;
		$this->loader = $loader;
		$this->registry = $registry;
		$this->cache = $cache ? $cache : $registry->get('cache');
		$this->config = $config ? $config : $registry->get('config');
		$this->db = $db ? $db : $registry->get('db');
		$this->config->set('config_error_display',true);
	}

	public function load_model($model = false, $ext_model = false) {
		if(!$model) return null;
		$this->_load_model($model);
		if($ext_model) $this->_load_model($ext_model);
	}

	public function _load_model($model) {
		$path = 'model_' . implode('_',explode('/',$model));
		$this->loader->model($model);
		$this->$path = $this->registry->get($path);
	}

	public function __get($key) {
		return $this->registry->get($key);
	}

}

function AF() {
	return new avramov_funcs;
}



?>