<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Import extends \Theme25\ControllerSubMethods {

    private $supportedFormats = ['csv', 'xml', 'xlsx'];
    private $batchSize = 150;
    private $existingProducts = [];
    private $languageMapping = [];

    public function __construct($registry) {
        parent::__construct($registry);
        $this->loadScripts();
        $this->initializeLanguageMapping();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'product-import.js',
        ], 'footer');
    }

    /**
     * Инициализира мапинга на езиците
     */
    private function initializeLanguageMapping() {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        foreach ($languages as $language) {
            $this->languageMapping[$language['code']] = $language['language_id'];
        }
    }

    /**
     * Основен метод за показване на формата за импорт
     */
    public function execute() {
        $this->setTitle('Импорт на продукти');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_import');
    }

    /**
     * Подготвя данните за формата
     */
    public function prepareData() {
        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product'),
            'supported_formats' => $this->supportedFormats,
            'max_file_size' => ini_get('upload_max_filesize'),
            'languages' => $this->languageMapping
        ]);
    }

    /**
     * Обработва качването и импорта на файл
     */
    public function processImport() {
        $json = [];

        try {
            // Валидация на заявката
            if (!$this->validateImportRequest()) {
                throw new \Exception('Невалидна заявка за импорт');
            }

            $uploadedFile = $this->handleFileUpload();
            if (!$uploadedFile) {
                throw new \Exception('Грешка при качване на файла');
            }

            // Определяне на формата на файла
            $fileFormat = $this->detectFileFormat($uploadedFile);

            // Зареждане на съответния модел за обработка
            $importModel = $this->loadImportModel($fileFormat);

            // Предварително зареждане на съществуващите продукти
            $this->loadExistingProducts();

            // Обработка на файла
            $result = $this->processFileImport($importModel, $uploadedFile);

            $json['success'] = 'Импортът завърши успешно';
            $json['statistics'] = $result;

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
            F()->log->error('Import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира заявката за импорт
     */
    private function validateImportRequest() {
        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        $fileExtension = strtolower(pathinfo($_FILES['import_file']['name'], PATHINFO_EXTENSION));
        return in_array($fileExtension, $this->supportedFormats);
    }

    /**
     * Обработва качването на файла
     */
    private function handleFileUpload() {
        $uploadDir = DIR_UPLOAD . 'import/';

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileName = 'import_' . date('Y-m-d_H-i-s') . '_' . $_FILES['import_file']['name'];
        $uploadPath = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['import_file']['tmp_name'], $uploadPath)) {
            return $uploadPath;
        }

        return false;
    }

    /**
     * Определя формата на файла
     */
    private function detectFileFormat($filePath) {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        if (!in_array($extension, $this->supportedFormats)) {
            throw new \Exception('Неподдържан формат на файл: ' . $extension);
        }

        return $extension;
    }

    /**
     * Зарежда съответния модел за импорт
     */
    private function loadImportModel($format) {
        $modelClass = 'Theme25\\Backend\\Model\\Catalog\\ProductImport' . ucfirst($format);
        $modelPath = 'catalog/product_import_' . $format;

        $this->loadModelAs($modelPath, 'importModel');

        return $this->importModel;
    }

    /**
     * Предварително зарежда съществуващите продукти в паметта
     */
    private function loadExistingProducts() {
        $sql = "SELECT product_id, model FROM `" . DB_PREFIX . "product`";
        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            $this->existingProducts[$row['model']] = $row['product_id'];
        }
    }

    /**
     * Обработва импорта на файла
     */
    private function processFileImport($importModel, $filePath) {
        // Парсиране на данните от файла
        $products = $importModel->parseFile($filePath);

        if (empty($products)) {
            throw new \Exception('Файлът не съдържа валидни данни за продукти');
        }

        // Разделяне на продуктите за обновяване и добавяне
        $productsToUpdate = [];
        $productsToAdd = [];

        foreach ($products as $product) {
            $model = $product['model'] ?? '';

            if (empty($model)) {
                continue; // Пропускаме продукти без модел
            }

            if (isset($this->existingProducts[$model])) {
                $product['product_id'] = $this->existingProducts[$model];
                $productsToUpdate[] = $product;
            } else {
                $productsToAdd[] = $product;
            }
        }

        // Обработка на порции
        $statistics = [
            'total_processed' => 0,
            'updated' => 0,
            'added' => 0,
            'errors' => 0
        ];

        // Обновяване на съществуващи продукти
        if (!empty($productsToUpdate)) {
            $statistics['updated'] = $this->processBatchUpdate($productsToUpdate);
        }

        // Добавяне на нови продукти
        if (!empty($productsToAdd)) {
            $statistics['added'] = $this->processBatchInsert($productsToAdd);
        }

        $statistics['total_processed'] = $statistics['updated'] + $statistics['added'];

        // Изтриване на временния файл
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        return $statistics;
    }

    /**
     * Обработва batch обновяване на съществуващи продукти
     */
    private function processBatchUpdate($products) {
        $updated = 0;
        $batches = array_chunk($products, $this->batchSize);

        foreach ($batches as $batch) {
            $updated += $this->updateProductsBatch($batch);

            // Пауза между порциите
            sleep(1);
        }

        return $updated;
    }

    /**
     * Обработва batch добавяне на нови продукти
     */
    private function processBatchInsert($products) {
        $added = 0;
        $batches = array_chunk($products, $this->batchSize);

        foreach ($batches as $batch) {
            $added += $this->insertProductsBatch($batch);

            // Пауза между порциите
            sleep(1);
        }

        return $added;
    }

    /**
     * Обновява порция от продукти с единична SQL заявка
     */
    private function updateProductsBatch($products) {
        if (empty($products)) {
            return 0;
        }

        try {
            // Подготовка на CASE конструкциите за основната таблица
            $caseClauses = $this->prepareCaseClausesForUpdate($products);
            $productIds = array_column($products, 'product_id');
            $productIdsStr = implode(',', array_map('intval', $productIds));

            // Обновяване на основната таблица product
            if (!empty($caseClauses)) {
                $sql = "UPDATE `" . DB_PREFIX . "product` SET " .
                       implode(', ', $caseClauses) .
                       " WHERE product_id IN ({$productIdsStr})";

                $this->db->query($sql);
            }

            // Обновяване на многоезичните данни
            $this->updateProductDescriptions($products);

            // Обновяване на категориите
            $this->updateProductCategories($products);

            return count($products);

        } catch (\Exception $e) {
            F()->log->error('Batch update error: ' . $e->getMessage(), __FILE__, __LINE__);
            return 0;
        }
    }

    /**
     * Добавя порция от нови продукти с единична SQL заявка
     */
    private function insertProductsBatch($products) {
        if (empty($products)) {
            return 0;
        }

        try {
            $this->loadModelAs('catalog/product', 'productModel');
            $added = 0;

            foreach ($products as $product) {
                // Подготовка на данните за добавяне
                $productData = $this->prepareProductDataForInsert($product);

                // Добавяне на продукта чрез стандартния модел
                $productId = $this->productModel->addProduct($productData);

                if ($productId) {
                    $added++;
                    // Добавяне в кеша на съществуващите продукти
                    $this->existingProducts[$product['model']] = $productId;
                }
            }

            return $added;

        } catch (\Exception $e) {
            F()->log->error('Batch insert error: ' . $e->getMessage(), __FILE__, __LINE__);
            return 0;
        }
    }

    /**
     * Подготвя CASE конструкциите за batch обновяване
     */
    private function prepareCaseClausesForUpdate($products) {
        $caseClauses = [];
        $fields = ['model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn', 'location',
                   'quantity', 'stock_status_id', 'image', 'manufacturer_id', 'shipping',
                   'price', 'points', 'tax_class_id', 'weight', 'weight_class_id',
                   'length', 'width', 'height', 'length_class_id', 'subtract',
                   'minimum', 'sort_order', 'status', 'date_modified'];

        foreach ($fields as $field) {
            $caseClause = $this->buildCaseClause($field, $products);
            if ($caseClause) {
                $caseClauses[] = "`{$field}` = {$caseClause}";
            }
        }

        return $caseClauses;
    }

    /**
     * Създава CASE конструкция за конкретно поле
     */
    private function buildCaseClause($field, $products) {
        $cases = [];
        $hasValues = false;

        foreach ($products as $product) {
            if (isset($product[$field])) {
                $value = $this->db->escape($product[$field]);
                $productId = (int)$product['product_id'];
                $cases[] = "WHEN {$productId} THEN '{$value}'";
                $hasValues = true;
            }
        }

        if (!$hasValues) {
            return null;
        }

        return "CASE product_id " . implode(' ', $cases) . " ELSE `{$field}` END";
    }

    /**
     * Обновява многоезичните описания на продуктите
     */
    private function updateProductDescriptions($products) {
        foreach ($products as $product) {
            if (!isset($product['product_description']) || !is_array($product['product_description'])) {
                continue;
            }

            $productId = (int)$product['product_id'];

            foreach ($product['product_description'] as $languageId => $description) {
                $sql = "INSERT INTO `" . DB_PREFIX . "product_description`
                        SET product_id = '{$productId}',
                            language_id = '{$languageId}',
                            name = '" . $this->db->escape($description['name'] ?? '') . "',
                            description = '" . $this->db->escape($description['description'] ?? '') . "',
                            tag = '" . $this->db->escape($description['tag'] ?? '') . "',
                            meta_title = '" . $this->db->escape($description['meta_title'] ?? '') . "',
                            meta_description = '" . $this->db->escape($description['meta_description'] ?? '') . "',
                            meta_keyword = '" . $this->db->escape($description['meta_keyword'] ?? '') . "'
                        ON DUPLICATE KEY UPDATE
                            name = VALUES(name),
                            description = VALUES(description),
                            tag = VALUES(tag),
                            meta_title = VALUES(meta_title),
                            meta_description = VALUES(meta_description),
                            meta_keyword = VALUES(meta_keyword)";

                $this->db->query($sql);
            }
        }
    }

    /**
     * Обновява категориите на продуктите
     */
    private function updateProductCategories($products) {
        foreach ($products as $product) {
            if (!isset($product['product_category']) || !is_array($product['product_category'])) {
                continue;
            }

            $productId = (int)$product['product_id'];

            // Изтриване на съществуващите категории
            $this->db->query("DELETE FROM `" . DB_PREFIX . "product_to_category` WHERE product_id = '{$productId}'");

            // Добавяне на новите категории
            foreach ($product['product_category'] as $categoryId) {
                $categoryId = (int)$categoryId;
                $this->db->query("INSERT INTO `" . DB_PREFIX . "product_to_category`
                                 SET product_id = '{$productId}', category_id = '{$categoryId}'");
            }
        }
    }

    /**
     * Подготвя данните за добавяне на нов продукт
     */
    private function prepareProductDataForInsert($product) {
        $data = [];

        // Основни полета
        $basicFields = ['model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn', 'location',
                       'quantity', 'stock_status_id', 'image', 'manufacturer_id', 'shipping',
                       'price', 'points', 'tax_class_id', 'weight', 'weight_class_id',
                       'length', 'width', 'height', 'length_class_id', 'subtract',
                       'minimum', 'sort_order', 'status'];

        foreach ($basicFields as $field) {
            if (isset($product[$field])) {
                $data[$field] = $product[$field];
            }
        }

        // Многоезични данни
        if (isset($product['product_description'])) {
            $data['product_description'] = $product['product_description'];
        }

        // Категории
        if (isset($product['product_category'])) {
            $data['product_category'] = $product['product_category'];
        }

        // Магазини (по подразбиране всички)
        $data['product_store'] = [0];

        return $data;
    }
}