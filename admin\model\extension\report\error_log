[17-Jul-2025 21:29:08 UTC] PHP Fatal error:  Uncaught Error: Call to a member function has() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php:180
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php(66): Theme25\Backend\Controller\Report\Report\Index->loadModel('report/report')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php(37): Theme25\Backend\Controller\Report\Report\Index->prepareReportsList()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php(29): Theme25\Backend\Controller\Report\Report\Index->prepareData()
#3 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report.php(17): Theme25\Backend\Controller\Report\Report\Index->execute()
#4 [internal function]: Theme25\Backend\Controller\Report\Report->index()
#5 /home/<USER>/storage_theme25/theme/RequestProcessor.php(195): call_user_func_array(Array, Array)
#6 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControl in /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php on line 180
[18-Jul-2025 17:55:30 Europe/Sofia] PHP Fatal error:  Uncaught Error: Call to a member function has() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/CustomerActivity.php:112
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/CustomerActivity.php(16): Theme25\Backend\Controller\Report\Report\CustomerActivity->loadModel('extension/repor...')
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php(145): Theme25\Backend\Controller\Report\Report\CustomerActivity->report()
#2 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php(39): Theme25\Backend\Controller\Report\Report\Index->prepareReportContent()
#3 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/Index.php(29): Theme25\Backend\Controller\Report\Report\Index->prepareData()
#4 /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report.php(17): Theme25\Backend\Controller\Report\Report\Index->execute()
#5 [internal function]: Theme25\Backend\Controller\Report\Report->index()
#6 /h in /home/<USER>/storage_theme25/theme/Backend/Controller/Report/Report/CustomerActivity.php on line 112
[18-Jul-2025 20:56:11 UTC] PHP Fatal error:  Uncaught Exception: Error: Unknown column 'op.quantity' in 'SELECT'<br />Error No: 1054<br />SELECT 
        t.customer_id, 
        t.customer, 
        t.email, 
        t.customer_group, 
        t.status, 
        COUNT(DISTINCT t.order_id) AS orders, 
        (SELECT SUM(op.quantity) FROM `oc_order_product` op WHERE op.order_id = t.order_id ) as products,
        SUM(t.total) AS total 
        FROM (SELECT c.customer_id, CONCAT(c.firstname, ' ', c.lastname) AS customer, c.email, cgd.name AS customer_group, c.status, o.order_id, SUM(op.quantity) as products, SUM(o.total) AS total FROM `oc_order` o LEFT JOIN `oc_customer` c ON (o.customer_id = c.customer_id) LEFT JOIN `oc_customer_group_description` cgd ON (c.customer_group_id = cgd.customer_group_id) WHERE o.customer_id > 0 AND cgd.language_id = '2' AND CONCAT(c.firstname, ' ', c.lastname) LIKE 'Константин Томов' AND o.order_status_id > '0' GROUP BY o.order_id) AS t 
        GROUP BY t.customer_id ORDER BY total DESC LIMIT 0,20 in in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
