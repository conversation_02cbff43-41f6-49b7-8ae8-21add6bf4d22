<?php

namespace Theme25\Backend\Controller\Catalog\Option;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'option-listing.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Опции');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/option');
    }

    /**
     * Подготвя данните за листването на опции
     */
    private function prepareData() {
        $this->loadModelsAs([
            'catalog/option' => 'optionModel'
        ]);

        // Подготвяне на филтрите
        $this->prepareFilters()
             ->prepareOptionsList()
             ->preparePagination()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя филтрите за търсене
     */
    private function prepareFilters() {
        $filter_name = $this->requestGet('filter_name', '');
        $filter_type = $this->requestGet('filter_type', '');
        $sort = $this->requestGet('sort', 'od.name');
        $order = $this->requestGet('order', 'ASC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'filter_name' => $filter_name,
            'filter_type' => $filter_type,
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        // Подготвяне на типовете опции за филтъра
        $option_types = [
            'select' => 'Select',
            'radio' => 'Radio',
            'checkbox' => 'Checkbox',
            'text' => 'Text',
            'textarea' => 'Textarea',
            'file' => 'File',
            'date' => 'Date',
            'time' => 'Time',
            'datetime' => 'Date & Time'
        ];
        $this->setData('option_types', $option_types);

        return $this;
    }

    /**
     * Подготвя списъка с опции
     */
    private function prepareOptionsList() {
        $filter_data = [
            'filter_name' => $this->data['filter_name'],
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        if (!empty($this->data['filter_type'])) {
            $filter_data['filter_type'] = $this->data['filter_type'];
        }

        $options = $this->optionModel->getOptions($filter_data);
        $option_total = $this->optionModel->getTotalOptions($filter_data);

        // Подготвяне на данните за всяка опция
        $option_list = [];
        foreach ($options as $option) {
            $edit_params = 'option_id=' . $option['option_id'];
            $delete_params = 'option_id=' . $option['option_id'];
            
            // Получаване на броя стойности за опцията (ако е приложимо)
            $value_count = 0;
            if (in_array($option['type'], ['select', 'radio', 'checkbox'])) {
                $option_values = $this->optionModel->getOptionValues($option['option_id']);
                $value_count = count($option_values);
            }
            
            $option_list[] = [
                'option_id' => $option['option_id'],
                'name' => $option['name'],
                'type' => $option['type'],
                'type_text' => $this->getOptionTypeText($option['type']),
                'sort_order' => $option['sort_order'],
                'value_count' => $value_count,
                'edit_url' => $this->getAdminLink('catalog/option/edit', $edit_params),
                'delete_url' => $this->getAdminLink('catalog/option/delete', $delete_params)
            ];
        }

        $this->setData([
            'options' => $option_list,
            'option_total' => $option_total
        ]);

        return $this;
    }

    /**
     * Получава текстовото представяне на типа опция
     */
    private function getOptionTypeText($type) {
        $types = [
            'select' => 'Select',
            'radio' => 'Radio',
            'checkbox' => 'Checkbox',
            'text' => 'Text',
            'textarea' => 'Textarea',
            'file' => 'File',
            'date' => 'Date',
            'time' => 'Time',
            'datetime' => 'Date & Time'
        ];
        
        return $types[$type] ?? $type;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['option_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('catalog/option', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('catalog/option', $filter_params . '&limit={limit}'));
        $pagination->setProductText('опции');

        // Генериране на HTML код за цялата пагинация
        $this->setData('pagination_html', $pagination->render());

        return $this;
    }

    /**
     * Генерира URL параметри за филтрите
     */
    private function buildFilterParams() {
        $params = [];

        // Филтри, които трябва да се запазят в URL
        $filter_fields = [
            'filter_name',
            'filter_type',
            'sort',
            'order'
        ];

        foreach ($filter_fields as $field) {
            if (isset($this->data[$field]) && $this->data[$field] !== '') {
                $params[] = $field . '=' . urlencode($this->data[$field]);
            }
        }

        return $params ? '&' . implode('&', $params) : '';
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        $url_params = [];

        if (!empty($this->data['filter_name'])) {
            $url_params['filter_name'] = $this->data['filter_name'];
        }

        if (!empty($this->data['filter_type'])) {
            $url_params['filter_type'] = $this->data['filter_type'];
        }

        // Конвертиране на масива в query string
        $url_query_string = !empty($url_params) ? http_build_query($url_params) : '';

        $this->setData([
            'add_url' => $this->getAdminLink('catalog/option/add'),
            'delete_selected_url' => $this->getAdminLink('catalog/option/delete'),
            'filter_url' => $this->getAdminLink('catalog/option', $url_query_string),
            'clear_filter_url' => $this->getAdminLink('catalog/option')
        ]);

        return $this;
    }

    /**
     * AJAX метод за autocomplete търсене
     */
    public function ajaxSearch() {
        $json = [];

        ob_start();
        $this->loadModelsAs([
            'catalog/option' => 'optionModel'
        ]);

        try {
            $filter_name = $this->requestGet('filter_name', '');
            $filter_type = $this->requestGet('filter_type', '');
            $limit = min(10, max(1, (int)$this->requestGet('limit', 10)));

            $filter_data = [
                'filter_name' => $filter_name,
                'start' => 0,
                'limit' => $limit,
                'sort' => 'od.name',
                'order' => 'ASC'
            ];

            if (!empty($filter_type)) {
                $filter_data['filter_type'] = $filter_type;
            }

            $options = $this->optionModel->getOptions($filter_data);

            foreach ($options as $option) {
                $json[] = [
                    'option_id' => $option['option_id'],
                    'name' => strip_tags(html_entity_decode($option['name'], ENT_QUOTES, 'UTF-8')),
                    'type' => $option['type'],
                    'type_text' => $this->getOptionTypeText($option['type']),
                    'sort_order' => $option['sort_order'] ?? 0
                ];
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при търсене: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json = ['error' => 'Unexpected output: ' . $output];
        }

        $this->setJSONResponseOutput($json);
    }

}
