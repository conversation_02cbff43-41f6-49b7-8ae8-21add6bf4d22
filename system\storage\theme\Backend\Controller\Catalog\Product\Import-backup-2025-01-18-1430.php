<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Import extends \Theme25\ControllerSubMethods {

    private $supportedFormats = [];
    private $unavailableFormats = [];
    private $formatRequirements = [];
    private $batchSize = 150;
    private $largeBatchSize = 100; // Намален batch размер за memory optimization
    private $largeImportThreshold = 1000; // Праг за големи импорти
    private $existingProducts = [];
    private $languageMapping = [];
    private $categoryCache = [];
    private $testMode = false;
    private $testModeStats = [
        'queries_saved' => 0,
        'inserts_saved' => 0,
        'updates_saved' => 0
    ];
    private $importCacheDir = '';
    private $fieldHelper;

    public function __construct($registry) {
        parent::__construct($registry);

        // Инициализираме import cache директорията
        $this->importCacheDir = DIR_CACHE . 'import/';
        $this->initializeImportCacheDir();

        // Зареждаме field helper
        $this->loadModelAs('catalog/importfieldshelper', 'fieldHelper');

        $this->loadScripts();
        $this->checkAvailableFormats();
        $this->initializeLanguageMapping();
        $this->initializeCategoryCache();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'product-import.js',
            'product-import-progress.js',
        ], 'footer');
    }

    /**
     * Проверява кои формати за импорт са налични
     */
    private function checkAvailableFormats() {
        $this->supportedFormats = [];
        $this->unavailableFormats = [];
        $this->formatRequirements = [];

        // CSV - винаги наличен (вграден в PHP)
        $this->supportedFormats[] = 'csv';
        $this->formatRequirements['csv'] = [
            'name' => 'CSV',
            'description' => 'Comma Separated Values (.csv)',
            'icon' => 'ri-file-text-line',
            'available' => true,
            'reason' => 'Вграден в PHP'
        ];

        // XML - проверка за simplexml разширение
        if (extension_loaded('simplexml')) {
            $this->supportedFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => true,
                'reason' => 'SimpleXML разширение активно'
            ];
        } else {
            $this->unavailableFormats[] = 'xml';
            $this->formatRequirements['xml'] = [
                'name' => 'XML',
                'description' => 'Extensible Markup Language (.xml)',
                'icon' => 'ri-code-s-slash-line',
                'available' => false,
                'reason' => 'Липсва SimpleXML разширение'
            ];
        }

        // XLSX - проверка за PhpSpreadsheet или алтернативни библиотеки
        $xlsxAvailable = false;
        $xlsxReason = '';

        // Проверка за PhpOffice\PhpSpreadsheet
        if (class_exists('PhpOffice\\PhpSpreadsheet\\IOFactory')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet библиотека';
        }
        // Проверка за алтернативна библиотека SimpleXLSX
        elseif (class_exists('SimpleXLSX')) {
            $xlsxAvailable = true;
            $xlsxReason = 'SimpleXLSX библиотека';
        }
        // Проверка дали файлът съществува в проекта
        elseif (file_exists(DIR_SYSTEM . 'storage/vendor/phpoffice/phpspreadsheet/src/PhpSpreadsheet/IOFactory.php')) {
            $xlsxAvailable = true;
            $xlsxReason = 'PhpOffice\\PhpSpreadsheet (локално)';
        }
        else {
            $xlsxReason = 'Липсва PhpOffice\\PhpSpreadsheet или SimpleXLSX библиотека';
        }

        if ($xlsxAvailable) {
            $this->supportedFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => true,
                'reason' => $xlsxReason
            ];
        } else {
            $this->unavailableFormats[] = 'xlsx';
            $this->formatRequirements['xlsx'] = [
                'name' => 'XLSX',
                'description' => 'Microsoft Excel (.xlsx)',
                'icon' => 'ri-file-excel-2-line',
                'available' => false,
                'reason' => $xlsxReason
            ];
        }

        // Логиране на резултатите
        F()->log->developer('Available import formats: ' . implode(', ', $this->supportedFormats), __FILE__, __LINE__);
        if (!empty($this->unavailableFormats)) {
            F()->log->developer('Unavailable import formats: ' . implode(', ', $this->unavailableFormats), __FILE__, __LINE__);
        }
    }

    /**
     * Инициализира мапинга на езиците
     */
    private function initializeLanguageMapping() {
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        foreach ($languages as $language) {
            $this->languageMapping[$language['code']] = $language['language_id'];
        }
    }

    /**
     * Инициализира кеша на категориите
     */
    private function initializeCategoryCache() {
        $this->loadModelAs('catalog/category', 'categoryModel');
        $this->loadCategoryPaths();
    }

    /**
     * Зарежда всички категории с техните пътища в кеша
     */
    private function loadCategoryPaths() {
        $language_id = $this->getLanguageId();

        $sql = "SELECT
                    cp.category_id,
                    GROUP_CONCAT(cd.name ORDER BY cp.level SEPARATOR ' > ') AS path
                FROM `" . DB_PREFIX . "category_path` cp
                LEFT JOIN `" . DB_PREFIX . "category_description` cd ON (cp.path_id = cd.category_id)
                WHERE cd.language_id = '{$language_id}'
                GROUP BY cp.category_id
                ORDER BY cp.category_id";

        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            $this->categoryCache[$row['path']] = $row['category_id'];
        }
    }

    /**
     * Инициализира директорията за import cache файлове
     */
    private function initializeImportCacheDir() {
        if (!is_dir($this->importCacheDir)) {
            if (!mkdir($this->importCacheDir, 0755, true)) {
                F()->log->developer('Failed to create import cache directory: ' . $this->importCacheDir, __FILE__, __LINE__);
                throw new \Exception('Не може да се създаде директория за import cache');
            }
        }

        // Почистваме стари import файлове (по-стари от 24 часа)
        $this->cleanupOldImportFiles();
    }

    /**
     * Почиства стари import cache файлове
     */
    private function cleanupOldImportFiles() {
        $files = glob($this->importCacheDir . 'import_*.json');
        $cutoffTime = time() - (24 * 60 * 60); // 24 часа

        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
                F()->log->developer('Cleaned up old import file: ' . basename($file), __FILE__, __LINE__);
            }
        }
    }

    /**
     * Записва import данни в JSON файл
     */
    private function saveImportData($importId, $data) {
        $filePath = $this->importCacheDir . 'import_' . $importId . '.json';

        // Добавяме timestamp за tracking
        $data['last_updated'] = time();
        $data['created_at'] = $data['created_at'] ?? time();

        $jsonData = json_encode($data, JSON_PRETTY_PRINT);

        // File locking за concurrent access
        $fp = fopen($filePath, 'w');
        if ($fp && flock($fp, LOCK_EX)) {
            fwrite($fp, $jsonData);
            flock($fp, LOCK_UN);
            fclose($fp);

            F()->log->developer('Import data saved to: ' . $filePath, __FILE__, __LINE__);
            return true;
        } else {
            F()->log->developer('Failed to save import data to: ' . $filePath, __FILE__, __LINE__);
            if ($fp) fclose($fp);
            return false;
        }
    }

    /**
     * Чете import данни от JSON файл
     */
    private function loadImportData($importId) {
        $filePath = $this->importCacheDir . 'import_' . $importId . '.json';

        if (!file_exists($filePath)) {
            F()->log->developer('Import file not found: ' . $filePath, __FILE__, __LINE__);
            return null;
        }

        // File locking за concurrent access
        $fp = fopen($filePath, 'r');
        if ($fp && flock($fp, LOCK_SH)) {
            $jsonData = fread($fp, filesize($filePath));
            flock($fp, LOCK_UN);
            fclose($fp);

            $data = json_decode($jsonData, true);
            if ($data === null) {
                F()->log->developer('Failed to decode JSON from: ' . $filePath, __FILE__, __LINE__);
                return null;
            }

            F()->log->developer('Import data loaded from: ' . $filePath, __FILE__, __LINE__);
            return $data;
        } else {
            F()->log->developer('Failed to read import data from: ' . $filePath, __FILE__, __LINE__);
            if ($fp) fclose($fp);
            return null;
        }
    }

    /**
     * Изтрива import данни от JSON файл
     */
    private function deleteImportData($importId) {
        $filePath = $this->importCacheDir . 'import_' . $importId . '.json';

        if (file_exists($filePath)) {
            if (unlink($filePath)) {
                F()->log->developer('Import data deleted: ' . $filePath, __FILE__, __LINE__);
                return true;
            } else {
                F()->log->developer('Failed to delete import data: ' . $filePath, __FILE__, __LINE__);
                return false;
            }
        }

        return true; // Файлът не съществува, считаме че е "изтрит"
    }

    /**
     * Инициира голям импорт с batch обработка
     */
    private function initiateLargeImport($products, $importFormat, $importData) {
        // Генерираме уникален ID за импорта
        $importId = uniqid('import_', true);
        $productCount = count($products);

        // Разделяме продуктите на batch-ове с по-малък размер
        $batches = array_chunk($products, $this->largeBatchSize);
        $totalBatches = count($batches);

        F()->log->developer("Инициира голям импорт: {$productCount} продукта в {$totalBatches} batch-а", __FILE__, __LINE__);

        // Запазваме информацията за импорта в JSON файл
        $importInfo = [
            'import_id' => $importId,
            'format' => $importFormat,
            'import_data' => $importData,
            'batches' => $batches,
            'total_batches' => $totalBatches,
            'total_products' => $productCount,
            'processed_batches' => 0,
            'processed_products' => 0,
            'added_products' => 0,
            'updated_products' => 0,
            'errors' => 0,
            'status' => 'initiated',
            'started_at' => time(),
            'test_mode' => $this->testMode
        ];

        if (!$this->saveImportData($importId, $importInfo)) {
            throw new \Exception('Не може да се запази информацията за импорта');
        }

        // Връщаме информация за започване на batch обработката
        $json = [
            'large_import' => true,
            'import_id' => $importId,
            'total_batches' => $totalBatches,
            'total_products' => $productCount,
            'batch_size' => $this->largeBatchSize,
            'message' => "Започва импорт на {$productCount} продукта в {$totalBatches} части"
        ];

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва един batch от голям импорт
     */
    public function processBatch() {
        $json = [];

        try {
            if (!isset($_POST['import_id']) || !isset($_POST['batch_index'])) {
                throw new \Exception('Липсват параметри за batch обработка');
            }

            $importId = $_POST['import_id'];
            $batchIndex = (int)$_POST['batch_index'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Проверяваме дали batch-ът е валиден
            if ($batchIndex >= $importInfo['total_batches']) {
                throw new \Exception('Невалиден batch индекс');
            }

            // Активираме тестов режим ако е зададен
            $this->testMode = $importInfo['test_mode'] ?? false;
            if ($this->testMode) {
                $this->testModeStats = [
                    'queries_saved' => 0,
                    'inserts_saved' => 0,
                    'updates_saved' => 0
                ];
            }

            // Получаваме продуктите за този batch
            $batchProducts = $importInfo['batches'][$batchIndex];

            F()->log->developer("Обработва batch {$batchIndex}: " . count($batchProducts) . " продукта", __FILE__, __LINE__);

            // Memory monitoring
            $memoryBefore = memory_get_usage(true);

            // Обработваме batch-а
            $batchResult = $this->processBatchProducts($batchProducts, $importInfo['format']);

            // Memory cleanup
            unset($batchProducts);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memoryAfter = memory_get_usage(true);

            // Обновяваме информацията за прогреса
            $importInfo['processed_batches'] = $batchIndex + 1;
            $importInfo['processed_products'] += $batchResult['processed'];
            $importInfo['added_products'] += $batchResult['added'];
            $importInfo['updated_products'] += $batchResult['updated'];
            $importInfo['errors'] += $batchResult['errors'];
            $importInfo['status'] = 'processing';

            // Записваме обновените данни
            if (!$this->saveImportData($importId, $importInfo)) {
                F()->log->developer('Failed to save import progress: ' . $importId, __FILE__, __LINE__);
                throw new \Exception('Не може да се запише прогресът на импорта');
            }

            // Пауза между batch-овете
            sleep(1);

            $json = [
                'success' => true,
                'batch_index' => $batchIndex,
                'processed_batches' => $batchIndex + 1,
                'total_batches' => $importInfo['total_batches'],
                'processed_products' => $importInfo['processed_products'],
                'total_products' => $importInfo['total_products'],
                'added_products' => $importInfo['added_products'],
                'updated_products' => $importInfo['updated_products'],
                'errors' => $importInfo['errors'],
                'memory_usage' => $memoryAfter,
                'memory_usage_mb' => round($memoryAfter / 1024 / 1024, 2),
                'batch_result' => $batchResult
            ];

            if ($this->testMode) {
                $json['test_stats'] = $this->testModeStats;
            }

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Batch processing error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Финализира голям импорт
     */
    public function finalizeLargeImport() {
        $json = [];

        try {
            if (!isset($_POST['import_id'])) {
                throw new \Exception('Липсва import_id параметър');
            }

            $importId = $_POST['import_id'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Обновяваме статуса
            $importInfo['status'] = 'completed';
            $importInfo['completed_at'] = time();
            $importInfo['duration'] = $importInfo['completed_at'] - $importInfo['started_at'];

            // Записваме финалните данни
            $this->saveImportData($importId, $importInfo);

            F()->log->developer("Импорт завършен: {$importInfo['processed_products']} продукта", __FILE__, __LINE__);

            $json = [
                'success' => true,
                'message' => $this->testMode ?
                    'Тестовият импорт завърши успешно (няма реални промени в базата данни)' :
                    'Импортът завърши успешно',
                'statistics' => [
                    'total_processed' => $importInfo['processed_products'],
                    'added' => $importInfo['added_products'],
                    'updated' => $importInfo['updated_products'],
                    'errors' => $importInfo['errors'],
                    'duration' => $importInfo['duration'],
                    'test_mode' => $this->testMode
                ]
            ];

            if ($this->testMode && isset($importInfo['test_stats'])) {
                $json['statistics']['test_stats'] = $importInfo['test_stats'];
            }

            // Почистваме JSON файла след кратко време (5 минути)
            // Това позволява на потребителя да види резултатите
            $this->scheduleCleanup($importId, 300);

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Finalize import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Планира почистване на import данни
     */
    private function scheduleCleanup($importId, $delaySeconds = 300) {
        // За простота, ще използваме file timestamp за cleanup
        // В production среда може да се използва cron job или queue система
        $cleanupFile = $this->importCacheDir . 'cleanup_' . $importId . '.txt';
        file_put_contents($cleanupFile, time() + $delaySeconds);
    }

    /**
     * Връща информация за прогреса на импорт
     */
    public function getImportProgress() {
        $json = [];

        try {
            if (!isset($_POST['import_id'])) {
                throw new \Exception('Липсва import_id параметър');
            }

            $importId = $_POST['import_id'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Изчисляваме процента на завършеност
            $progressPercent = 0;
            if ($importInfo['total_batches'] > 0) {
                $progressPercent = round(($importInfo['processed_batches'] / $importInfo['total_batches']) * 100, 2);
            }

            $json = [
                'success' => true,
                'import_id' => $importId,
                'status' => $importInfo['status'],
                'progress_percent' => $progressPercent,
                'processed_batches' => $importInfo['processed_batches'],
                'total_batches' => $importInfo['total_batches'],
                'processed_products' => $importInfo['processed_products'],
                'total_products' => $importInfo['total_products'],
                'added_products' => $importInfo['added_products'],
                'updated_products' => $importInfo['updated_products'],
                'errors' => $importInfo['errors'],
                'started_at' => $importInfo['started_at'],
                'last_updated' => $importInfo['last_updated'] ?? time(),
                'test_mode' => $importInfo['test_mode'] ?? false
            ];

            // Добавяме времева информация
            $currentTime = time();
            $elapsedTime = $currentTime - $importInfo['started_at'];
            $json['elapsed_time'] = $elapsedTime;
            $json['elapsed_time_formatted'] = $this->formatDuration($elapsedTime);

            // Изчисляваме приблизителното оставащо време
            if ($progressPercent > 0 && $progressPercent < 100) {
                $estimatedTotalTime = ($elapsedTime / $progressPercent) * 100;
                $remainingTime = $estimatedTotalTime - $elapsedTime;
                $json['estimated_remaining_time'] = max(0, $remainingTime);
                $json['estimated_remaining_time_formatted'] = $this->formatDuration(max(0, $remainingTime));
            }

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Get import progress error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Форматира продължителност в секунди към четим формат
     */
    private function formatDuration($seconds) {
        if ($seconds < 60) {
            return round($seconds) . ' сек';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . ' мин ' . round($remainingSeconds) . ' сек';
        } else {
            $hours = floor($seconds / 3600);
            $remainingMinutes = floor(($seconds % 3600) / 60);
            return $hours . ' ч ' . $remainingMinutes . ' мин';
        }
    }

    /**
     * Отменя текущ импорт
     */
    public function cancelImport() {
        $json = [];

        try {
            if (!isset($_POST['import_id'])) {
                throw new \Exception('Липсва import_id параметър');
            }

            $importId = $_POST['import_id'];

            // Зареждаме информацията за импорта
            $importInfo = $this->loadImportData($importId);
            if (!$importInfo) {
                throw new \Exception('Не може да се намери информация за импорта');
            }

            // Обновяваме статуса
            $importInfo['status'] = 'cancelled';
            $importInfo['cancelled_at'] = time();

            // Записваме обновените данни
            $this->saveImportData($importId, $importInfo);

            F()->log->developer("Импорт отменен: {$importId}", __FILE__, __LINE__);

            $json = [
                'success' => true,
                'message' => 'Импортът беше отменен успешно',
                'import_id' => $importId
            ];

        } catch (\Exception $e) {
            $json = ['error' => $e->getMessage()];
            F()->log->developer('Cancel import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Обработва batch от продукти
     */
    private function processBatchProducts($products, $format) {
        $result = [
            'processed' => 0,
            'added' => 0,
            'updated' => 0,
            'errors' => 0
        ];

        $productsToAdd = [];
        $productsToUpdate = [];

        // Предварително зареждане на съществуващите продукти ако не е направено
        if (empty($this->existingProducts)) {
            $this->loadExistingProducts();
        }

        // Разделяме продуктите на нови и за обновяване
        foreach ($products as $product) {
            $model = $product['model'] ?? '';

            if (empty($model)) {
                $result['errors']++;
                continue;
            }

            if (isset($this->existingProducts[$model])) {
                $product['product_id'] = $this->existingProducts[$model];
                $productsToUpdate[] = $product;
            } else {
                $productsToAdd[] = $product;
            }
        }

        // Обработка на порции
        if (!empty($productsToUpdate)) {
            $updated = $this->processBatchUpdate($productsToUpdate);
            $result['updated'] = $updated;
        }

        if (!empty($productsToAdd)) {
            $added = $this->processBatchInsert($productsToAdd);
            $result['added'] = $added;
        }

        $result['processed'] = $result['added'] + $result['updated'];

        return $result;
    }

    /**
     * Основен метод за показване на формата за импорт
     */
    public function execute() {
        $this->setTitle('Импорт на продукти');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('catalog/product_import');
    }

    /**
     * Подготвя данните за формата
     */
    public function prepareData() {
        // Конвертираме max_file_size в байтове за JavaScript
        $maxFileSizeBytes = $this->parseFileSize(ini_get('upload_max_filesize'));

        $this->setData([
            'back_url' => $this->getAdminLink('catalog/product'),
            'supported_formats' => $this->supportedFormats,
            'unavailable_formats' => $this->unavailableFormats,
            'format_requirements' => $this->formatRequirements,
            'available_formats' => array_filter($this->formatRequirements, function($format) {
                return $format['available'];
            }),
            'max_file_size' => ini_get('upload_max_filesize'),
            'max_file_size_bytes' => $maxFileSizeBytes,
            'languages' => $this->languageMapping,
            'is_developer' => isDeveloper(),
            'large_import_threshold' => $this->largeImportThreshold
        ]);
    }

    /**
     * Парсира file size стринг към байтове
     */
    private function parseFileSize($size) {
        $size = trim($size);
        $last = strtolower($size[strlen($size)-1]);
        $size = (int) $size;

        switch($last) {
            case 'g':
                $size *= 1024;
            case 'm':
                $size *= 1024;
            case 'k':
                $size *= 1024;
        }

        return $size;
    }

    /**
     * Обработва качването и импорта на файл
     */
    public function processImport() {
        $json = [];

        try {
            // Валидация на заявката
            if (!$this->validateImportRequest()) {
                throw new \Exception('Невалидна заявка за импорт');
            }

            // Проверка за тестов режим
            $this->testMode = isset($_POST['test_mode']) && $_POST['test_mode'] == '1' && isDeveloper();

            if ($this->testMode) {
                F()->log->developer('=== ТЕСТОВ РЕЖИМ АКТИВИРАН ===', __FILE__, __LINE__);
                $this->testModeStats = [
                    'queries_saved' => 0,
                    'inserts_saved' => 0,
                    'updates_saved' => 0
                ];
            }

            $uploadedFile = $this->handleFileUpload();
            if (!$uploadedFile) {
                throw new \Exception('Грешка при качване на файла');
            }

            // Определяне на формата на файла
            $fileFormat = $this->detectFileFormat($uploadedFile);

            // Зареждане на съответния модел за обработка
            $importModel = $this->loadImportModel($fileFormat);

            // Парсиране на файла за получаване на продуктите
            $products = $importModel->parseFile($uploadedFile);

            if (empty($products)) {
                throw new \Exception('Няма валидни продукти за импорт в файла');
            }

            $productCount = count($products);
            F()->log->developer("Парсирани {$productCount} продукта от файла", __FILE__, __LINE__);

            // Проверяваме дали импортът е голям и трябва да се раздели
            if ($productCount > $this->largeImportThreshold) {
                // За големи импорти връщаме информация за batch обработка
                $this->initiateLargeImport($products, $fileFormat, $_POST);
                return; // initiateLargeImport() вече изпраща JSON response
            } else {


                F()->log->developer("Обработване на {$productCount} продукта директно", __FILE__, __LINE__);

                // За малки импорти обработваме директно
                $this->loadExistingProducts();
                $result = $this->processBatchProducts($products, $fileFormat);



                if ($this->testMode) {
                    $json['success'] = 'Тестовият импорт завърши успешно (няма реални промени в базата данни)';
                    $result['test_mode'] = true;
                    $result['test_stats'] = $this->testModeStats;
                } else {
                    $json['success'] = 'Импортът завърши успешно';
                }

                $json['statistics'] = $result;
            }

            // Изтриване на временния файл
            if (file_exists($uploadedFile)) {
                unlink($uploadedFile);
            }

        } catch (\Exception $e) {
            $json['error'] = $e->getMessage();
            F()->log->error('Import error: ' . $e->getMessage(), __FILE__, __LINE__);
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира заявката за импорт
     */
    private function validateImportRequest() {
        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        $fileExtension = strtolower(pathinfo($_FILES['import_file']['name'], PATHINFO_EXTENSION));

        if (!in_array($fileExtension, $this->supportedFormats)) {
            // Проверяваме дали форматът е в недостъпните формати
            if (in_array($fileExtension, $this->unavailableFormats)) {
                $formatInfo = $this->formatRequirements[$fileExtension] ?? null;
                $reason = $formatInfo ? $formatInfo['reason'] : 'Неизвестна причина';
                throw new \Exception("Форматът {$fileExtension} не е наличен в момента. Причина: {$reason}");
            } else {
                $supportedList = implode(', ', array_map('strtoupper', $this->supportedFormats));
                throw new \Exception("Неподдържан файлов формат '{$fileExtension}'. Поддържани формати: {$supportedList}");
            }
        }

        return true;
    }

    /**
     * Обработва качването на файла
     */
    private function handleFileUpload() {
        $uploadDir = DIR_UPLOAD . 'import/';

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileName = 'import_' . date('Y-m-d_H-i-s') . '_' . $_FILES['import_file']['name'];
        $uploadPath = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['import_file']['tmp_name'], $uploadPath)) {
            return $uploadPath;
        }

        return false;
    }

    /**
     * Определя формата на файла
     */
    private function detectFileFormat($filePath) {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        if (!in_array($extension, $this->supportedFormats)) {
            throw new \Exception('Неподдържан формат на файл: ' . $extension);
        }

        return $extension;
    }

    /**
     * Зарежда съответния модел за импорт
     */
    private function loadImportModel($format) {
        // Правилният път за зареждане на модели в темата според ModelProcessor.php
        //
        // ModelProcessor.php използва routeToClass() който трансформира всяка част от пътя с ucfirst(strtolower($part))
        //
        // Файловете са преименувани, за да съответстват на конвенцията на ModelProcessor.php:
        // - Productimportcsv.php (клас: Productimportcsv)
        // - Productimportxlsx.php (клас: Productimportxlsx)
        // - Productimportxml.php (клас: Productimportxml)
        //
        // Примери на трансформация:
        // 'catalog/productimportcsv' → routeToClass() → 'Catalog/Productimportcsv' → файл: Productimportcsv.php ✓
        // 'catalog/productimportxlsx' → routeToClass() → 'Catalog/Productimportxlsx' → файл: Productimportxlsx.php ✓
        // 'catalog/productimportxml' → routeToClass() → 'Catalog/Productimportxml' → файл: Productimportxml.php ✓
        $modelPath = 'catalog/productimport' . strtolower($format);

        // Debug информация за разработчици
        if (isDeveloper()) {
            F()->log->developer("Loading import model for format: {$format}", __FILE__, __LINE__);
            F()->log->developer("Model path: {$modelPath}", __FILE__, __LINE__);

            // Показваме как ModelProcessor ще трансформира пътя
            $parts = explode('/', $modelPath);
            $transformedParts = [];
            foreach ($parts as $part) {
                $transformedParts[] = ucfirst(strtolower($part));
            }
            $expectedPath = implode('/', $transformedParts);
            $expectedFile = str_replace('/', '', $expectedPath) . '.php';
            F()->log->developer("Expected transformed path: {$expectedPath}", __FILE__, __LINE__);
            F()->log->developer("Expected file name: {$expectedFile}", __FILE__, __LINE__);
            F()->log->developer("Files are now renamed to match ModelProcessor convention", __FILE__, __LINE__);
        }

        $this->loadModelAs($modelPath, 'importModel');

        return $this->importModel;
    }

    /**
     * Предварително зарежда съществуващите продукти в паметта
     */
    private function loadExistingProducts() {
        $sql = "SELECT product_id, model FROM `" . DB_PREFIX . "product`";
        $query = $this->db->query($sql);

        foreach ($query->rows as $row) {
            $this->existingProducts[$row['model']] = $row['product_id'];
        }
    }

    /**
     * Обработва импорта на файла
     */
    private function processFileImport($importModel, $filePath) {
        // Парсиране на данните от файла
        $products = $importModel->parseFile($filePath);

        if (empty($products)) {
            throw new \Exception('Файлът не съдържа валидни данни за продукти');
        }

        // Разделяне на продуктите за обновяване и добавяне
        $productsToUpdate = [];
        $productsToAdd = [];

        foreach ($products as $product) {
            $model = $product['model'] ?? '';

            if (empty($model)) {
                continue; // Пропускаме продукти без модел
            }

            // Обработваме категорийните пътища
            if (isset($product['product_category']) && is_string($product['product_category'])) {
                $product['product_category'] = $this->parseCategoryPaths($product['product_category']);
            }

            // Обработваме опциите
            if (isset($product['product_options'])) {
                $product['product_options'] = $this->processProductOptions($product['product_options']);
            }

            // Обработваме атрибутите
            if (isset($product['product_attributes'])) {
                $product['product_attributes'] = $this->processProductAttributes($product['product_attributes']);
            }

            if (isset($this->existingProducts[$model])) {
                $product['product_id'] = $this->existingProducts[$model];
                $productsToUpdate[] = $product;
            } else {
                $productsToAdd[] = $product;
            }
        }

        // Обработка на порции
        $statistics = [
            'total_processed' => 0,
            'updated' => 0,
            'added' => 0,
            'errors' => 0
        ];

        // Обновяване на съществуващи продукти
        if (!empty($productsToUpdate)) {
            $statistics['updated'] = $this->processBatchUpdate($productsToUpdate);
        }

        // Добавяне на нови продукти
        if (!empty($productsToAdd)) {
            $statistics['added'] = $this->processBatchInsert($productsToAdd);
        }

        $statistics['total_processed'] = $statistics['updated'] + $statistics['added'];

        // Изтриване на временния файл
        if (file_exists($filePath)) {
            unlink($filePath);
        }

        return $statistics;
    }

    /**
     * Обработва batch обновяване на съществуващи продукти
     */
    private function processBatchUpdate($products) {
        $updated = 0;
        $optimalBatchSize = min($this->largeBatchSize, 100); // Максимум 100 продукта на batch
        $batches = array_chunk($products, $optimalBatchSize);

        F()->log->developer("Обработка на " . count($batches) . " batch-а за обновяване", __FILE__, __LINE__);

        foreach ($batches as $batchIndex => $batch) {
            // Memory monitoring
            $memoryBefore = memory_get_usage(true);

            $batchUpdated = $this->updateProductsBatch($batch);
            $updated += $batchUpdated;

            // Memory cleanup
            unset($batch);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memoryAfter = memory_get_usage(true);
            $memoryUsed = round(($memoryAfter - $memoryBefore) / 1024 / 1024, 2);

            F()->log->developer("Batch {$batchIndex}: обновени {$batchUpdated} продукта, memory: {$memoryUsed}MB", __FILE__, __LINE__);

            // Пауза между порциите
            sleep(1);
        }

        return $updated;
    }

    /**
     * Обработва batch добавяне на нови продукти
     */
    private function processBatchInsert($products) {
        $added = 0;
        $optimalBatchSize = min($this->largeBatchSize, 100); // Максимум 100 продукта на batch
        $batches = array_chunk($products, $optimalBatchSize);

        F()->log->developer("Обработка на " . count($batches) . " batch-а за добавяне", __FILE__, __LINE__);

        foreach ($batches as $batchIndex => $batch) {
            // Memory monitoring
            $memoryBefore = memory_get_usage(true);

            $batchAdded = $this->insertProductsBatch($batch);
            $added += $batchAdded;

            // Memory cleanup
            unset($batch);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memoryAfter = memory_get_usage(true);
            $memoryUsed = round(($memoryAfter - $memoryBefore) / 1024 / 1024, 2);

            F()->log->developer("Batch {$batchIndex}: добавени {$batchAdded} продукта, memory: {$memoryUsed}MB", __FILE__, __LINE__);

            // Пауза между порциите
            sleep(1);
        }

        return $added;
    }

    /**
     * Обновява порция от продукти с единична SQL заявка
     */
    private function updateProductsBatch($products) {
        if (empty($products)) {
            return 0;
        }

        try {
            // Memory monitoring
            $memoryStart = memory_get_usage(true);

            // Подготовка на CASE конструкциите за основната таблица
            $caseClauses = $this->prepareCaseClausesForUpdate($products);
            $productIds = array_column($products, 'product_id');
            $productIdsStr = implode(',', array_map('intval', $productIds));

            // Обновяване на основната таблица product
            if (!empty($caseClauses)) {
                $sql = "UPDATE `" . DB_PREFIX . "product` SET " .
                       implode(', ', $caseClauses) .
                       " WHERE product_id IN ({$productIdsStr})";

                $this->executeQuery($sql, 'UPDATE');
            }

            // Memory cleanup след основната заявка
            unset($caseClauses, $productIds, $productIdsStr);

            // Обновяване на многоезичните данни
            $this->updateProductDescriptions($products);

            // Обновяване на категориите
            $this->updateProductCategories($products);

            // Финално memory cleanup
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memoryEnd = memory_get_usage(true);
            $memoryUsed = round(($memoryEnd - $memoryStart) / 1024 / 1024, 2);

            if (function_exists('isDeveloper') && isDeveloper()) {
                F()->log->developer("Update batch memory usage: {$memoryUsed}MB", __FILE__, __LINE__);
            }

            return count($products);

        } catch (\Exception $e) {
            F()->log->error('Batch update error: ' . $e->getMessage(), __FILE__, __LINE__);
            return 0;
        }
    }

    /**
     * Добавя порция от нови продукти с оптимизирани batch SQL заявки
     */
    private function insertProductsBatch($products) {
        if (empty($products)) {
            return 0;
        }

        try {
            // Memory monitoring
            $memoryStart = memory_get_usage(true);
            $added = 0;

            // Подготовка на данните за batch операции
            $productInserts = [];
            $descriptionInserts = [];
            $categoryInserts = [];
            $optionInserts = [];
            $optionValueInserts = [];
            $attributeInserts = [];

            // Генериране на нови product_id-та
            $startProductId = $this->getNextProductId();

            foreach ($products as $index => $product) {
                $productId = $startProductId + $index;

                // Подготовка на основните данни за продукта
                $productInserts[] = $this->prepareProductInsertData($product, $productId);

                // Подготовка на многоезичните данни
                if (isset($product['product_description'])) {
                    foreach ($product['product_description'] as $languageId => $description) {
                        $descriptionInserts[] = $this->prepareDescriptionInsertData($productId, $languageId, $description);
                    }
                }

                // Подготовка на категориите
                if (isset($product['product_category']) && is_array($product['product_category'])) {
                    foreach ($product['product_category'] as $categoryId) {
                        if ((int)$categoryId > 0) {
                            $categoryInserts[] = "({$productId}, {$categoryId})";
                        }
                    }
                }

                // Подготовка на опциите
                if (isset($product['product_options']) && is_array($product['product_options'])) {
                    $this->prepareOptionInserts($productId, $product['product_options'], $optionInserts, $optionValueInserts);
                }

                // Подготовка на атрибутите
                if (isset($product['product_attributes']) && is_array($product['product_attributes'])) {
                    $this->prepareAttributeInserts($productId, $product['product_attributes'], $attributeInserts);
                }

                // Добавяне в кеша на съществуващите продукти
                $this->existingProducts[$product['model']] = $productId;
                $added++;
            }

            // Изпълнение на batch INSERT заявките
            $this->executeBatchInserts($productInserts, $descriptionInserts, $categoryInserts, $optionInserts, $optionValueInserts, $attributeInserts);

            // Memory cleanup
            unset($productInserts, $descriptionInserts, $categoryInserts, $optionInserts, $optionValueInserts, $attributeInserts);

            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $memoryEnd = memory_get_usage(true);
            $memoryUsed = round(($memoryEnd - $memoryStart) / 1024 / 1024, 2);

            if (function_exists('isDeveloper') && isDeveloper()) {
                F()->log->developer("Insert batch memory usage: {$memoryUsed}MB", __FILE__, __LINE__);
            }

            return $added;

        } catch (\Exception $e) {
            F()->log->error('Batch insert error: ' . $e->getMessage(), __FILE__, __LINE__);
            return 0;
        }
    }

    /**
     * Подготвя CASE конструкциите за batch обновяване
     */
    private function prepareCaseClausesForUpdate($products) {
        $caseClauses = [];
        $fields = ['model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn', 'location',
                   'quantity', 'stock_status_id', 'image', 'manufacturer_id', 'shipping',
                   'price', 'points', 'tax_class_id', 'weight', 'weight_class_id',
                   'length', 'width', 'height', 'length_class_id', 'subtract',
                   'minimum', 'sort_order', 'status', 'date_modified'];

        foreach ($fields as $field) {
            $caseClause = $this->buildCaseClause($field, $products);
            if ($caseClause) {
                $caseClauses[] = "`{$field}` = {$caseClause}";
            }
        }

        return $caseClauses;
    }

    /**
     * Създава CASE конструкция за конкретно поле
     */
    private function buildCaseClause($field, $products) {
        $cases = [];
        $hasValues = false;

        foreach ($products as $product) {
            if (isset($product[$field])) {
                $value = $this->db->escape($product[$field]);
                $productId = (int)$product['product_id'];
                $cases[] = "WHEN {$productId} THEN '{$value}'";
                $hasValues = true;
            }
        }

        if (!$hasValues) {
            return null;
        }

        return "CASE product_id " . implode(' ', $cases) . " ELSE `{$field}` END";
    }

    /**
     * Обновява многоезичните описания на продуктите
     */
    private function updateProductDescriptions($products) {
        foreach ($products as $product) {
            if (!isset($product['product_description']) || !is_array($product['product_description'])) {
                continue;
            }

            $productId = (int)$product['product_id'];

            foreach ($product['product_description'] as $languageId => $description) {
                $sql = "INSERT INTO `" . DB_PREFIX . "product_description`
                        SET product_id = '{$productId}',
                            language_id = '{$languageId}',
                            name = '" . $this->db->escape($description['name'] ?? '') . "',
                            description = '" . $this->db->escape($description['description'] ?? '') . "',
                            tag = '" . $this->db->escape($description['tag'] ?? '') . "',
                            meta_title = '" . $this->db->escape($description['meta_title'] ?? '') . "',
                            meta_description = '" . $this->db->escape($description['meta_description'] ?? '') . "',
                            meta_keyword = '" . $this->db->escape($description['meta_keyword'] ?? '') . "'
                        ON DUPLICATE KEY UPDATE
                            name = VALUES(name),
                            description = VALUES(description),
                            tag = VALUES(tag),
                            meta_title = VALUES(meta_title),
                            meta_description = VALUES(meta_description),
                            meta_keyword = VALUES(meta_keyword)";

                $this->executeQuery($sql, 'INSERT');
            }
        }
    }

    /**
     * Обновява категориите на продуктите
     */
    private function updateProductCategories($products) {
        foreach ($products as $product) {
            if (!isset($product['product_category']) || empty($product['product_category'])) {
                continue;
            }

            $productId = (int)$product['product_id'];

            // Изтриване на съществуващите категории
            $this->executeQuery("DELETE FROM `" . DB_PREFIX . "product_to_category` WHERE product_id = '{$productId}'", 'DELETE');

            // Добавяне на новите категории
            $categoryIds = is_array($product['product_category']) ? $product['product_category'] : [$product['product_category']];

            foreach ($categoryIds as $categoryId) {
                $categoryId = (int)$categoryId;
                if ($categoryId > 0) {
                    $this->executeQuery("INSERT INTO `" . DB_PREFIX . "product_to_category`
                                     SET product_id = '{$productId}', category_id = '{$categoryId}'", 'INSERT');
                }
            }
        }

        // Обновяваме опциите на продуктите
        $this->updateProductOptions($products);

        // Обновяваме атрибутите на продуктите
        $this->updateProductAttributes($products);
    }

    /**
     * Обновява опциите на продуктите
     */
    private function updateProductOptions($products) {
        foreach ($products as $product) {
            if (!isset($product['product_options']) || empty($product['product_options'])) {
                continue;
            }

            $productId = (int)$product['product_id'];
            $options = $product['product_options'];

            // Изтриване на съществуващите опции
            $this->executeQuery("DELETE FROM `" . DB_PREFIX . "product_option` WHERE product_id = '{$productId}'", 'DELETE');
            $this->executeQuery("DELETE FROM `" . DB_PREFIX . "product_option_value` WHERE product_id = '{$productId}'", 'DELETE');

            // Добавяне на новите опции
            foreach ($options as $option) {
                $this->addProductOption($productId, $option);
            }
        }
    }

    /**
     * Добавя опция към продукт
     */
    private function addProductOption($productId, $option) {
        // Намираме или създаваме опцията
        $optionId = $this->findOrCreateOption($option['option_name'], $option['option_type']);

        if (!$optionId) {
            return;
        }

        // Добавяме опцията към продукта
        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "product_option`
                         SET product_id = '{$productId}',
                             option_id = '{$optionId}',
                             value = '',
                             required = '" . (int)($option['required'] ?? 0) . "'", 'INSERT');

        $productOptionId = $this->db->getLastId();

        // Добавяме стойностите на опцията
        if (isset($option['values']) && is_array($option['values'])) {
            foreach ($option['values'] as $value) {
                $optionValueId = $this->findOrCreateOptionValue($optionId, $value);
                if ($optionValueId) {
                    $this->executeQuery("INSERT INTO `" . DB_PREFIX . "product_option_value`
                                     SET product_option_id = '{$productOptionId}',
                                         product_id = '{$productId}',
                                         option_id = '{$optionId}',
                                         option_value_id = '{$optionValueId}',
                                         quantity = '0',
                                         subtract = '0',
                                         price = '0.0000',
                                         price_prefix = '+',
                                         points = '0',
                                         points_prefix = '+',
                                         weight = '0.00000000',
                                         weight_prefix = '+'", 'INSERT');
                }
            }
        }
    }

    /**
     * Намира или създава опция
     */
    private function findOrCreateOption($optionName, $optionType = 'select') {
        $language_id = $this->getLanguageId();

        // Търсим съществуваща опция
        $sql = "SELECT o.option_id
                FROM `" . DB_PREFIX . "option` o
                LEFT JOIN `" . DB_PREFIX . "option_description` od ON (o.option_id = od.option_id)
                WHERE od.name = '" . $this->db->escape($optionName) . "'
                AND od.language_id = '{$language_id}'";

        $query = $this->db->query($sql);

        if ($query->num_rows) {
            return $query->row['option_id'];
        }

        // Създаваме нова опция
        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "option`
                         SET type = '" . $this->db->escape($optionType) . "', sort_order = '0'", 'INSERT');

        $optionId = $this->db->getLastId();

        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "option_description`
                         SET option_id = '{$optionId}',
                             language_id = '{$language_id}',
                             name = '" . $this->db->escape($optionName) . "'", 'INSERT');

        return $optionId;
    }

    /**
     * Намира или създава стойност на опция
     */
    private function findOrCreateOptionValue($optionId, $valueName) {
        $language_id = $this->getLanguageId();

        // Търсим съществуваща стойност
        $sql = "SELECT ov.option_value_id
                FROM `" . DB_PREFIX . "option_value` ov
                LEFT JOIN `" . DB_PREFIX . "option_value_description` ovd ON (ov.option_value_id = ovd.option_value_id)
                WHERE ov.option_id = '{$optionId}'
                AND ovd.name = '" . $this->db->escape($valueName) . "'
                AND ovd.language_id = '{$language_id}'";

        $query = $this->db->query($sql);

        if ($query->num_rows) {
            return $query->row['option_value_id'];
        }

        // Създаваме нова стойност
        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "option_value`
                         SET option_id = '{$optionId}', sort_order = '0'", 'INSERT');

        $optionValueId = $this->db->getLastId();

        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "option_value_description`
                         SET option_value_id = '{$optionValueId}',
                             language_id = '{$language_id}',
                             option_id = '{$optionId}',
                             name = '" . $this->db->escape($valueName) . "'", 'INSERT');

        return $optionValueId;
    }

    /**
     * Обновява атрибутите на продуктите
     */
    private function updateProductAttributes($products) {
        foreach ($products as $product) {
            if (!isset($product['product_attributes']) || empty($product['product_attributes'])) {
                continue;
            }

            $productId = (int)$product['product_id'];
            $attributes = $product['product_attributes'];

            // Изтриване на съществуващите атрибути
            $this->executeQuery("DELETE FROM `" . DB_PREFIX . "product_attribute` WHERE product_id = '{$productId}'", 'DELETE');

            // Добавяне на новите атрибути
            foreach ($attributes as $attribute) {
                $this->addProductAttribute($productId, $attribute);
            }
        }
    }

    /**
     * Добавя атрибут към продукт
     */
    private function addProductAttribute($productId, $attribute) {
        $attributeId = $this->findOrCreateAttribute($attribute['attribute_name'], $attribute['attribute_group']);

        if (!$attributeId) {
            return;
        }

        $language_id = $this->getLanguageId();

        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "product_attribute`
                         SET product_id = '{$productId}',
                             attribute_id = '{$attributeId}',
                             language_id = '{$language_id}',
                             text = '" . $this->db->escape($attribute['attribute_value']) . "'", 'INSERT');
    }

    /**
     * Намира или създава атрибут
     */
    private function findOrCreateAttribute($attributeName, $attributeGroup = 'General') {
        $language_id = $this->getLanguageId();

        // Търсим съществуващ атрибут
        $sql = "SELECT a.attribute_id
                FROM `" . DB_PREFIX . "attribute` a
                LEFT JOIN `" . DB_PREFIX . "attribute_description` ad ON (a.attribute_id = ad.attribute_id)
                WHERE ad.name = '" . $this->db->escape($attributeName) . "'
                AND ad.language_id = '{$language_id}'";

        $query = $this->db->query($sql);

        if ($query->num_rows) {
            return $query->row['attribute_id'];
        }

        // Намираме или създаваме групата на атрибута
        $attributeGroupId = $this->findOrCreateAttributeGroup($attributeGroup);

        // Създаваме нов атрибут
        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "attribute`
                         SET attribute_group_id = '{$attributeGroupId}', sort_order = '0'", 'INSERT');

        $attributeId = $this->db->getLastId();

        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "attribute_description`
                         SET attribute_id = '{$attributeId}',
                             language_id = '{$language_id}',
                             name = '" . $this->db->escape($attributeName) . "'", 'INSERT');

        return $attributeId;
    }

    /**
     * Намира или създава група на атрибути
     */
    private function findOrCreateAttributeGroup($groupName) {
        $language_id = $this->getLanguageId();

        // Търсим съществуваща група
        $sql = "SELECT ag.attribute_group_id
                FROM `" . DB_PREFIX . "attribute_group` ag
                LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd ON (ag.attribute_group_id = agd.attribute_group_id)
                WHERE agd.name = '" . $this->db->escape($groupName) . "'
                AND agd.language_id = '{$language_id}'";

        $query = $this->db->query($sql);

        if ($query->num_rows) {
            return $query->row['attribute_group_id'];
        }

        // Създаваме нова група
        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "attribute_group`
                         SET sort_order = '0'", 'INSERT');

        $attributeGroupId = $this->db->getLastId();

        $this->executeQuery("INSERT INTO `" . DB_PREFIX . "attribute_group_description`
                         SET attribute_group_id = '{$attributeGroupId}',
                             language_id = '{$language_id}',
                             name = '" . $this->db->escape($groupName) . "'", 'INSERT');

        return $attributeGroupId;
    }

    /**
     * Подготвя данните за добавяне на нов продукт
     */
    private function prepareProductDataForInsert($product) {
        $data = [];

        // Основни полета
        $basicFields = ['model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn', 'location',
                       'quantity', 'stock_status_id', 'image', 'manufacturer_id', 'shipping',
                       'price', 'points', 'tax_class_id', 'weight', 'weight_class_id',
                       'length', 'width', 'height', 'length_class_id', 'subtract',
                       'minimum', 'sort_order', 'status'];

        foreach ($basicFields as $field) {
            if (isset($product[$field])) {
                $data[$field] = $product[$field];
            }
        }

        // Многоезични данни
        if (isset($product['product_description'])) {
            $data['product_description'] = $product['product_description'];
        }

        // Категории
        if (isset($product['product_category'])) {
            $data['product_category'] = $product['product_category'];
        }

        // Магазини (по подразбиране всички)
        $data['product_store'] = [0];

        return $data;
    }

    /**
     * Намира категория по път или я създава ако не съществува
     */
    private function findOrCreateCategoryByPath($categoryPath) {
        if (empty($categoryPath)) {
            return null;
        }

        // Проверяваме дали категорията вече съществува в кеша
        if (isset($this->categoryCache[$categoryPath])) {
            return $this->categoryCache[$categoryPath];
        }

        // Разделяме пътя на части
        $pathParts = array_map('trim', explode('>', $categoryPath));
        $parentId = 0;
        $currentPath = '';

        foreach ($pathParts as $index => $categoryName) {
            if ($index > 0) {
                $currentPath .= ' > ';
            }
            $currentPath .= $categoryName;

            // Проверяваме дали тази част от пътя съществува
            if (isset($this->categoryCache[$currentPath])) {
                $parentId = $this->categoryCache[$currentPath];
                continue;
            }

            // Създаваме новата категория
            $categoryId = $this->createCategory($categoryName, $parentId);

            if ($categoryId) {
                $this->categoryCache[$currentPath] = $categoryId;
                $parentId = $categoryId;
            } else {
                return null; // Грешка при създаване
            }
        }

        return $parentId;
    }

    /**
     * Създава нова категория
     */
    private function createCategory($name, $parentId = 0) {
        $language_id = $this->getLanguageId();

        $categoryData = [
            'parent_id' => $parentId,
            'top' => 0,
            'column' => 1,
            'sort_order' => 0,
            'status' => 1,
            'image' => '',
            'category_description' => [
                $language_id => [
                    'name' => $name,
                    'description' => '',
                    'meta_title' => $name,
                    'meta_description' => '',
                    'meta_keyword' => ''
                ]
            ],
            'category_store' => [0],
            'category_seo_url' => []
        ];

        try {
            return $this->categoryModel->addCategory($categoryData);
        } catch (\Exception $e) {
            F()->log->error('Error creating category: ' . $e->getMessage(), __FILE__, __LINE__);
            return false;
        }
    }

    /**
     * Парсира категорийни пътища от стринг
     */
    private function parseCategoryPaths($categoryPathString) {
        if (empty($categoryPathString)) {
            return [];
        }

        $categoryIds = [];
        $paths = array_map('trim', explode(',', $categoryPathString));

        foreach ($paths as $path) {
            $categoryId = $this->findOrCreateCategoryByPath($path);
            if ($categoryId) {
                $categoryIds[] = $categoryId;
            }
        }

        return array_unique($categoryIds);
    }

    /**
     * Валидира и обработва опциите на продукта
     */
    private function processProductOptions($optionsData) {
        if (empty($optionsData)) {
            return [];
        }

        $options = [];

        // Ако е JSON стринг, декодираме го
        if (is_string($optionsData)) {
            $decoded = json_decode($optionsData, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $optionsData = $decoded;
            } else {
                // Опитваме се да парсираме като прост формат
                return $this->parseSimpleOptions($optionsData);
            }
        }

        // Обработваме JSON структурата
        if (is_array($optionsData)) {
            foreach ($optionsData as $option) {
                if (isset($option['name']) && isset($option['values'])) {
                    $options[] = [
                        'option_name' => $option['name'],
                        'option_type' => $option['type'] ?? 'select',
                        'required' => $option['required'] ?? 0,
                        'values' => $option['values']
                    ];
                }
            }
        }

        return $options;
    }

    /**
     * Парсира опции в прост формат
     */
    private function parseSimpleOptions($optionsString) {
        $options = [];
        $optionPairs = explode(';', $optionsString);

        foreach ($optionPairs as $pair) {
            if (strpos($pair, ':') !== false) {
                list($name, $values) = explode(':', $pair, 2);
                $options[] = [
                    'option_name' => trim($name),
                    'option_type' => 'select',
                    'required' => 0,
                    'values' => array_map('trim', explode(',', $values))
                ];
            }
        }

        return $options;
    }

    /**
     * Валидира и обработва атрибутите на продукта
     */
    private function processProductAttributes($attributesData) {
        if (empty($attributesData)) {
            return [];
        }

        $attributes = [];

        // Ако е JSON стринг, декодираме го
        if (is_string($attributesData)) {
            $decoded = json_decode($attributesData, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $attributesData = $decoded;
            } else {
                // Опитваме се да парсираме като прост формат
                return $this->parseSimpleAttributes($attributesData);
            }
        }

        // Обработваме JSON структурата
        if (is_array($attributesData)) {
            foreach ($attributesData as $attribute) {
                if (isset($attribute['name']) && isset($attribute['value'])) {
                    $attributes[] = [
                        'attribute_name' => $attribute['name'],
                        'attribute_value' => $attribute['value'],
                        'attribute_group' => $attribute['group'] ?? 'General'
                    ];
                }
            }
        }

        return $attributes;
    }

    /**
     * Парсира атрибути в прост формат
     */
    private function parseSimpleAttributes($attributesString) {
        $attributes = [];
        $attributePairs = explode(';', $attributesString);

        foreach ($attributePairs as $pair) {
            if (strpos($pair, ':') !== false) {
                list($name, $value) = explode(':', $pair, 2);
                $attributes[] = [
                    'attribute_name' => trim($name),
                    'attribute_value' => trim($value),
                    'attribute_group' => 'General'
                ];
            }
        }

        return $attributes;
    }

    /**
     * Получава следващия свободен product_id
     */
    private function getNextProductId() {
        $query = $this->db->query("SELECT MAX(product_id) as max_id FROM `" . DB_PREFIX . "product`");
        return (int)$query->row['max_id'] + 1;
    }

    /**
     * Подготвя данните за INSERT в product таблицата
     */
    private function prepareProductInsertData($product, $productId) {
        $fields = [
            'product_id' => $productId,
            'model' => $product['model'] ?? '',
            'sku' => $product['sku'] ?? '',
            'upc' => $product['upc'] ?? '',
            'ean' => $product['ean'] ?? '',
            'jan' => $product['jan'] ?? '',
            'isbn' => $product['isbn'] ?? '',
            'mpn' => $product['mpn'] ?? '',
            'location' => $product['location'] ?? '',
            'quantity' => (int)($product['quantity'] ?? 0),
            'stock_status_id' => (int)($product['stock_status_id'] ?? 7),
            'image' => $product['image'] ?? '',
            'manufacturer_id' => (int)($product['manufacturer_id'] ?? 0),
            'shipping' => (int)($product['shipping'] ?? 1),
            'price' => (float)($product['price'] ?? 0),
            'points' => (int)($product['points'] ?? 0),
            'tax_class_id' => (int)($product['tax_class_id'] ?? 0),
            'date_available' => date('Y-m-d'),
            'weight' => (float)($product['weight'] ?? 0),
            'weight_class_id' => (int)($product['weight_class_id'] ?? 1),
            'length' => (float)($product['length'] ?? 0),
            'width' => (float)($product['width'] ?? 0),
            'height' => (float)($product['height'] ?? 0),
            'length_class_id' => (int)($product['length_class_id'] ?? 1),
            'subtract' => (int)($product['subtract'] ?? 1),
            'minimum' => (int)($product['minimum'] ?? 1),
            'sort_order' => (int)($product['sort_order'] ?? 0),
            'status' => (int)($product['status'] ?? 1),
            'viewed' => 0,
            'date_added' => date('Y-m-d H:i:s'),
            'date_modified' => date('Y-m-d H:i:s')
        ];

        $values = [];
        foreach ($fields as $value) {
            if (is_string($value)) {
                $values[] = "'" . $this->db->escape($value) . "'";
            } else {
                $values[] = $value;
            }
        }

        return '(' . implode(', ', $values) . ')';
    }

    /**
     * Подготвя данните за INSERT в product_description таблицата
     */
    private function prepareDescriptionInsertData($productId, $languageId, $description) {
        $values = [
            $productId,
            (int)$languageId,
            "'" . $this->db->escape($description['name'] ?? '') . "'",
            "'" . $this->db->escape($description['description'] ?? '') . "'",
            "'" . $this->db->escape($description['tag'] ?? '') . "'",
            "'" . $this->db->escape($description['meta_title'] ?? '') . "'",
            "'" . $this->db->escape($description['meta_description'] ?? '') . "'",
            "'" . $this->db->escape($description['meta_keyword'] ?? '') . "'"
        ];

        return '(' . implode(', ', $values) . ')';
    }

    /**
     * Подготвя данните за INSERT на опции
     */
    private function prepareOptionInserts($productId, $options, &$optionInserts, &$optionValueInserts) {
        foreach ($options as $option) {
            $optionId = $this->findOrCreateOption($option['option_name'], $option['option_type']);
            if (!$optionId) continue;

            $productOptionId = $this->getNextProductOptionId();

            $optionInserts[] = "({$productOptionId}, {$productId}, {$optionId}, '', " . (int)($option['required'] ?? 0) . ")";

            if (isset($option['values']) && is_array($option['values'])) {
                foreach ($option['values'] as $value) {
                    $optionValueId = $this->findOrCreateOptionValue($optionId, $value);
                    if ($optionValueId) {
                        $optionValueInserts[] = "({$productOptionId}, {$productId}, {$optionId}, {$optionValueId}, 0, 0, '0.0000', '+', 0, '+', '0.00000000', '+')";
                    }
                }
            }
        }
    }

    /**
     * Подготвя данните за INSERT на атрибути
     */
    private function prepareAttributeInserts($productId, $attributes, &$attributeInserts) {
        $languageId = $this->getLanguageId();

        foreach ($attributes as $attribute) {
            $attributeId = $this->findOrCreateAttribute($attribute['attribute_name'], $attribute['attribute_group']);
            if ($attributeId) {
                $attributeInserts[] = "({$productId}, {$attributeId}, {$languageId}, '" . $this->db->escape($attribute['attribute_value']) . "')";
            }
        }
    }

    /**
     * Получава следващия свободен product_option_id
     */
    private function getNextProductOptionId() {
        static $nextId = null;
        if ($nextId === null) {
            $query = $this->db->query("SELECT MAX(product_option_id) as max_id FROM `" . DB_PREFIX . "product_option`");
            $nextId = (int)$query->row['max_id'] + 1;
        }
        return $nextId++;
    }

    /**
     * Изпълнява всички batch INSERT заявки
     */
    private function executeBatchInserts($productInserts, $descriptionInserts, $categoryInserts, $optionInserts, $optionValueInserts, $attributeInserts) {
        // INSERT за основните продукти
        if (!empty($productInserts)) {
            $sql = "INSERT INTO `" . DB_PREFIX . "product`
                    (product_id, model, sku, upc, ean, jan, isbn, mpn, location, quantity, stock_status_id, image, manufacturer_id, shipping, price, points, tax_class_id, date_available, weight, weight_class_id, length, width, height, length_class_id, subtract, minimum, sort_order, status, viewed, date_added, date_modified)
                    VALUES " . implode(', ', $productInserts);
            $this->executeQuery($sql, 'INSERT');
        }

        // INSERT за описанията
        if (!empty($descriptionInserts)) {
            $sql = "INSERT INTO `" . DB_PREFIX . "product_description`
                    (product_id, language_id, name, description, tag, meta_title, meta_description, meta_keyword)
                    VALUES " . implode(', ', $descriptionInserts);
            $this->executeQuery($sql, 'INSERT');
        }

        // INSERT за категориите
        if (!empty($categoryInserts)) {
            $sql = "INSERT INTO `" . DB_PREFIX . "product_to_category`
                    (product_id, category_id)
                    VALUES " . implode(', ', $categoryInserts);
            $this->executeQuery($sql, 'INSERT');
        }

        // INSERT за опциите
        if (!empty($optionInserts)) {
            $sql = "INSERT INTO `" . DB_PREFIX . "product_option`
                    (product_option_id, product_id, option_id, value, required)
                    VALUES " . implode(', ', $optionInserts);
            $this->executeQuery($sql, 'INSERT');
        }

        // INSERT за стойностите на опциите
        if (!empty($optionValueInserts)) {
            $sql = "INSERT INTO `" . DB_PREFIX . "product_option_value`
                    (product_option_id, product_id, option_id, option_value_id, quantity, subtract, price, price_prefix, points, points_prefix, weight, weight_prefix)
                    VALUES " . implode(', ', $optionValueInserts);
            $this->executeQuery($sql, 'INSERT');
        }

        // INSERT за атрибутите
        if (!empty($attributeInserts)) {
            $sql = "INSERT INTO `" . DB_PREFIX . "product_attribute`
                    (product_id, attribute_id, language_id, text)
                    VALUES " . implode(', ', $attributeInserts);
            $this->executeQuery($sql, 'INSERT');
        }

        // INSERT за магазините (по подразбиране всички продукти са за магазин 0)
        if (!empty($productInserts)) {
            $storeInserts = [];
            $startProductId = $this->getNextProductId() - count($productInserts);
            for ($i = 0; $i < count($productInserts); $i++) {
                $productId = $startProductId + $i;
                $storeInserts[] = "({$productId}, 0)";
            }
            $sql = "INSERT INTO `" . DB_PREFIX . "product_to_store`
                    (product_id, store_id)
                    VALUES " . implode(', ', $storeInserts);
            $this->executeQuery($sql, 'INSERT');
        }
    }

    /**
     * Изпълнява SQL заявка или я логира в тестов режим
     */
    private function executeQuery($sql, $type = 'UNKNOWN') {
        if ($this->testMode) {
            F()->log->developer("TEST MODE - {$type} Query: " . $sql, __FILE__, __LINE__);
            $this->testModeStats['queries_saved']++;
            if ($type === 'INSERT') {
                $this->testModeStats['inserts_saved']++;
            } elseif ($type === 'UPDATE') {
                $this->testModeStats['updates_saved']++;
            }
        } else {
            $this->db->query($sql);
        }
    }
}