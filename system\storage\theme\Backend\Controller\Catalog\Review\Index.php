<?php

namespace Theme25\Backend\Controller\Catalog\Review;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'review-listing.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Коментари');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/review');
    }

    /**
     * Подготвя данните за листването на коментари
     */
    private function prepareData() {
        $this->loadModelsAs([
            'catalog/review' => 'reviewModel',
            'catalog/product' => 'productModel'
        ]);

        // Подготвяне на филтрите
        $this->prepareFilters()
             ->prepareReviewsList()
             ->preparePagination()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя филтрите за търсене
     */
    private function prepareFilters() {
        $filter_product = $this->requestGet('filter_product', '');
        $filter_author = $this->requestGet('filter_author', '');
        $filter_status = $this->requestGet('filter_status', '');
        $filter_date_added = $this->requestGet('filter_date_added', '');
        $sort = $this->requestGet('sort', 'r.date_added');
        $order = $this->requestGet('order', 'DESC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'filter_product' => $filter_product,
            'filter_author' => $filter_author,
            'filter_status' => $filter_status,
            'filter_date_added' => $filter_date_added,
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        return $this;
    }

    /**
     * Подготвя списъка с коментари
     */
    private function prepareReviewsList() {
        $filter_data = [
            'filter_product' => $this->data['filter_product'],
            'filter_author' => $this->data['filter_author'],
            'filter_status' => $this->data['filter_status'],
            'filter_date_added' => $this->data['filter_date_added'],
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        $reviews = $this->reviewModel->getReviews($filter_data);
        $review_total = $this->reviewModel->getTotalReviews($filter_data);

        // Подготвяне на данните за всеки коментар
        $review_list = [];
        foreach ($reviews as $review) {
            $edit_params = 'review_id=' . $review['review_id'];
            $delete_params = 'review_id=' . $review['review_id'];

            $review_list[] = [
                'review_id' => $review['review_id'],
                'product_name' => $review['name'] ?? 'Неизвестен продукт',
                'author' => $review['author'],
                'rating' => $review['rating'],
                'status' => $review['status'] ? 'Активен' : 'Неактивен',
                'status_class' => $review['status'] ? 'text-success' : 'text-danger',
                'date_added' => date('d.m.Y H:i', strtotime($review['date_added'])),
                'edit_url' => $this->getAdminLink('catalog/review/edit', $edit_params),
                'delete_url' => $this->getAdminLink('catalog/review/delete', $delete_params)
            ];
        }

        $this->setData([
            'reviews' => $review_list,
            'review_total' => $review_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['review_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('catalog/review', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('catalog/review', $filter_params . '&limit={limit}'));
        $pagination->setProductText('коментара');

        // Генериране на HTML код за цялата пагинация
        $this->setData('pagination_html', $pagination->render());

        return $this;
    }

    /**
     * Генерира URL параметри за филтрите
     */
    private function buildFilterParams() {
        $params = [];

        // Филтри, които трябва да се запазят в URL
        $filter_fields = [
            'filter_product',
            'filter_author',
            'filter_status',
            'filter_date_added',
            'sort',
            'order'
        ];

        foreach ($filter_fields as $field) {
            if (isset($this->data[$field]) && $this->data[$field] !== '') {
                $params[] = $field . '=' . urlencode($this->data[$field]);
            }
        }

        return $params ? '&' . implode('&', $params) : '';
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        // Генериране на URL параметри за филтрите
        $filter_params = $this->buildFilterParams();

        // Генериране на URL за сортиране
        $sort_url = '';
        if ($this->data['order'] == 'ASC') {
            $sort_url = '&order=DESC';
        } else {
            $sort_url = '&order=ASC';
        }

        $this->setData([
            'add_url' => $this->getAdminLink('catalog/review/add'),
            'delete_selected_url' => $this->getAdminLink('catalog/review/delete'),
            'filter_url' => $this->getAdminLink('catalog/review', $filter_params),
            'clear_filter_url' => $this->getAdminLink('catalog/review'),
            'ajax_search_url' => $this->getAdminLink('catalog/review/ajaxSearch'),

            // URL-и за сортиране на колони
            'sort_product' => $this->getAdminLink('catalog/review', $filter_params . '&sort=pd.name' . $sort_url),
            'sort_author' => $this->getAdminLink('catalog/review', $filter_params . '&sort=r.author' . $sort_url),
            'sort_rating' => $this->getAdminLink('catalog/review', $filter_params . '&sort=r.rating' . $sort_url),
            'sort_status' => $this->getAdminLink('catalog/review', $filter_params . '&sort=r.status' . $sort_url),
            'sort_date_added' => $this->getAdminLink('catalog/review', $filter_params . '&sort=r.date_added' . $sort_url)
        ]);

        return $this;
    }

    /**
     * AJAX метод за търсене и филтриране
     */
    public function ajaxSearch() {
        $json = [];

        ob_start();

        try {
            $this->loadModelsAs([
                'catalog/review' => 'reviewModel',
            ]);

            // Подготвяне на филтрите от AJAX заявката
            $filter_product = $this->requestGet('filter_product', '');
            $filter_author = $this->requestGet('filter_author', '');
            $filter_status = $this->requestGet('filter_status', '');
            $filter_date_added = $this->requestGet('filter_date_added', '');
            $sort = $this->requestGet('sort', 'r.date_added');
            $order = $this->requestGet('order', 'DESC');
            $page = max(1, (int)$this->requestGet('page', 1));

            // Проверка дали това е autocomplete заявка
            $is_autocomplete = $this->requestGet('autocomplete', false);
            $limit = $is_autocomplete ? min(10, max(1, (int)$this->requestGet('limit', 10))) : $this->getConfig('config_limit_admin');

            $filter_data = [
                'filter_product' => $filter_product,
                'filter_author' => $filter_author,
                'filter_status' => $filter_status,
                'filter_date_added' => $filter_date_added,
                'sort' => $sort,
                'order' => $order,
                'start' => ($page - 1) * $limit,
                'limit' => $limit
            ];

            $reviews = $this->reviewModel->getReviews($filter_data);
            $review_total = $this->reviewModel->getTotalReviews($filter_data);

            if ($is_autocomplete) {
                // Autocomplete отговор
                foreach ($reviews as $review) {
                    $json[] = [
                        'review_id' => $review['review_id'],
                        'product_name' => strip_tags(html_entity_decode($review['name'] ?? 'Неизвестен продукт', ENT_QUOTES, 'UTF-8')),
                        'author' => strip_tags(html_entity_decode($review['author'], ENT_QUOTES, 'UTF-8')),
                        'rating' => $review['rating'],
                        'status' => $review['status'] ? 'Активен' : 'Неактивен',
                        'date_added' => date('d.m.Y H:i', strtotime($review['date_added']))
                    ];
                }
            } else {
                // Пълен AJAX отговор за таблицата
                $review_list = [];
                foreach ($reviews as $review) {
                    $edit_params = 'review_id=' . $review['review_id'];
                    $delete_params = 'review_id=' . $review['review_id'];

                    $review_list[] = [
                        'review_id' => $review['review_id'],
                        'product_name' => $review['name'] ?? 'Неизвестен продукт',
                        'author' => $review['author'],
                        'rating' => $review['rating'],
                        'status' => $review['status'] ? 'Активен' : 'Неактивен',
                        'status_class' => $review['status'] ? 'text-success' : 'text-danger',
                        'date_added' => date('d.m.Y H:i', strtotime($review['date_added'])),
                        'edit_url' => $this->getAdminLink('catalog/review/edit', $edit_params),
                        'delete_url' => $this->getAdminLink('catalog/review/delete', $delete_params)
                    ];
                }

                // Подготвяне на пагинацията
                $pagination = new \Theme25\Pagination();
                $pagination->total = $review_total;
                $pagination->page = $page;
                $pagination->limit = $limit;

                $filter_params = $this->buildFilterParams();
                $pagination->url = $this->getAdminLink('catalog/review', $filter_params . '&page={page}');
                $pagination->setProductText('коментара');

                $json = [
                    'success' => true,
                    'reviews' => $review_list,
                    'total' => $review_total,
                    'pagination' => $pagination->render(),
                    'current_page' => $page,
                    'total_pages' => ceil($review_total / $limit)
                ];
            }

        } catch (\Exception $e) {
            $json = ['error' => 'Грешка при търсене: ' . $e->getMessage()];
        }

        $output = ob_get_clean();
        if($output) {
            $json['debug_output'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }
}
