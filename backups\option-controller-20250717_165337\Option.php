<?php

namespace Theme25\Backend\Controller\Catalog;

class Option extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/option');
    }

    /**
     * Главна страница с опции - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Catalog/Option/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Опции');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/option');
        }
    }

    /**
     * Добавяне на нова опция - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Catalog/Option/Add', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на опция');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/option_form');
        }
    }

    /**
     * Редактиране на опция - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Catalog/Option/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на опция');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/option_form');
        }
    }

    /**
     * Запазване на опция - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Catalog/Option/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Изтриване на опция - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Catalog/Option/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Автозавършване за опции - dispatcher метод
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Option/Autocomplete', $this);
        
        if ($subController && is_callable([$subController, 'autocomplete'])) {
            $json = $subController->autocomplete($this->requestGet());
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX търсене за autocomplete - dispatcher метод
     */
    public function ajaxSearch() {
        $subController = $this->setBackendSubController('Catalog/Option/Index', $this);
        if ($subController && is_callable([$subController, 'ajaxSearch'])) {
            return $subController->ajaxSearch();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Получаване на следващата стойност за sort_order - dispatcher метод
     */
    public function getNextSortOrder() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Option/Autocomplete', $this);
        
        if ($subController && is_callable([$subController, 'getNextSortOrder'])) {
            $json = $subController->getNextSortOrder();
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }
}
