<?php

namespace Theme25\Backend\Controller\Catalog\Review;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    public function execute() {
        $json = [];

        ob_start();

        try {
            $this->loadModelsAs([
                'catalog/review' => 'reviewModel'
            ]);

            // Проверка на правата за достъп
            if (!$this->hasPermission('modify', 'catalog/review')) {
                $json = [
                    'success' => false,
                    'error' => 'Нямате права за изтриване на коментари'
                ];
            } else {
                $deleted_count = $this->deleteReviews();
                
                if ($deleted_count > 0) {
                    $json = [
                        'success' => true,
                        'message' => 'Успешно изтрити ' . $deleted_count . ' коментар' . ($deleted_count == 1 ? '' : 'а'),
                        'deleted_count' => $deleted_count
                    ];
                } else {
                    $json = [
                        'success' => false,
                        'error' => 'Няма коментари за изтриване'
                    ];
                }
            }

        } catch (\Exception $e) {
            $json = [
                'success' => false,
                'error' => 'Системна грешка: ' . $e->getMessage()
            ];
        }

        $output = ob_get_clean();
        if($output) {
            $json['debug_output'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Изтрива коментари
     */
    private function deleteReviews() {
        $deleted_count = 0;

        // Проверка за единичен коментар
        $review_id = (int)$this->requestGet('review_id', 0);
        if ($review_id) {
            if ($this->deleteReview($review_id)) {
                $deleted_count = 1;
            }
        } else {
            // Проверка за множествено изтриване
            $selected = $this->requestPost('selected', []);
            if (is_array($selected) && !empty($selected)) {
                foreach ($selected as $review_id) {
                    $review_id = (int)$review_id;
                    if ($review_id > 0 && $this->deleteReview($review_id)) {
                        $deleted_count++;
                    }
                }
            }
        }

        return $deleted_count;
    }

    /**
     * Изтрива единичен коментар
     */
    private function deleteReview($review_id) {
        try {
            // Проверка дали коментарът съществува
            $review_info = $this->reviewModel->getReview($review_id);
            if (!$review_info) {
                return false;
            }

            // Изтриване на коментара
            $this->reviewModel->deleteReview($review_id);
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Проверява правата за достъп
     */
    private function hasPermission($action, $route) {
        // Проверка дали user обектът съществува и има метод hasPermission
        if (isset($this->user) && method_exists($this->user, 'hasPermission')) {
            return $this->user->hasPermission($action, $route);
        }
        
        // По подразбиране разрешаваме достъпа ако няма user система
        return true;
    }
}
