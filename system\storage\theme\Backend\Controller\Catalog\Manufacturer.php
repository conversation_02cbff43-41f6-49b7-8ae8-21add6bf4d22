<?php

namespace Theme25\Backend\Controller\Catalog;

class Manufacturer extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'catalog/manufacturer');
    }

    /**
     * Главна страница с производители - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Catalog/Manufacturer/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Производители');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/manufacturer');
        }
    }

    /**
     * Добавяне на нов производител - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Catalog/Manufacturer/Add', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на производител');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/manufacturer_form');
        }
    }

    /**
     * Редактиране на производител - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Catalog/Manufacturer/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на производител');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('catalog/manufacturer_form');
        }
    }

    /**
     * Запазване на производител - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Catalog/Manufacturer/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Изтриване на производител - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Catalog/Manufacturer/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Автозавършване за производители - dispatcher метод
     */
    public function autocomplete() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Manufacturer/Autocomplete', $this);
        
        if ($subController && is_callable([$subController, 'autocomplete'])) {
            $json = $subController->autocomplete($this->requestGet());
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * AJAX търсене за autocomplete - dispatcher метод
     */
    public function ajaxSearch() {
        $subController = $this->setBackendSubController('Catalog/Manufacturer/Index', $this);
        if ($subController && is_callable([$subController, 'ajaxSearch'])) {
            return $subController->ajaxSearch();
        } else {
            $json = ['error' => 'Методът не е намерен'];
            $this->setJSONResponseOutput($json);
        }
    }

    /**
     * Получаване на следващата стойност за sort_order - dispatcher метод
     */
    public function getNextSortOrder() {
        $json = [];

        ob_start();

        $subController = $this->setBackendSubController('Catalog/Manufacturer/Autocomplete', $this);
        
        if ($subController && is_callable([$subController, 'getNextSortOrder'])) {
            $json = $subController->getNextSortOrder();
        } else {
            $json['error'] = 'Методът не е намерен';
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }
}
