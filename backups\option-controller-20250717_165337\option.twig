{{ header }}{{ column_left }}

<div class="content-wrapper option-listing-page">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0 text-2xl font-bold text-gray-900">{{ heading_title }}</h1>
                </div>
                <div class="col-sm-6">
                    <div class="flex justify-end space-x-2">
                        <a href="{{ add_url }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="ri-add-line mr-2"></i>
                            Добави опция
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <!-- Филтри -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Филтри</h3>
                    
                    <form id="filter-form" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <input type="hidden" name="user_token" value="{{ user_token }}">
                        
                        <div class="relative">
                            <label for="filter-name" class="block text-sm font-medium text-gray-700 mb-1">
                                Име на опция
                            </label>
                            <input type="text" 
                                   id="filter-name" 
                                   name="filter_name" 
                                   value="{{ filter_name }}"
                                   placeholder="Търсене по име..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label for="filter-type" class="block text-sm font-medium text-gray-700 mb-1">
                                Тип опция
                            </label>
                            <select id="filter-type" 
                                    name="filter_type" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Всички типове</option>
                                {% for type_key, type_name in option_types %}
                                    <option value="{{ type_key }}" {% if filter_type == type_key %}selected{% endif %}>
                                        {{ type_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="flex items-end space-x-2">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                                <i class="ri-search-line mr-1"></i>
                                Търси
                            </button>
                            <button type="button" id="clear-filter" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                                <i class="ri-close-line mr-1"></i>
                                Изчисти
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Списък с опции -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            Опции ({{ option_total }})
                        </h3>
                        
                        <div class="flex items-center space-x-2">
                            <button id="delete-selected" 
                                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed" 
                                    disabled>
                                <i class="ri-delete-bin-line mr-1"></i>
                                Изтрий избраните
                            </button>
                        </div>
                    </div>
                </div>

                {% if options %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sort-header cursor-pointer" data-sort="od.name">
                                        Име
                                        <i class="ri-arrow-up-down-line ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sort-header cursor-pointer" data-sort="o.type">
                                        Тип
                                        <i class="ri-arrow-up-down-line ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Стойности
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sort-header cursor-pointer" data-sort="o.sort_order">
                                        Подредба
                                        <i class="ri-arrow-up-down-line ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Действия
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                {% for option in options %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" 
                                                   class="item-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                                                   value="{{ option.option_id }}">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ option.name }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                {% if option.type == 'select' %}bg-blue-100 text-blue-800
                                                {% elseif option.type == 'radio' %}bg-green-100 text-green-800
                                                {% elseif option.type == 'checkbox' %}bg-purple-100 text-purple-800
                                                {% elseif option.type == 'text' %}bg-gray-100 text-gray-800
                                                {% elseif option.type == 'textarea' %}bg-yellow-100 text-yellow-800
                                                {% elseif option.type == 'file' %}bg-red-100 text-red-800
                                                {% elseif option.type == 'date' %}bg-indigo-100 text-indigo-800
                                                {% elseif option.type == 'time' %}bg-pink-100 text-pink-800
                                                {% elseif option.type == 'datetime' %}bg-orange-100 text-orange-800
                                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                                {{ option.type_text }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {% if option.value_count > 0 %}
                                                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">
                                                    {{ option.value_count }} стойности
                                                </span>
                                            {% else %}
                                                <span class="text-gray-400">—</span>
                                            {% endif %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ option.sort_order }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div class="flex justify-end space-x-2">
                                                <a href="{{ option.edit_url }}" 
                                                   class="edit-btn text-blue-600 hover:text-blue-900 bg-blue-100 hover:bg-blue-200 px-3 py-1 rounded-md transition-colors">
                                                    <i class="ri-edit-line mr-1"></i>
                                                    Редактирай
                                                </a>
                                                <button class="delete-btn text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded-md transition-colors"
                                                        data-option-id="{{ option.option_id }}"
                                                        data-option-name="{{ option.name }}">
                                                    <i class="ri-delete-bin-line mr-1"></i>
                                                    Изтрий
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Пагинация -->
                    {% if pagination.total_pages > 1 %}
                        <div class="px-6 py-4 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-700">
                                    Показани {{ pagination.start_item }} до {{ pagination.end_item }} от {{ pagination.total_items }} резултата
                                </div>
                                
                                <div class="flex space-x-1">
                                    {% if pagination.current_page > 1 %}
                                        <a href="{{ filter_url }}&page={{ pagination.current_page - 1 }}" 
                                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                            Предишна
                                        </a>
                                    {% endif %}
                                    
                                    {% for page in 1..pagination.total_pages %}
                                        {% if page == pagination.current_page %}
                                            <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">
                                                {{ page }}
                                            </span>
                                        {% elseif page <= pagination.current_page + 2 and page >= pagination.current_page - 2 %}
                                            <a href="{{ filter_url }}&page={{ page }}" 
                                               class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                                {{ page }}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if pagination.current_page < pagination.total_pages %}
                                        <a href="{{ filter_url }}&page={{ pagination.current_page + 1 }}" 
                                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                            Следваща
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="p-8 text-center">
                        <div class="text-gray-400 mb-4">
                            <i class="ri-inbox-line text-4xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Няма намерени опции</h3>
                        <p class="text-gray-500 mb-4">Започнете като добавите първата си опция.</p>
                        <a href="{{ add_url }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center">
                            <i class="ri-add-line mr-2"></i>
                            Добави опция
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </section>
</div>

{{ footer }}
