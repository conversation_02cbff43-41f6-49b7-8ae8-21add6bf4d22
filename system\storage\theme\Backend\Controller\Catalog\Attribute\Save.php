<?php

namespace Theme25\Backend\Controller\Catalog\Attribute;

class Save extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Запазване на атрибут
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();

        // Валидация на данните
        $validation_errors = $this->validateAttributeData($post);
        if (!empty($validation_errors)) {
            $json['errors'] = $validation_errors;
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/attribute' => 'attributeModel'
        ]);

        try {
            $attribute_id = (int)($post['attribute_id'] ?? 0);

            if ($attribute_id > 0) {
                // Редактиране на съществуващ атрибут
                $this->attributeModel->editAttribute($attribute_id, $post);
                $json['success'] = 'Атрибутът беше успешно актуализиран';
                $json['attribute_id'] = $attribute_id;
            } else {
                // Добавяне на нов атрибут
                $attribute_id = $this->attributeModel->addAttribute($post);
                $json['success'] = 'Атрибутът беше успешно добавен';
                $json['attribute_id'] = $attribute_id;
            }

            $json['redirect'] = $this->getAdminLink('catalog/attribute');

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при запазване на атрибута: ' . $e->getMessage();
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира данните за атрибута
     */
    private function validateAttributeData($data) {
        $errors = [];

        // Проверка за права за модификация
        if (!$this->user->hasPermission('modify', 'catalog/attribute')) {
            $errors['permission'] = 'Нямате права за модификация на атрибути';
            return $errors;
        }

        // Проверка за група атрибути
        if (empty($data['attribute_group_id']) || !is_numeric($data['attribute_group_id'])) {
            $errors['attribute_group'] = 'Моля, изберете група атрибути';
        }

        // Проверка за описания на атрибута
        if (empty($data['attribute_description']) || !is_array($data['attribute_description'])) {
            $errors['attribute_description'] = 'Липсват описания на атрибута';
        } else {
            $has_valid_name = false;
            foreach ($data['attribute_description'] as $language_id => $description) {
                if (!empty($description['name']) && strlen(trim($description['name'])) >= 1) {
                    $has_valid_name = true;
                    
                    // Проверка за дължина на името
                    if (strlen($description['name']) > 64) {
                        $errors['name'][$language_id] = 'Името на атрибута не може да бъде по-дълго от 64 символа';
                    }
                }
            }
            
            if (!$has_valid_name) {
                $errors['name']['general'] = 'Трябва да въведете име на атрибута поне за един език';
            }
        }

        // Проверка за sort_order
        if (isset($data['sort_order'])) {
            if (!is_numeric($data['sort_order'])) {
                $errors['sort_order'] = 'Подредбата трябва да бъде число';
            } else {
                $data['sort_order'] = (int)$data['sort_order'];
            }
        } else {
            $data['sort_order'] = 0;
        }

        // Проверка за дублиращи се имена в същата група
        if (empty($errors['attribute_group']) && empty($errors['name'])) {
            $this->loadModelsAs(['catalog/attribute' => 'attributeModel']);
            
            $attribute_id = (int)($data['attribute_id'] ?? 0);
            $existing_attributes = $this->attributeModel->getAttributes([
                'filter_attribute_group_id' => $data['attribute_group_id']
            ]);

            foreach ($data['attribute_description'] as $language_id => $description) {
                if (!empty($description['name'])) {
                    foreach ($existing_attributes as $existing) {
                        if ($existing['attribute_id'] != $attribute_id && 
                            strtolower($existing['name']) == strtolower($description['name'])) {
                            $errors['name'][$language_id] = 'Атрибут с това име вече съществува в тази група';
                            break;
                        }
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Подготвя данните за запазване
     */
    private function prepareDataForSave($data) {
        $prepared_data = [
            'attribute_group_id' => (int)$data['attribute_group_id'],
            'sort_order' => (int)($data['sort_order'] ?? 0),
            'attribute_description' => []
        ];

        // Подготвяне на описанията
        if (isset($data['attribute_description']) && is_array($data['attribute_description'])) {
            foreach ($data['attribute_description'] as $language_id => $description) {
                if (!empty($description['name'])) {
                    $prepared_data['attribute_description'][(int)$language_id] = [
                        'name' => trim($description['name'])
                    ];
                }
            }
        }

        return $prepared_data;
    }
}
