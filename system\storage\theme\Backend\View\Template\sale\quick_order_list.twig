<!-- Quick Order Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Бързи поръчки</h1>
            <p class="text-gray-500 mt-1">Управление на всички бързи поръчки в магазина</p>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white border-b border-gray-200 px-6 py-3">
    <form id="filter-form" method="get" action="{{ filter_action_url }}">
        <!-- Скрито поле за user_token -->
        <input type="hidden" name="user_token" value="{{ user_token }}">
        <div class="flex flex-wrap items-center gap-4">
            <!-- Статус филтър -->
            <div class="w-full md:w-auto">
                <select name="filter_status_id"
                        class="w-full md:w-48 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
                    <option value="">Всички статуси</option>
                    {% if order_statuses %}
                        {% for status in order_statuses %}
                            <option value="{{ status.status_id }}" {% if status.status_id == filter_status_id %}selected{% endif %}>
                                {{ status.name }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>

            <!-- Общо търсене -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_search"
                       placeholder="Търсене по клиент, телефон или продукт..."
                       value="{{ filter_search }}"
                       class="w-full md:w-64 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Номер на поръчка -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_order_id"
                       placeholder="Номер на поръчка"
                       value="{{ filter_order_id }}"
                       class="w-full md:w-32 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Име на клиент -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_names"
                       placeholder="Име на клиент"
                       value="{{ filter_names }}"
                       class="w-full md:w-40 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Телефон -->
            <div class="w-full md:w-auto">
                <input type="text"
                       name="filter_phone"
                       placeholder="Телефон"
                       value="{{ filter_phone }}"
                       class="w-full md:w-32 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Дата -->
            <div class="w-full md:w-auto">
                <input type="date"
                       name="filter_date_added"
                       value="{{ filter_date_added }}"
                       class="w-full md:w-40 px-4 py-2 border border-gray-300 rounded-button text-gray-700 bg-white focus:outline-none focus:border-primary text-sm">
            </div>

            <!-- Скрити полета за сортиране и пагинация -->
            <input type="hidden" name="sort" value="{{ sort }}">
            <input type="hidden" name="order" value="{{ order }}">
            <input type="hidden" name="page" value="1">

            <!-- Бутони -->
            <div class="w-full md:w-auto flex gap-2">
                <button type="submit"
                        class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm">
                    <i class="ri-search-line mr-1"></i>
                    Търси
                </button>

                {% if filter_status_id or filter_search or filter_order_id or filter_names or filter_phone or filter_date_added %}
                <a href="{{ clear_filters_url }}"
                   class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center text-sm">
                    <i class="ri-close-line mr-1"></i>
                    Изчисти
                </a>
                {% endif %}
            </div>
        </div>
    </form>
</div>
<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <div class="bg-white rounded shadow overflow-hidden">

        {% if error_warning %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="ri-error-warning-line text-red-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">{{ error_warning }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        {% if success %}
        <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="ri-check-line text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">{{ success }}</p>
                </div>
            </div>
        </div>
        {% endif %}

        {% if quick_orders %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Номер
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Клиент
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Продукт
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Цена/Общо
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Статус
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Дата
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Действие
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for order in quick_orders %}
                    <tr class="hover:bg-gray-50" data-order-id="{{ order.order_id }}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{{ order.order_id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                            <div class="text-sm text-gray-500">{{ order.phone }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center space-x-3">
                                {% if order.product_image %}
                                <div class="flex-shrink-0">
                                    <img src="{{ order.product_image }}" alt="{{ order.product_name }}" class="w-12 h-12 object-cover rounded border">
                                </div>
                                {% endif %}
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">{{ order.product_name }}</div>
                                    <div class="text-sm text-gray-500">Количество: {{ order.quantity }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                <div class="font-medium">{{ order.price }}</div>
                                <div class="text-gray-500">Общо: {{ order.total }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="status-badge inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ order.status_class }}">
                                {{ order.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ order.date_added }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{{ order.view_url }}"
                                   class="text-primary hover:text-primary/80 transition-colors"
                                   title="Преглед">
                                    <i class="ri-eye-line"></i>
                                </a>
                                <a href="{{ order.edit_url }}"
                                   class="text-blue-600 hover:text-blue-800 transition-colors"
                                   title="Редактиране">
                                    <i class="ri-edit-line"></i>
                                </a>
                                <button type="button"
                                        data-action="delete"
                                        data-order-id="{{ order.order_id }}"
                                        data-order-name="{{ order.customer_name }}"
                                        class="text-red-600 hover:text-red-800 transition-colors"
                                        title="Изтриване">
                                    <i class="ri-delete-bin-line"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination and Results -->
        {% if pagination or results %}
        <div class="bg-white px-4 py-3 border-t border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <!-- Results Info -->
                {% if results %}
                <div class="text-sm text-gray-700">
                    {{ results }}
                </div>
                {% endif %}

                <!-- Pagination -->
                {% if pagination %}
                <div class="flex-1 flex justify-center sm:justify-end">
                    {{ pagination|raw }}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="text-center py-12">
            <i class="ri-inbox-line text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Няма намерени бързи поръчки</h3>
            <p class="text-gray-500">Няма намерени бързи поръчки с текущите филтри.</p>
        </div>
        {% endif %}
    </div>
</main>

<script>
/**
 * Променя лимита на записи на страница и презарежда страницата
 */
function changeLimitAndReload(newLimit) {
    const url = new URL(window.location);
    url.searchParams.set('limit', newLimit);
    url.searchParams.set('page', '1'); // Рестартиране към първа страница

    // Запазване на user_token ако го има
    {% if user_token %}
    if (!url.searchParams.has('user_token')) {
        url.searchParams.set('user_token', '{{ user_token }}');
    }
    {% endif %}

    window.location.href = url.toString();
}

// Инициализация на QuickOrder модула при зареждане на страницата
document.addEventListener('DOMContentLoaded', function() {
    // Модулът се инициализира автоматично от quick-order.js
    console.log('QuickOrder списък зареден');
});
</script>
