<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($parent_controller) {
        parent::__construct($parent_controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
        $this->loadStyles();
    }

    /**
     * Изпълнява подготовката на формата за редактиране на категория
     */
    public function execute() {
        $category_id = $this->requestGet('category_id', 0);
        
        if ($category_id) {
            $this->setTitle('Редакция на категория');
        } else {
            $this->setTitle('Добавяне на категория');
        }
        
        $this->initAdminData();
        $this->prepareCategoryForm();
        $this->renderTemplateWithDataAndOutput('catalog/category_form');
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме category-form.js, ако съществува
        $categoryFormUrl = $base . 'backend_js/category-form.js';
        $categoryFormPath = DIR_THEME . 'Backend/View/Javascript/category-form.js';
        
        if (file_exists($categoryFormPath)) {
            $lastModified = filemtime($categoryFormPath);
            $categoryFormUrl .= '?v=' . $lastModified;
            $this->document->addScript($categoryFormUrl, 'footer');
        }

        // Зареждаме AdvancedRichTextEditor.js
        $editorUrl = $base . 'backend_js/AdvancedRichTextEditor.js';
        $editorPath = DIR_THEME . 'Backend/View/Javascript/AdvancedRichTextEditor.js';

        if (file_exists($editorPath)) {
            $lastModified = filemtime($editorPath);
            $editorUrl .= '?v=' . $lastModified;
            $this->document->addScript($editorUrl, 'footer');
        }

        
        // Зареждаме image-manager.js 
        $imageManagerUrl = $base . 'backend_js/image-manager.js';
        $imageManagerPath = DIR_THEME . 'Backend/View/Javascript/image-manager.js';

        if (file_exists($imageManagerPath)) {
            $lastModified = filemtime($imageManagerPath);
            $imageManagerUrl .= '?v=' . $lastModified;
            $this->document->addScript($imageManagerUrl, 'footer');
        }
    }

    /**
     * Зарежда необходимите CSS файлове
     */
     protected function loadStyles() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;

        $RichEditorUrl = $base . 'backend_css/rich-text-editor.css';
        $RichEditorPath = DIR_THEME . 'Backend/View/Css/rich-text-editor.css';
        
        if (file_exists($RichEditorPath)) {
            $lastModified = filemtime($RichEditorPath);
            $RichEditorUrl .= '?v=' . $lastModified;
            $this->document->addStyle($RichEditorUrl);
        }

        $this->document->addStyle('https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css');
        
    }

    /**
     * Подготвя формата за редактиране/добавяне на категория
     */
    public function prepareCategoryForm() {
        $this->loadLanguage('catalog/category');

        // Зареждане на модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel',
            'localisation/language' => 'languageModel',
            'setting/store' => 'storeModel',
            'tool/image'              => 'imageModel',
            'tool/Imageservice'       => 'imageService'
        ]);

        $category_id = $this->requestGet('category_id', 0);
        $category_info = [];

        if ($category_id && $this->request->server['REQUEST_METHOD'] != 'POST') {
            $category_info = $this->categoryModel->getCategory($category_id);
        }

        // Подготовка на основните данни
        $this->prepareCategoryBasicData($category_id, $category_info);
        
        // Подготовка на езиковите данни
        $this->prepareCategoryLanguageData($category_id, $category_info);
        
        // Подготовка на SEO данни
        $this->prepareCategorySeoData($category_id, $category_info);
        
        // Подготовка на данни за изображения
        $this->prepareCategoryImageData($category_id, $category_info);
        
        // Подготовка на данни за магазини
        // $this->prepareCategoryStoreData($category_id, $category_info);
        
        // Подготовка на данни за родителски категории
        $this->prepareCategoryParentData($category_id, $category_info);
        
        // Подготовка на данни за формата
        $this->prepareCategoryFormData($category_id);
    }

    /**
     * Подготвя основните данни за категорията
     */
    private function prepareCategoryBasicData($category_id, $category_info) {
        // Основни полета
        $basic_fields = [
            'category_id' => $category_id,
            'sort_order' => 0,
            'status' => 0,
            'top' => 0,
            'column' => 1
        ];

        foreach ($basic_fields as $field => $default) {
            if (isset($category_info[$field])) {
                $this->setData($field, $category_info[$field]);
            } else {
                $this->setData($field, $default);
            }
        }

    
        $this->setData('site_url', HTTPS_CATALOG);
    }

    /**
     * Подготвя езиковите данни за категорията
     */
    private function prepareCategoryLanguageData($category_id, $category_info) {
        // Получаване на всички езици
        $this->prepareLanguageData();
        $languages = $this->languages;
        $this->setData('languages', $languages);

        // Подготовка на описанията за всички езици
        $category_description = [];

        foreach ($languages as $language) {
            $language_id = $language['language_id'];
            
            if (isset($this->request->post['category_description'][$language_id])) {
                $category_description[$language_id] = $this->request->post['category_description'][$language_id];
            } elseif ($category_id) {
                $description = $this->categoryModel->getCategoryDescriptions($category_id);
                if (isset($description[$language_id])) {
                    $category_description[$language_id] = $description[$language_id];
                } else {
                    $category_description[$language_id] = [
                        'name' => '',
                        'description' => '',
                        'meta_title' => '',
                        'meta_description' => '',
                        'meta_keyword' => ''
                    ];
                }
            } else {
                $category_description[$language_id] = [
                    'name' => '',
                    'description' => '',
                    'meta_title' => '',
                    'meta_description' => '',
                    'meta_keyword' => ''
                ];
            }
        }

        $this->setData('category_description', $category_description);
    }

    /**
     * Подготвя SEO данните за категорията
     */
    private function prepareCategorySeoData($category_id, $category_info) {
        // Получаване на всички магазини
        // $stores = $this->storeModel->getStores();
        // $all_stores = [['store_id' => 0, 'name' => $this->getConfig('config_name')]];
        // $all_stores = array_merge($all_stores, $stores);

        // Получаване на всички езици
        $languages = $this->languages;

        // Подготовка на SEO URL данни
        $category_seo_url = [];

        // foreach ($all_stores as $store) {
            foreach ($languages as $language) {
                // $store_id = $store['store_id'];
                $language_id = $language['language_id'];
                
                if (isset($this->request->post['category_seo_url'][$language_id])) {
                    $category_seo_url[$language_id] = $this->request->post['category_seo_url'][$language_id];
                } elseif ($category_id) {
                    $seo_urls = $this->categoryModel->getCategorySeoUrls($category_id);
                    if($seo_urls){
                        $seo_urls = $seo_urls[0];
                    }
                    if (isset($seo_urls[$language_id])) {
                        $category_seo_url[$language_id] = $seo_urls[$language_id];
                    } else {
                        $category_seo_url[$language_id] = '';
                    }
                } else {
                    $category_seo_url[$language_id] = '';
                }
            }
        // }

        $this->setData('category_seo_url', $category_seo_url);
        // $this->setData('stores', $all_stores);
    }

    /**
     * Подготвя данните за изображения
     */
    private function prepareCategoryImageData($category_id, $category_info) {
        // Основно изображение
        if (isset($this->request->post['image'])) {
            $this->setData('image', $this->request->post['image']);
        } elseif (!empty($category_info['image'])) {
            $this->setData('image', $category_info['image']);
        } else {
            $this->setData('image', '');
        }

        $this->prepareImageData($this->getData('image'));

        // Placeholder изображение
        $this->setData('placeholder', $this->imageModel->resize('no_image.png', 192, 192));
    }

    private function prepareImageData($image) {
        $main_image_path = $image;
        $this->setData('image', $main_image_path);

        if ($main_image_path && is_file(ThemeData()->getImageServerPath() . $main_image_path)) {
            $image_details = $this->imageService->getImageDetailsByPath($main_image_path, 192, 192);
            $this->setData('thumb', $image_details['resized_image_url']);
        } else {
            $this->setData('thumb', $this->imageModel->resize('no_image.png', 192, 192));
        }
    }

    /**
     * Подготвя данните за магазини
     */
    private function prepareCategoryStoreData($category_id, $category_info) {
        // Категория към магазини
        if (isset($this->request->post['category_store'])) {
            $category_store = $this->request->post['category_store'];
        } elseif ($category_id) {
            $category_store = $this->categoryModel->getCategoryStores($category_id);
        } else {
            $category_store = [0]; // По подразбиране главният магазин
        }

        $this->setData('category_store', $category_store);
    }

    /**
     * Подготвя данните за родителски категории
     */
    private function prepareCategoryParentData($category_id, $category_info) {
        // Задаване на parent_id
        if (isset($this->request->post['parent_id'])) {
            $parent_id = $this->request->post['parent_id'];
        } elseif (!empty($category_info)) {
            $parent_id = $category_info['parent_id'];
        } else {
            $parent_id = 0;
        }
        $this->setData('parent_id', $parent_id);

        // Задаване на името на родителската категория
        $parent_category_name = '';
        if ($parent_id > 0) {
            $parent_info = $this->categoryModel->getCategory($parent_id);
            if ($parent_info) {
                $parent_category_name = $parent_info['name'];
            }
        }
        $this->setData('parent_category_name', $parent_category_name);

        // // Получаване на всички категории за родителски dropdown
        // $categories = $this->categories;

        // // Филтриране на текущата категория от списъка (не може да бъде родител на себе си)
        // if ($category_id) {
        //     $categories = array_filter($categories, function($cat) use ($category_id) {
        //         return $cat['category_id'] != $category_id;
        //     });
        // }

        // $this->setData('categories', $categories);
    }

    /**
     * Подготвя данните за формата
     */
    private function prepareCategoryFormData($category_id) {
        // URL адреси
        $this->setData([
            'action' => $this->getAdminLink('catalog/category/save'),
            'cancel' => $this->getAdminLink('catalog/category'),
        ]);

        // Токен за сигурност
        $this->setData('user_token', $this->session->data['user_token']);
        
        // Активен език ID
        $this->setData('active_language_id', $this->getLanguageId());
    }
}
