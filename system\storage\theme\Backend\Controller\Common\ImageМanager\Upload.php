<?php

namespace Theme25\Backend\Controller\Common\ImageManager;

class Upload extends \Theme25\ControllerSubMethods {

    /**
     * Обработва качването на файлове
     * 
     * @return array Резултат от качването
     */
    public function processFiles() {
        $this->loadLanguage('common/filemanager');
        
        // Валидация на директория и права на достъп
        $validationController = $this->setBackendSubController('Common/ImageManager/Validation', $this->_controller);
        $validation = $validationController->validateDirectoryAccess();
        
        if (!$validation['valid']) {
            return [
                'success' => false,
                'error' => $validation['error']
            ];
        }
        
        $directory = $validation['directory'];
        
        // Обработка на качените файлове
        return $this->processUploadedFiles($directory, $validationController);
    }
    
    /**
     * Обработва качените файлове
     * 
     * @param string $directory Целева директория
     * @param object $validationController Контролер за валидация
     * @return array Резултат от качването
     */
    public function processUploadedFiles($directory, $validationController) {
        if (empty($this->requestFiles('files'))) {
            return [
                'success' => false,
                'error' => 'Няма избрани файлове за качване'
            ];
        }
        
        $files = $this->requestFiles('files');
        
        // Валидация на файловете
        $fileValidation = $validationController->validateMultipleFiles($files);
        
        if (!$fileValidation['valid']) {
            return [
                'success' => false,
                'error' => 'Няма валидни файлове за качване',
                'errors' => $fileValidation['errors']
            ];
        }
        
        // Обработка на валидните файлове
        return $this->uploadValidFiles($fileValidation['validFiles'], $directory);
    }
    
    /**
     * Качва валидните файлове
     * 
     * @param array $validFiles Масив с валидни файлове
     * @param string $directory Целева директория
     * @return array Резултат от качването
     */
    private function uploadValidFiles($validFiles, $directory) {
        $uploadedFiles = [];
        $errors = [];
        
        foreach ($validFiles as $file) {
            $result = $this->processSingleFile($file, $directory);
            
            if ($result['success']) {
                $uploadedFiles[] = $result['file'];
            } else {
                $errors[] = $result['error'];
            }
        }
        
        return $this->prepareUploadResponse($uploadedFiles, $errors);
    }
    
    /**
     * Обработва единичен файл
     * 
     * @param array $file Данни за файла
     * @param string $directory Целева директория
     * @return array Резултат от обработката
     */
    public function processSingleFile($file, $directory) {
        // Генериране на уникално име на файла
        $filename = $this->generateUniqueFilename($file['name'], $directory);
        $targetPath = $directory . '/' . $filename;
        
        // Преместване на качения файл
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            return $this->createSuccessfulUploadResult($filename, $targetPath);
        } else {
            return [
                'success' => false,
                'error' => 'Грешка при качване на файл: ' . $file['name']
            ];
        }
    }
    
    /**
     * Създава резултат за успешно качен файл
     * 
     * @param string $filename Име на файла
     * @param string $targetPath Пълен път до файла
     * @return array Резултат за успешно качване
     */
    private function createSuccessfulUploadResult($filename, $targetPath) {
        $this->loadModelAs('tool/image', 'imageModel');
        $relativePath = str_replace(ThemeData()->getImageServerPath(), '', $targetPath);
        
        return [
            'success' => true,
            'file' => [
                'name' => $filename,
                'path' => $relativePath,
                'thumb' => $this->imageModel->resize($relativePath, 150, 150),
                'size' => filesize($targetPath)
            ]
        ];
    }
    
    /**
     * Генерира уникално име на файл
     * 
     * @param string $originalName Оригинално име на файла
     * @param string $directory Целева директория
     * @return string Уникално име на файла
     */
    public function generateUniqueFilename($originalName, $directory) {
        $filename = basename(html_entity_decode($originalName, ENT_QUOTES, 'UTF-8'));
        $targetPath = $directory . '/' . $filename;
        
        $counter = 1;
        $originalNameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        
        while (file_exists($targetPath)) {
            $filename = $originalNameWithoutExt . '_' . $counter . '.' . $extension;
            $targetPath = $directory . '/' . $filename;
            $counter++;
        }
        
        return $filename;
    }
    
    /**
     * Подготвя отговора за качването
     * 
     * @param array $uploadedFiles Масив с качени файлове
     * @param array $errors Масив с грешки
     * @return array Финален отговор
     */
    private function prepareUploadResponse($uploadedFiles, $errors) {
        if (!empty($uploadedFiles)) {
            $response = [
                'success' => true,
                'message' => 'Успешно качени ' . count($uploadedFiles) . ' файла',
                'files' => $uploadedFiles
            ];
            
            if (!empty($errors)) {
                $response['errors'] = $errors;
                $response['message'] .= '. Възникнаха грешки при ' . count($errors) . ' файла.';
            }
            
            return $response;
        } elseif (!empty($errors)) {
            return [
                'success' => false,
                'error' => 'Възникнаха грешки при качване на файловете',
                'errors' => $errors
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Няма файлове за обработка'
            ];
        }
    }
    
    /**
     * Качва единичен файл (за използване от други контролери)
     * 
     * @param array $fileData Данни за файла
     * @param string $targetDirectory Целева директория (опционално)
     * @return array Резултат от качването
     */
    public function uploadSingleFile($fileData, $targetDirectory = null) {
        // Ако не е зададена директория, използваме текущата
        if ($targetDirectory === null) {
            $validationController = $this->setBackendSubController('Common/ImageManager/Validation', $this->_controller);
            $validation = $validationController->validateDirectoryAccess();
            
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }
            
            $targetDirectory = $validation['directory'];
        }
        
        // Валидация на файла
        $validationController = $this->setBackendSubController('Common/ImageManager/Validation', $this->_controller);
        $fileValidation = $validationController->validateUploadedFile($fileData);
        
        if (!$fileValidation['valid']) {
            return [
                'success' => false,
                'error' => $fileValidation['error']
            ];
        }
        
        // Качване на файла
        return $this->processSingleFile($fileData, $targetDirectory);
    }

    private function getCurrentDirectory() {
        return ltrim(urldecode($this->requestGet('directory', '')), '/');
    }
}
