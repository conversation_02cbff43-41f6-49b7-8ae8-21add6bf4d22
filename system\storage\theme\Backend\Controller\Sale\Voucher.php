<?php

namespace Theme25\Backend\Controller\Sale;

/**
 * Главен контролер за подаръчни ваучери
 *
 * Този контролер управлява всички операции свързани с подаръчни ваучери в административната част.
 * Следва sub-controller архитектурата на темата и предоставя методи за преглед, редактиране,
 * добавяне и актуализиране на статуса на ваучери.
 *
 * @package Theme25\Backend\Controller\Sale
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Voucher extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'sale/voucher');

        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'voucher.js'
        ], 'footer');
    }

    /**
     * Основен метод - показва списъка с ваучери
     */
    public function index() {
        $this->setTitle('Подаръчни ваучери');

        // Инициализиране на данните
        $this->initAdminData();

        try {
            $subController = $this->setBackendSubController('Sale/Voucher/Index', $this);

            // Подготовка на данните
            $subController->prepareData();

            // Добавяне на основни данни ако липсват
            if (!isset($this->data['vouchers'])) {
                $this->setData('vouchers', []);
            }
            if (!isset($this->data['status_options'])) {
                $this->setData('status_options', [
                    ['value' => '', 'text' => 'Всички статуси'],
                    ['value' => '1', 'text' => 'Активен'],
                    ['value' => '0', 'text' => 'Неактивен']
                ]);
            }
            if (!isset($this->data['pagination_html'])) {
                $this->setData('pagination_html', '');
            }

        } catch (Exception $e) {
            // Ако има грешка, задаваме минимални данни
            $this->setData([
                'vouchers' => [],
                'status_options' => [
                    ['value' => '', 'text' => 'Всички статуси'],
                    ['value' => '1', 'text' => 'Активен'],
                    ['value' => '0', 'text' => 'Неактивен']
                ],
                'pagination_html' => ''
            ]);
        }

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('sale/voucher_list');
    }

    /**
     * Добавяне на нов ваучер
     */
    public function add() {
        $this->setTitle('Добавяне на подаръчен ваучер');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Sale/Voucher/Add', $this);

        // Подготовка на данните
        $subController->execute();

        $this->renderTemplateWithDataAndOutput('sale/voucher_form');
    }

    /**
     * Редактиране на ваучер
     */
    public function edit() {
        $this->setTitle('Редактиране на подаръчен ваучер');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Sale/Voucher/Edit', $this);

        // Подготовка на данните
        $subController->execute();

        $this->renderTemplateWithDataAndOutput('sale/voucher_form');
    }

    /**
     * Изтриване на ваучер
     */
    public function delete() {
        $subController = $this->setBackendSubController('Sale/Voucher/Delete', $this);
        $subController->execute();
    }

    /**
     * AJAX метод за актуализиране на статус
     */
    public function status_update() {
        $subController = $this->setBackendSubController('Sale/Voucher/StatusUpdate', $this);
        $subController->execute();
    }

    /**
     * Изпращане на ваучер по email
     */
    public function send() {
        $subController = $this->setBackendSubController('Sale/Voucher/Send', $this);
        $subController->execute();
    }

    /**
     * История на ваучер
     */
    public function history() {
        $this->setTitle('История на подаръчен ваучер');

        // Инициализиране на данните
        $this->initAdminData();

        $subController = $this->setBackendSubController('Sale/Voucher/History', $this);

        // Подготовка на данните
        $subController->execute();

        $this->renderTemplateWithDataAndOutput('sale/voucher_history');
    }

}
