<?php

namespace Theme25\Backend\Controller\Common;

class ImageManager extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/imagemanager');
    }

    /**
     * Основен метод за листване на изображения и папки
     */
    public function index() {
        $this->loadLanguage('common/filemanager');
        
        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'common/filemanager')) {
            $this->setJSONResponseOutput([
                'error' => $this->getLanguageText('error_permission')
            ]);
            return;
        }
        
        // Получаване на директорията
        $directory = $this->getTargetDirectory();
        
        // Проверка за валидност на директорията
        if (!$this->isValidDirectory($directory)) {
            $this->setJSONResponseOutput([
                'error' => 'Невалидна директория'
            ]);
            return;
        }
        
        // Зареждане на съдържанието на директорията
        $result = $this->loadDirectoryContents($directory);
        
        $this->setJSONResponseOutput($result);
    }
    
    /**
     * Метод за качване на файлове
     */
    public function upload() {
        $this->loadLanguage('common/filemanager');
        
        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'common/filemanager')) {
            $this->setJSONResponseOutput([
                'error' => $this->getLanguageText('error_permission')
            ]);
            return;
        }
        
        // Получаване на директорията
        $directory = $this->getTargetDirectory();
        
        // Проверка за валидност на директорията
        if (!$this->isValidDirectory($directory)) {
            $this->setJSONResponseOutput([
                'error' => 'Невалидна директория'
            ]);
            return;
        }
        
        // Обработка на качените файлове
        $result = $this->processUploadedFiles($directory);
        
        $this->setJSONResponseOutput($result);
    }
    
    /**
     * Получава целевата директория от заявката
     * 
     * @return string Път до директорията
     */
    private function getTargetDirectory() {
        $requestDirectory = $this->requestGet('directory', '');
        
        if ($requestDirectory) {
            return rtrim(DIR_IMAGE . 'catalog/' . str_replace('*', '', $requestDirectory), '/');
        }
        
        return DIR_IMAGE . 'catalog';
    }
    
    /**
     * Проверява дали директорията е валидна и в рамките на catalog папката
     * 
     * @param string $directory Път до директорията
     * @return bool
     */
    private function isValidDirectory($directory) {
        if (!is_dir($directory)) {
            return false;
        }
        
        $realPath = str_replace('\\', '/', realpath($directory));
        $catalogPath = str_replace('\\', '/', DIR_IMAGE . 'catalog');
        
        return substr($realPath, 0, strlen($catalogPath)) === $catalogPath;
    }
    
    /**
     * Зарежда съдържанието на директория
     * 
     * @param string $directory Път до директорията
     * @return array Резултат с данни за директорията
     */
    private function loadDirectoryContents($directory) {
        $this->loadModelAs('tool/image', 'imageModel');
        
        // Получаване на папки
        $directories = glob($directory . '/*', GLOB_ONLYDIR);
        if (!$directories) {
            $directories = [];
        }
        
        // Получаване на изображения
        $files = glob($directory . '/*.{jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF}', GLOB_BRACE);
        if (!$files) {
            $files = [];
        }
        
        $items = [];
        
        // Обработка на папките
        foreach ($directories as $dir) {
            $items[] = $this->createDirectoryItem($dir);
        }
        
        // Обработка на файловете
        foreach ($files as $file) {
            $items[] = $this->createImageItem($file);
        }
        
        // Сортиране - папки първо, след това файлове
        usort($items, function($a, $b) {
            if ($a['type'] != $b['type']) {
                return $a['type'] == 'directory' ? -1 : 1;
            }
            return strcasecmp($a['name'], $b['name']);
        });
        
        // Създаване на breadcrumb навигация
        $breadcrumb = $this->createBreadcrumb();
        
        // Получаване на родителската директория
        $parentDirectory = $this->getParentDirectory();
        
        return [
            'success' => true,
            'items' => $items,
            'breadcrumb' => $breadcrumb,
            'current_directory' => $this->requestGet('directory', ''),
            'parent_directory' => $parentDirectory
        ];
    }
    
    /**
     * Създава елемент за папка
     * 
     * @param string $dirPath Път до папката
     * @return array Данни за папката
     */
    private function createDirectoryItem($dirPath) {
        $name = basename($dirPath);
        $relativePath = str_replace(DIR_IMAGE . 'catalog/', '', $dirPath);
        
        return [
            'type' => 'directory',
            'name' => $name,
            'path' => $relativePath,
            'thumb' => '',
            'size' => 0,
            'date_modified' => date('Y-m-d H:i:s', filemtime($dirPath))
        ];
    }
    
    /**
     * Създава елемент за изображение
     * 
     * @param string $filePath Път до файла
     * @return array Данни за изображението
     */
    private function createImageItem($filePath) {
        $name = basename($filePath);
        $relativePath = str_replace(DIR_IMAGE, '', $filePath);
        
        return [
            'type' => 'image',
            'name' => $name,
            'path' => $relativePath,
            'thumb' => $this->imageModel->resize($relativePath, 150, 150),
            'size' => filesize($filePath),
            'date_modified' => date('Y-m-d H:i:s', filemtime($filePath))
        ];
    }
    
    /**
     * Създава breadcrumb навигация
     * 
     * @return array Breadcrumb данни
     */
    private function createBreadcrumb() {
        $breadcrumb = [];
        $currentDirectory = $this->requestGet('directory', '');
        
        if ($currentDirectory) {
            $parts = explode('/', trim($currentDirectory, '/'));
            $currentPath = '';
            
            $breadcrumb[] = [
                'name' => 'Начало',
                'path' => ''
            ];
            
            foreach ($parts as $part) {
                $currentPath .= ($currentPath ? '/' : '') . $part;
                $breadcrumb[] = [
                    'name' => $part,
                    'path' => $currentPath
                ];
            }
        } else {
            $breadcrumb[] = [
                'name' => 'Начало',
                'path' => ''
            ];
        }
        
        return $breadcrumb;
    }
    
    /**
     * Получава родителската директория
     * 
     * @return string Път до родителската директория
     */
    private function getParentDirectory() {
        $currentDirectory = $this->requestGet('directory', '');
        
        if ($currentDirectory) {
            $parts = explode('/', trim($currentDirectory, '/'));
            if (count($parts) > 1) {
                array_pop($parts);
                return implode('/', $parts);
            }
        }
        
        return '';
    }

    /**
     * Обработва качените файлове
     *
     * @param string $directory Целева директория
     * @return array Резултат от качването
     */
    private function processUploadedFiles($directory) {
        $uploadedFiles = [];
        $errors = [];

        if (!empty($this->request->files['files'])) {
            $files = $this->request->files['files'];

            // Обработка на множество файлове
            if (is_array($files['name'])) {
                for ($i = 0; $i < count($files['name']); $i++) {
                    if ($files['error'][$i] == UPLOAD_ERR_OK) {
                        $fileData = [
                            'name' => $files['name'][$i],
                            'type' => $files['type'][$i],
                            'tmp_name' => $files['tmp_name'][$i],
                            'error' => $files['error'][$i],
                            'size' => $files['size'][$i]
                        ];

                        $result = $this->processSingleFile($fileData, $directory);

                        if ($result['success']) {
                            $uploadedFiles[] = $result['file'];
                        } else {
                            $errors[] = $result['error'];
                        }
                    }
                }
            } else {
                // Обработка на единичен файл
                if ($files['error'] == UPLOAD_ERR_OK) {
                    $result = $this->processSingleFile($files, $directory);

                    if ($result['success']) {
                        $uploadedFiles[] = $result['file'];
                    } else {
                        $errors[] = $result['error'];
                    }
                }
            }
        }

        // Подготовка на отговора
        if (!empty($uploadedFiles)) {
            return [
                'success' => true,
                'message' => 'Успешно качени ' . count($uploadedFiles) . ' файла',
                'files' => $uploadedFiles,
                'errors' => $errors
            ];
        } elseif (!empty($errors)) {
            return [
                'success' => false,
                'error' => 'Възникнаха грешки при качване на файловете',
                'errors' => $errors
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Няма избрани файлове за качване'
            ];
        }
    }

    /**
     * Обработва единичен файл
     *
     * @param array $file Данни за файла
     * @param string $directory Целева директория
     * @return array Резултат от обработката
     */
    private function processSingleFile($file, $directory) {
        // Валидация на файла
        $validationResult = $this->validateUploadedFile($file);
        if (!$validationResult['valid']) {
            return [
                'success' => false,
                'error' => $validationResult['error']
            ];
        }

        // Генериране на уникално име на файла
        $filename = $this->generateUniqueFilename($file['name'], $directory);
        $targetPath = $directory . '/' . $filename;

        // Преместване на качения файл
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            $this->loadModelAs('tool/image', 'imageModel');
            $relativePath = str_replace(DIR_IMAGE, '', $targetPath);

            return [
                'success' => true,
                'file' => [
                    'name' => $filename,
                    'path' => $relativePath,
                    'thumb' => $this->imageModel->resize($relativePath, 150, 150),
                    'size' => filesize($targetPath)
                ]
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Грешка при качване на файл: ' . $file['name']
            ];
        }
    }

    /**
     * Валидира качен файл
     *
     * @param array $file Данни за файла
     * @return array Резултат от валидацията
     */
    private function validateUploadedFile($file) {
        $filename = basename(html_entity_decode($file['name'], ENT_QUOTES, 'UTF-8'));

        // Проверка на дължината на името
        if ((utf8_strlen($filename) < 3) || (utf8_strlen($filename) > 255)) {
            return [
                'valid' => false,
                'error' => 'Невалидно име на файл: ' . $filename
            ];
        }

        // Проверка на разширението
        $allowedExtensions = ['jpg', 'jpeg', 'gif', 'png'];
        $extension = utf8_strtolower(utf8_substr(strrchr($filename, '.'), 1));

        if (!in_array($extension, $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => 'Неподдържан формат на файл: ' . $filename
            ];
        }

        // Проверка на MIME типа
        $allowedMimes = ['image/jpeg', 'image/pjpeg', 'image/png', 'image/x-png', 'image/gif'];

        if (!in_array($file['type'], $allowedMimes)) {
            return [
                'valid' => false,
                'error' => 'Невалиден MIME тип: ' . $filename
            ];
        }

        // Проверка на размера (5MB лимит)
        if ($file['size'] > 5 * 1024 * 1024) {
            return [
                'valid' => false,
                'error' => 'Файлът е твърде голям: ' . $filename
            ];
        }

        return ['valid' => true];
    }

    /**
     * Генерира уникално име на файл
     *
     * @param string $originalName Оригинално име на файла
     * @param string $directory Целева директория
     * @return string Уникално име на файла
     */
    private function generateUniqueFilename($originalName, $directory) {
        $filename = basename(html_entity_decode($originalName, ENT_QUOTES, 'UTF-8'));
        $targetPath = $directory . '/' . $filename;

        $counter = 1;
        $originalNameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);

        while (file_exists($targetPath)) {
            $filename = $originalNameWithoutExt . '_' . $counter . '.' . $extension;
            $targetPath = $directory . '/' . $filename;
            $counter++;
        }

        return $filename;
    }
}
