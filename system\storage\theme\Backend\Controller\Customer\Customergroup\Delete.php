<?php

namespace Theme25\Backend\Controller\Customer\Customergroup;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява изтриването на клиентска група/групи
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer_group')) {
            $json['error'] = 'Нямате права за изтриване на клиентски групи!';
        } else {
            $this->load->model('customer/customer_group');

            if (isset($this->request->post['selected']) && is_array($this->request->post['selected'])) {
                // Масово изтриване
                $deleted_count = 0;
                $errors = [];

                foreach ($this->request->post['selected'] as $customer_group_id) {
                    $customer_group_id = (int)$customer_group_id;
                    
                    if ($this->canDeleteCustomerGroup($customer_group_id)) {
                        $this->model_customer_customer_group->deleteCustomerGroup($customer_group_id);
                        $deleted_count++;
                    } else {
                        $customer_group_info = $this->model_customer_customer_group->getCustomerGroup($customer_group_id);
                        if ($customer_group_info) {
                            $errors[] = sprintf('Клиентската група "%s" не може да бъде изтрита, защото има свързани клиенти!', 
                                $customer_group_info['name']);
                        }
                    }
                }

                if ($deleted_count > 0) {
                    $json['success'] = sprintf('Успешно изтрити %d клиентски групи!', $deleted_count);
                }

                if (!empty($errors)) {
                    $json['warning'] = implode('<br>', $errors);
                }

                if ($deleted_count == 0 && empty($errors)) {
                    $json['error'] = 'Не са избрани клиентски групи за изтриване!';
                }
            } else {
                // Единично изтриване
                $customer_group_id = (int)$this->requestGet('customer_group_id', 0);

                if ($customer_group_id && $this->canDeleteCustomerGroup($customer_group_id)) {
                    $customer_group_info = $this->model_customer_customer_group->getCustomerGroup($customer_group_id);
                    $this->model_customer_customer_group->deleteCustomerGroup($customer_group_id);
                    
                    if ($customer_group_info) {
                        $json['success'] = sprintf('Клиентската група "%s" беше успешно изтрита!', 
                            $customer_group_info['name']);
                    } else {
                        $json['success'] = 'Клиентската група беше успешно изтрита!';
                    }
                } else {
                    $json['error'] = 'Клиентската група не може да бъде изтрита, защото има свързани клиенти!';
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали клиентската група може да бъде изтрита
     */
    private function canDeleteCustomerGroup($customer_group_id) {
        // Проверка за свързани клиенти
        $this->load->model('customer/customer');
        $customers = $this->model_customer_customer->getTotalCustomersByCustomerGroupId($customer_group_id);

        if ($customers > 0) {
            return false;
        }

        // Проверка дали това е групата по подразбиране
        if ($customer_group_id == $this->getConfig('config_customer_group_id')) {
            return false;
        }

        return true;
    }
}
