/**
 * Voucher Management JavaScript
 * Функционалности за управление на подаръчни ваучери
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Инициализация на функционалностите
    initVoucherList();
    initVoucherForm();
    
});

/**
 * Инициализация на функционалностите за списъка с ваучери
 */
function initVoucherList() {
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="selected[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Individual checkbox change
    const individualCheckboxes = document.querySelectorAll('input[name="selected[]"]');
    individualCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
        });
    });

    // Status update functionality
    initStatusUpdate();
    
    // Send voucher functionality
    initSendVoucher();
}

/**
 * Инициализация на функционалностите за формата
 */
function initVoucherForm() {
    // Auto-generate voucher code
    const generateCodeBtn = document.getElementById('generate-code');
    if (generateCodeBtn) {
        generateCodeBtn.addEventListener('click', function() {
            generateVoucherCode();
        });
    }

    // Form validation
    const voucherForm = document.getElementById('voucher-form');
    if (voucherForm) {
        voucherForm.addEventListener('submit', function(e) {
            if (!validateVoucherForm()) {
                e.preventDefault();
            }
        });
    }

    // Amount formatting
    const amountInput = document.getElementById('amount');
    if (amountInput) {
        amountInput.addEventListener('blur', function() {
            formatAmountInput(this);
        });
    }
}

/**
 * Актуализира състоянието на "select all" checkbox
 */
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('select-all');
    const individualCheckboxes = document.querySelectorAll('input[name="selected[]"]');
    
    if (selectAllCheckbox && individualCheckboxes.length > 0) {
        const checkedCount = document.querySelectorAll('input[name="selected[]"]:checked').length;
        
        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === individualCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
}

/**
 * Инициализация на AJAX актуализиране на статус
 */
function initStatusUpdate() {
    const statusButtons = document.querySelectorAll('[data-action="status-update"]');
    statusButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const voucherId = this.dataset.voucherId;
            const currentStatus = this.dataset.currentStatus;
            const newStatus = currentStatus === '1' ? '0' : '1';
            
            updateVoucherStatus(voucherId, newStatus, this);
        });
    });
}

/**
 * Актуализира статуса на ваучер чрез AJAX
 */
function updateVoucherStatus(voucherId, status, buttonElement) {
    const formData = new FormData();
    formData.append('voucher_id', voucherId);
    formData.append('status', status);
    formData.append('user_token', getUserToken());

    fetch(getAdminUrl('sale/voucher/status_update'), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Актуализиране на UI
            const statusSpan = buttonElement.closest('tr').querySelector('.status-badge');
            if (statusSpan) {
                statusSpan.textContent = data.status_text;
                statusSpan.className = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full ' + data.status_class;
            }
            
            // Актуализиране на button data
            buttonElement.dataset.currentStatus = status;
            
            showNotification(data.success, 'success');
        } else {
            showNotification(data.error || 'Грешка при актуализиране на статуса', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Грешка при комуникация със сървъра', 'error');
    });
}

/**
 * Инициализация на изпращане на ваучери
 */
function initSendVoucher() {
    const sendButtons = document.querySelectorAll('[data-action="send-voucher"]');
    sendButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const voucherId = this.dataset.voucherId;
            const voucherCode = this.dataset.voucherCode;
            
            if (confirm(`Сигурни ли сте, че искате да изпратите ваучер ${voucherCode}?`)) {
                sendVoucher(voucherId);
            }
        });
    });
}

/**
 * Изпраща ваучер по email чрез AJAX
 */
function sendVoucher(voucherId) {
    const formData = new FormData();
    formData.append('voucher_id', voucherId);
    formData.append('user_token', getUserToken());

    fetch(getAdminUrl('sale/voucher/send'), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.success, 'success');
        } else {
            showNotification(data.error || 'Грешка при изпращане на ваучера', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Грешка при комуникация със сървъра', 'error');
    });
}

/**
 * Генерира случаен код за ваучер
 */
function generateVoucherCode() {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    
    for (let i = 0; i < 8; i++) {
        code += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.value = code;
    }
}

/**
 * Валидира формата за ваучер
 */
function validateVoucherForm() {
    let isValid = true;
    const errors = [];

    // Валидация на код
    const code = document.getElementById('code');
    if (code && (!code.value.trim() || code.value.trim().length < 3)) {
        errors.push('Кодът на ваучера трябва да бъде поне 3 символа');
        isValid = false;
    }

    // Валидация на сума
    const amount = document.getElementById('amount');
    if (amount && (!amount.value || parseFloat(amount.value) <= 0)) {
        errors.push('Сумата трябва да бъде по-голяма от 0');
        isValid = false;
    }

    // Валидация на имена
    const fromName = document.getElementById('from_name');
    if (fromName && !fromName.value.trim()) {
        errors.push('Името на подарителя е задължително');
        isValid = false;
    }

    const toName = document.getElementById('to_name');
    if (toName && !toName.value.trim()) {
        errors.push('Името на получателя е задължително');
        isValid = false;
    }

    // Валидация на email адреси
    const fromEmail = document.getElementById('from_email');
    if (fromEmail && (!fromEmail.value.trim() || !isValidEmail(fromEmail.value))) {
        errors.push('Невалиден email адрес на подарителя');
        isValid = false;
    }

    const toEmail = document.getElementById('to_email');
    if (toEmail && (!toEmail.value.trim() || !isValidEmail(toEmail.value))) {
        errors.push('Невалиден email адрес на получателя');
        isValid = false;
    }

    // Валидация на тема
    const theme = document.getElementById('voucher_theme_id');
    if (theme && !theme.value) {
        errors.push('Темата на ваучера е задължителна');
        isValid = false;
    }

    if (!isValid) {
        showNotification(errors.join('\n'), 'error');
    }

    return isValid;
}

/**
 * Форматира полето за сума
 */
function formatAmountInput(input) {
    const value = parseFloat(input.value);
    if (!isNaN(value)) {
        input.value = value.toFixed(2);
    }
}

/**
 * Проверява дали email адресът е валиден
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Показва нотификация
 */
function showNotification(message, type = 'info') {
    // Създаване на нотификация елемент
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <button class="ml-3 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                <i class="ri-close-line"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Автоматично премахване след 5 секунди
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Получава user token от скритото поле
 */
function getUserToken() {
    const tokenInput = document.querySelector('input[name="user_token"]');
    return tokenInput ? tokenInput.value : '';
}

/**
 * Генерира admin URL
 */
function getAdminUrl(route, params = '') {
    const baseUrl = window.location.origin + window.location.pathname;
    const separator = params ? '&' : '';
    return `${baseUrl}?route=${route}${separator}${params}`;
}
