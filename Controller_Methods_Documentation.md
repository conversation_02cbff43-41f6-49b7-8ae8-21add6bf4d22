# Документация на методите в Theme25\Controller

Този документ описва основните методи, налични в базовия контролер `Theme25\Controller`, и предоставя примери за тяхното използване. Целта е да улесни разработката и интеграцията с AI асистенти.

---

### `setTitle($title)`

*   **Описание:** Задава заглавието на страницата. Този метод е съкращение, което задава едновременно заглавието на документа (в `<title>` тага) и `heading_title` за шаблона.
*   **Пример:**

    **Преди:**
    ```php
    $this->document->setTitle($this->language->get('heading_title'));
    $data['heading_title'] = $this->language->get('heading_title');
    ```

    **След:**
    ```php
    $this->setTitle($this->language->get('heading_title'));
    ```

---

### `loadLanguage($route)`

*   **Описание:** Зарежда езиков файл. Това е директен заместител на стандартния метод, като целта е консистентност.
*   **Пример:**

    **Преди:**
    ```php
    $this->load->language('checkout/cart');
    ```

    **След:**
    ```php
    $this->loadLanguage('checkout/cart');
    ```

---

### `getLanguageText($key, $route = '')`

*   **Описание:** Взема текстов низ по ключ. Ако се налага, може да зареди и друг езиков файл в движение, спестявайки един ред код.
*   **Пример:**

    **Преди:**
    ```php
    $this->load->language('product/product');
    $data['text_price'] = $this->language->get('text_price');
    ```

    **След:**
    ```php
    $data['text_price'] = $this->getLanguageText('text_price', 'product/product');
    ```

---

### `getLanguageId()`

*   **Описание:** Връща ID на текущо избрания език, като взима предвид и настройките от втората база данни, ако е активна.
*   **Пример:**

    **Преди:**
    ```php
    $current_language_id = $this->config->get('config_language_id');
    ```

    **След:**
    ```php
    $current_language_id = $this->getLanguageId();
    ```

---

### `loadController($controller, $data = [])`

*   **Описание:** Зарежда и изпълнява друг контролер. Директен заместител за консистентност.
*   **Пример:**

    **Преди:**
    ```php
    $data['header'] = $this->load->controller('common/header');
    ```

    **След:**
    ```php
    $data['header'] = $this->loadController('common/header');
    ```

---

### `loadModel($route)`

*   **Описание:** Зарежда модел. Директен заместител на `$this->load->model()`. Основното предимство идва от `loadModelAs`.
*   **Пример:**

    **Преди:**
    ```php
    $this->load->model('catalog/product');
    $product_info = $this->model_catalog_product->getProduct(123);
    ```

    **След:**
    ```php
    $this->loadModel('catalog/product');
    $product_info = $this->model_catalog_product->getProduct(123);
    ```

---

### `loadModelAs($route, $alias)`

*   **Описание:** Зарежда модел и му присвоява лесен за запомняне псевдоним (alias), което прави кода много по-четим и лесен за поддръжка.
*   **Пример:**

    **Преди:**
    ```php
    $this->load->model('catalog/product');
    $product_info = $this->model_catalog_product->getProduct(123);
    ```

    **След:**
    ```php
    $this->loadModelAs('catalog/product', 'productModel');
    $product_info = $this->productModel->getProduct(123);
    ```

---

### `loadModelsAs($modelMap)`

*   **Описание:** Зарежда множество модели с техните псевдоними наведнъж, което значително съкращава повтарящ се код.
*   **Пример:**

    **Преди:**
    ```php
    $this->load->model('catalog/product');
    $this->load->model('catalog/category');
    // ... и т.н.
    ```

    **След:**
    ```php
    $this->loadModelsAs([
        'catalog/product' => 'productModel',
        'catalog/category' => 'categoryModel'
    ]);
    $product_info = $this->productModel->getProduct(123);
    $category_info = $this->categoryModel->getCategory(1);
    ```

---

### `loadView($route, $data = [])`

*   **Описание:** Рендира и връща съдържанието на шаблон (view). Директен заместител за консистентност.
*   **Пример:**

    **Преди:**
    ```php
    $this->load->view('common/home', $data);
    ```

    **След:**
    ```php
    $this->loadView('common/home', $data);
    ```

---

### `getConfig($key)`

*   **Описание:** Взема стойност от системната конфигурация, като проверява и втората база данни, ако е налична.
*   **Пример:**

    **Преди:**
    ```php
    $store_name = $this->config->get('config_name');
    ```

    **След:**
    ```php
    $store_name = $this->getConfig('config_name');
    ```

---

### `getUserToken()`

*   **Описание:** Връща `user_token` на логнатия администратор от сесията.
*   **Пример:**

    **Преди:**
    ```php
    $user_token = $this->session->data['user_token'];
    ```

    **След:**
    ```php
    $user_token = $this->getUserToken();
    ```

---

### `getAdminLink($route, $args = '', ...)`

*   **Описание:** Генерира URL към страница в административния панел, като автоматично добавя `user_token`.
*   **Пример:**

    **Преди:**
    ```php
    $data['cancel'] = $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true);
    ```

    **След:**
    ```php
    $data['cancel'] = $this->getAdminLink('catalog/product');
    ```

---

### `isPostRequest()`

*   **Описание:** Проверява дали текущата заявка е тип POST. Прави кода по-четим.
*   **Пример:**

    **Преди:**
    ```php
    if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
        // ...
    }
    ```

    **След:**
    ```php
    if ($this->isPostRequest()) {
        // ...
    }
    ```

---

### `requestPost($key = null, $default = null)`

*   **Описание:** По-безопасен и кратък начин за достъп до `$_POST` данни.
*   **Пример:**

    **Преди:**
    ```php
    if (isset($this->request->post['product_name'])) {
        $product_name = $this->request->post['product_name'];
    } else {
        $product_name = '';
    }
    ```

    **След:**
    ```php
    $product_name = $this->requestPost('product_name', '');
    ```

---

### `requestGet($key = null, $default = null)`

*   **Описание:** По-безопасен и кратък начин за достъп до `$_GET` данни.
*   **Пример:**

    **Преди:**
    ```php
    if (isset($this->request->get['product_id'])) {
        $product_id = $this->request->get['product_id'];
    } else {
        $product_id = 0;
    }
    ```

    **След:**
    ```php
    $product_id = $this->requestGet('product_id', 0);
    ```

---

### `setData($keyOrArray, $value = null)`

*   **Описание:** Улеснява добавянето на данни към масива `$data`, който се подава на шаблона. Позволява верижно извикване.
*   **Пример:**

    **Преди:**
    ```php
    $data['product_name'] = 'iPhone';
    $data['price'] = 999;
    $data['quantity'] = 10;
    ```

    **След (вариант 1):**
    ```php
    $this->setData('product_name', 'iPhone');
    $this->setData('price', 999);
    $this->setData('quantity', 10);
    ```

    **След (вариант 2 - верижно):**
    ```php
    $this->setData('product_name', 'iPhone')
         ->setData('price', 999)
         ->setData('quantity', 10);
    ```

    **След (вариант 3 - с масив):**
    ```php
    $this->setData([
        'product_name' => 'iPhone',
        'price' => 999,
        'quantity' => 10
    ]);
    ```

---

### `getData($key = null)`

*   **Описание:** Взема данни от общия масив (`$this->data`). Ако не е подаден ключ, връща целия масив.
*   **Пример:**
    ```php
    $product_name = $this->getData('product_name');
    $all_data = $this->getData();
    ```

---

### `renderTemplate($route, $additionalData = [])`

*   **Описание:** Рендира шаблон, като автоматично му подава всички данни от `$this->data`, както и допълнителни данни, ако са зададени.
*   **Пример:**
    ```php
    // Данните от $this->data и ['extra' => 'info'] ще са налични в шаблона
    $html = $this->renderTemplate('product/special', ['extra' => 'info']);
    ```

---

### `renderTemplateWithDataAndOutput($route, $additionalData = [])`

*   **Описание:** Комбинира зареждането на данни, рендирането на шаблон и изпращането на отговора в един метод.
*   **Пример:**

    **Преди:**
    ```php
    $data['products'] = $products;
    // ... добавяне на header, footer, column_left и т.н.
    $data['header'] = $this->load->controller('common/header');
    $data['footer'] = $this->load->controller('common/footer');
    $data['column_left'] = $this->load->controller('common/column_left');

    $this->response->setOutput($this->load->view('product/category', $data));
    ```

    **След:**
    ```php
    $this->setData('products', $products);
    // header, footer и др. се зареждат автоматично от базовия шаблон, ако не са дефинирани
    $this->renderTemplateWithDataAndOutput('product/category');
    ```

---

### `dbQuery($query)`

*   **Описание:** Изпълнява SQL заявка към базата данни. Директен заместител за консистентност и бъдещи разширения (напр. логване).
*   **Пример:**

    **Преди:**
    ```php
    $result = $this->db->query("SELECT * FROM " . DB_PREFIX . "product");
    ```

    **След:**
    ```php
    $result = $this->dbQuery("SELECT * FROM " . DB_PREFIX . "product");
    ```

---

### `dbEscape($value)`

*   **Описание:** Екранира специални символи в стринг за безопасно използване в SQL заявка. Директен заместител за консистентност.
*   **Пример:**

    **Преди:**
    ```php
    $name = "Kate's Laptop";
    $sql = "SELECT * FROM " . DB_PREFIX . "product_description WHERE name = '" . $this->db->escape($name) . "'";
    ```

    **След:**
    ```php
    $name = "Kate's Laptop";
    $sql = "SELECT * FROM " . DB_PREFIX . "product_description WHERE name = '" . $this->dbEscape($name) . "'";
    ```
