<?php

namespace Theme25\Backend\Controller\Catalog\Manufacturer;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'manufacturer-form.js',
            'image-manager.js'
        ], 'footer');
    }

    public function execute() {
        $manufacturer_id = (int)$this->requestGet('manufacturer_id');
        
        if (!$manufacturer_id) {
            $this->response->redirect($this->getAdminLink('catalog/manufacturer'));
            return;
        }

        $this->setTitle('Редактиране на производител');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareManufacturerForm($manufacturer_id);

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/manufacturer_form');
    }

    /**
     * Подготвя формата за редактиране на производител
     */
    private function prepareManufacturerForm($manufacturer_id) {
        $this->loadModelsAs([
            'catalog/manufacturer' => 'manufacturerModel',
            'localisation/language' => 'languageModel',
            'setting/store' => 'storeModel',
            'design/seo_url' => 'seoUrlModel'
        ]);

        // Получаване на данните за производителя
        $manufacturer_info = $this->manufacturerModel->getManufacturer($manufacturer_id);
        
        if (!$manufacturer_info) {
            $this->response->redirect($this->getAdminLink('catalog/manufacturer'));
            return;
        }

        // Подготвяне на данните
        $this->prepareBasicData($manufacturer_info)
             ->prepareLanguageData()
             ->prepareStoreData($manufacturer_id)
             ->prepareSeoUrls($manufacturer_id)
             ->prepareUrls($manufacturer_id);

        return $this;
    }

    /**
     * Подготвя основните данни за формата
     */
    private function prepareBasicData($manufacturer_info) {
        $this->setData([
            'manufacturer_id' => $manufacturer_info['manufacturer_id'],
            'name' => $manufacturer_info['name'],
            'image' => $manufacturer_info['image'],
            'sort_order' => $manufacturer_info['sort_order']
        ]);

        // Подготвяне на изображението
        $this->prepareImageData();

        return $this;
    }

    /**
     * Подготвя данните за езиците
     */
    private function prepareLanguageData() {
        $languages = $this->languageModel->getLanguages();
        
        $this->setData([
            'languages' => $languages,
            'language_id' => $this->getLanguageId()
        ]);

        return $this;
    }

    /**
     * Подготвя данните за магазините
     */
    private function prepareStoreData($manufacturer_id) {
        $stores = $this->storeModel->getStores();
        
        // Добавяне на основния магазин
        $store_data = [];
        $store_data[] = [
            'store_id' => 0,
            'name' => $this->getLanguageText('text_default')
        ];
        
        foreach ($stores as $store) {
            $store_data[] = [
                'store_id' => $store['store_id'],
                'name' => $store['name']
            ];
        }

        // Получаване на магазините за този производител
        $manufacturer_stores = $this->manufacturerModel->getManufacturerStores($manufacturer_id);
        
        $this->setData([
            'stores' => $store_data,
            'manufacturer_store' => $manufacturer_stores
        ]);

        return $this;
    }

    /**
     * Подготвя SEO URL данните
     */
    private function prepareSeoUrls($manufacturer_id) {
        $manufacturer_seo_url = [];
        
        foreach ($this->data['stores'] as $store) {
            $manufacturer_seo_url[$store['store_id']] = [];
            
            foreach ($this->data['languages'] as $language) {
                $seo_url_info = $this->seoUrlModel->getSeoUrl(
                    'manufacturer_id=' . $manufacturer_id,
                    $store['store_id'],
                    $language['language_id']
                );
                
                $manufacturer_seo_url[$store['store_id']][$language['language_id']] = 
                    $seo_url_info ? $seo_url_info['keyword'] : '';
            }
        }

        $this->setData([
            'manufacturer_seo_url' => $manufacturer_seo_url
        ]);

        return $this;
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls($manufacturer_id) {
        $this->setData([
            'action' => $this->getAdminLink('catalog/manufacturer/save', 'manufacturer_id=' . $manufacturer_id),
            'cancel' => $this->getAdminLink('catalog/manufacturer'),
            'image_manager' => $this->getAdminLink('common/filemanager'),
            'placeholder' => ThemeData()->getImageServerUrl() . 'placeholder.png'
        ]);

        return $this;
    }

    /**
     * Подготвя данните за изображения
     */
    private function prepareImageData() {
        $this->loadModelsAs([
            'tool/image' => 'imageModel'
        ]);

        $image_thumb = '';
        
        if ($this->data['image'] && is_file(DIR_IMAGE . $this->data['image'])) {
            $image_thumb = $this->imageModel->resize($this->data['image'], 100, 100);
        } else {
            $image_thumb = $this->imageModel->resize('no_image.png', 100, 100);
        }

        $this->setData([
            'thumb' => $image_thumb
        ]);

        return $this;
    }

    /**
     * Подготвя данните за мултиезичност
     */
    private function prepareMultiLanguageData($manufacturer_id) {
        // За manufacturer в OpenCart няма мултиезични полета освен SEO URL
        // Но можем да подготвим структурата за бъдещи разширения
        
        $manufacturer_description = [];
        
        foreach ($this->data['languages'] as $language) {
            $manufacturer_description[$language['language_id']] = [
                'meta_title' => '',
                'meta_description' => '',
                'meta_keyword' => '',
                'description' => ''
            ];
        }

        $this->setData([
            'manufacturer_description' => $manufacturer_description
        ]);

        return $this;
    }

    /**
     * Подготвя данните за grid layout
     */
    private function prepareGridLayout() {
        $this->setData([
            'grid_columns' => 2, // Две колони за по-добра организация
            'form_groups' => [
                'basic' => [
                    'title' => 'Основни данни',
                    'fields' => ['name', 'image', 'sort_order']
                ],
                'stores' => [
                    'title' => 'Магазини',
                    'fields' => ['manufacturer_store']
                ],
                'seo' => [
                    'title' => 'SEO настройки',
                    'fields' => ['manufacturer_seo_url']
                ]
            ]
        ]);

        return $this;
    }

    /**
     * Подготвя данните за валидация
     */
    private function prepareValidationData() {
        $this->setData([
            'error_warning' => '',
            'error_name' => '',
            'error_keyword' => []
        ]);

        return $this;
    }
}
