<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Products extends \Theme25\ControllerSubMethods {

     /**
     * Унифицирано търсене в продукти с кеширане
     */
    public function search($query, $page = 1, $limit = 20, $extendedSearch = 0) {
        // Проверяваме кеша първо
        $cacheKey = $this->modelSearch->generateCacheKey('products_search', $query . '_ext_' . $extendedSearch . '_p' . $page . '_l' . $limit, $limit);
        $cachedResults = $this->modelSearch->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        $offset = ($page - 1) * $limit;

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, $limit, $extendedSearch, $offset);

        // Получаваме общия брой резултати
        $total = $this->getTotalCount($query, $extendedSearch);

        $searchResults = [
            'results' => $results,
            'total' => $total
        ];

        // Кешираме резултатите
        $this->modelSearch->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }

    
    /**
     * Рендира визуализацията на списъка с продукти
     */
    public function renderItems($results, $withPagination = false, $query = '', $page = 1, $limit = 20, $total = 0, $extendedSearch = 0) {

       

         // Подготвяме данните за продуктите
        $products = [];
        foreach ($results as $product) {
            // Проверка за специална цена
            $special_data = $this->getSpecialPriceData($product);

            $products[] = [
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'] ?? '',
                'sku' => $product['sku'] ?? '',
                'price' => $this->formatCurrency($special_data['price'], $this->getConfig('config_currency')),
                'old_price' => $special_data['old_price'],
                'discount_percent' => $special_data['discount_percent'],
                'special' => $special_data['special'],
                // 'status' => $product['status'] == 1 ? 'Активен' : 'Неактивен',
                'status' => $product['status'],
                // 'status_class' => $product['status'] == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
                'status_class' => $product['status'] == 'Активен' ? 'bg-green-500' : 'bg-yellow-500',
                'image' => $product['image'] ?? '',
                'quantity' => $product['quantity'] ?? 0,
                // 'view' => $this->getAdminLink('catalog/product/view', '&product_id=' . $product['product_id']),
                'category' => $this->getCategoryName($product['product_id']),
                'edit' => $this->getAdminLink('catalog/product/edit', '&product_id=' . $product['product_id'])
            ];
        }

        // Подготвяме данните за шаблона
        $data = [
            'products' => $products,
            'with_pagination' => $withPagination,
            'query' => $query,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ];

        // Пагинация ако е необходима
        if ($withPagination && $total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/products', '&query=' . urlencode($query) . '&extended_search=' . $extendedSearch . '&page={page}');
            $pagination->setProductText('продукта');
            $data['pagination'] = $pagination->render();
        }
        // Рендираме шаблона и връщаме HTML
        return $this->renderPartTemplate('common/global_search_products', $data);
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $extendedSearch = 0, $offset = 0) {
        try {
            if ($extendedSearch) {
                // Разширено търсене с транслитерация и fuzzy search
                $words = $this->modelSearch->prepareSearchWords($query);
                if (empty($words)) {
                    return [];
                }

                // Търсим точни съвпадения първо
                $exactResults = $this->searchExactMatches($words, $limit, 0); // Винаги започваме от 0 за точните

                // Ако имаме достатъчно точни резултати, прилагаме offset и limit
                if (count($exactResults) >= ($offset + $limit)) {
                    return array_slice($exactResults, $offset, $limit);
                }

                // Ако точните резултати са по-малко от нужните, добавяме частични
                $exactCount = count($exactResults);
                $remainingNeeded = ($offset + $limit) - $exactCount;
                $partialOffset = max(0, $offset - $exactCount);

                $partialResults = $this->searchPartialMatches($words, $remainingNeeded, $exactResults, $partialOffset);

                // Обединяваме резултатите
                $allResults = array_merge($exactResults, $partialResults);

                // Прилагаме offset и limit върху комбинираните резултати
                return array_slice($allResults, $offset, $limit);
            } else {
                // Обикновено търсене - само точни съвпадения
                return $this->searchExactMatchesOnly($query, $limit, $offset);
            }

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Търси точни съвпадения
     */
    private function searchExactMatches($words, $limit, $offset = 0) {
        $language_id = $this->getLanguageId();
        $results = [];

        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));

            $sql = "
                SELECT DISTINCT
                    p.product_id,
                    pd.name,
                    p.model,
                    p.sku,
                    p.price,
                    p.status,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                    AND p.status = '1'
                    AND (
                        ".
                        (is_numeric($word) ? "p.product_id = '" . (int)$word . "' OR " : "")
                        ."
                        LOWER(pd.name) = '{$escapedWord}'
                        OR LOWER(p.model) = '{$escapedWord}'
                        OR LOWER(p.sku) = '{$escapedWord}'
                    )
                ORDER BY
                    pd.name ASC
                LIMIT " . (int)$offset . ", " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $productId = $row['product_id'];
                if (!isset($results[$productId])) {
                    $results[$productId] = [
                        'product_id' => $row['product_id'],
                        'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                        'model' => $row['model'] ?? '',
                        'sku' => $row['sku'] ?? '',
                        'price' => number_format((float)$row['price'], 2, '.', ''),
                        'status' => $row['status'],
                        'relevance' => 100 // Най-висока релевантност за точни съвпадения
                    ];
                }
            }

            if (count($results) >= $limit) {
                break;
            }
        }

        return array_values($results);
    }



    /**
     * Търси частични съвпадения
     */
    private function searchPartialMatches($words, $limit, $excludeResults = [], $offset = 0) {
        $language_id = $this->getLanguageId();
        $results = [];
        $excludeIds = array_column($excludeResults, 'product_id');

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(pd.name) LIKE '%{$escapedWord}%'",
                "LOWER(p.model) LIKE '%{$escapedWord}%'",
                "LOWER(p.sku) LIKE '%{$escapedWord}%'"
            ];

            if( is_numeric($word) ){
                $wordConditions[] = "p.product_id = '" . (int)$word . "'";
            }

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(pd.name) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(p.model) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(p.sku) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените продукти
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND p.product_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                p.product_id,
                pd.name,
                p.model,
                p.sku,
                p.price,
                p.status,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "product p
            LEFT JOIN
                " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE
                pd.language_id = '" . (int)$language_id . "'
                AND ({$whereCondition})
            ORDER BY
                pd.name ASC
            LIMIT " . (int)$offset . ", " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $results[] = [
                'product_id' => $row['product_id'],
                'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                'model' => $row['model'] ?? '',
                'sku' => $row['sku'] ?? '',
                'price' => number_format((float)$row['price'], 2, '.', ''),
                'status' => $row['status'],
                'relevance' => 50 // По-ниска релевантност за частични съвпадения
            ];
        }

        return $results;
    }

    /**
     * Основна логика за търсене
     */
    private function performSearch($query, $limit = 5, $offset = 0) {
        $results = [];

        try {
            // Debug: Логираме входните параметри
            $this->logDev("Products::performSearch - Query: '$query', Limit: $limit, Offset: $offset");

            // Подготвяме търсещите условия
            $searchConditions = $this->buildSearchConditions($query);
            $this->logDev("Products::performSearch - Search conditions: $searchConditions");

            $language_id = $this->getLanguageId();
            $this->logDev("Products::performSearch - Language ID: $language_id");

            $sql = "
                SELECT
                    p.product_id,
                    pd.name,
                    p.model,
                    p.sku,
                    p.price,
                    p.status,
                    p.image
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                    AND ({$searchConditions})
                ORDER BY
                    pd.name ASC
                LIMIT " . (int)$limit;

            if ($offset > 0) {
                $sql .= " OFFSET " . (int)$offset;
            }

            // Debug: Логираме SQL заявката
            $this->logDev("Products::performSearch - SQL: " . $sql);

            $query_result = $this->db->query($sql);

            // Debug: Логираме резултата от заявката
            $this->logDev("Products::performSearch - Query result rows count: " . count($query_result->rows));

            if (empty($query_result->rows)) {
                $this->logDev("Products::performSearch - No rows returned from database");

                // Тестваме дали има продукти изобщо
                $test_sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "product p LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) WHERE pd.language_id = '" . (int)$language_id . "'";
                $test_result = $this->db->query($test_sql);
                $this->logDev("Products::performSearch - Total products in DB: " . $test_result->row['total']);
            }

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'product_id' => $row['product_id'],
                    'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                    'model' => $row['model'] ?? '',
                    'sku' => $row['sku'] ?? '',
                    'price' => number_format((float)$row['price'], 2, '.', ''),
                    'status' => $row['status'],
                    'image' => $row['image'] ?? ''
                ];
            }

        } catch (Exception $e) {
            // Логиране на грешката
            $this->logDev('Products::performSearch - Exception: ' . $e->getMessage());
            $this->logDev('Products::performSearch - Stack trace: ' . $e->getTraceAsString());
        }

        return $results;
    }

    /**
     * Получаване на общия брой резултати
     * Използва същата логика като performOptimizedSearch() за консистентност
     */
    private function getTotalCount($query, $extendedSearch = 0) {
        try {
            if ($extendedSearch) {
                // Разширено търсене с транслитерация и fuzzy search
                $words = $this->modelSearch->prepareSearchWords($query);
                if (empty($words)) {
                    return 0;
                }

                // Броим точните съвпадения
                $exactCount = $this->getExactMatchesCount($words);

                // Броим частичните съвпадения (без дублиране с точните)
                $partialCount = $this->getPartialMatchesCount($words, $exactCount > 0);

                return $exactCount + $partialCount;
            } else {
                // Обикновено търсене - само точни съвпадения
                return $this->getExactMatchesOnlyCount($query);
            }

        } catch (Exception $e) {
            $this->logDev('Products::getTotalCount - Exception: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Брои точните съвпадения за разширено търсене
     */
    private function getExactMatchesCount($words) {
        $language_id = $this->getLanguageId();

        // Строим условията за точни съвпадения
        $exactConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));

            $wordConditions = [
                "LOWER(pd.name) = '{$escapedWord}'",
                "LOWER(p.model) = '{$escapedWord}'",
                "LOWER(p.sku) = '{$escapedWord}'"
            ];

            if (is_numeric($word)) {
                $wordConditions[] = "p.product_id = '" . (int)$word . "'";
            }

            $exactConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        if (empty($exactConditions)) {
            return 0;
        }

        $whereCondition = implode(' AND ', $exactConditions);

        $sql = "
            SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE pd.language_id = '" . (int)$language_id . "'
            AND ({$whereCondition})";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Брои частичните съвпадения за разширено търсене
     */
    private function getPartialMatchesCount($words, $hasExactMatches = false) {
        $language_id = $this->getLanguageId();

        // Строим условията за частични съвпадения
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(pd.name) LIKE '%{$escapedWord}%'",
                "LOWER(p.model) LIKE '%{$escapedWord}%'",
                "LOWER(p.sku) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(pd.name) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(p.model) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(p.sku) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Ако имаме точни съвпадения, изключваме ги от частичните
        $excludeExact = '';
        if ($hasExactMatches) {
            $exactConditions = [];
            foreach ($words as $word) {
                $escapedWord = $this->db->escape(mb_strtolower($word));

                $wordConditions = [
                    "LOWER(pd.name) = '{$escapedWord}'",
                    "LOWER(p.model) = '{$escapedWord}'",
                    "LOWER(p.sku) = '{$escapedWord}'"
                ];

                if (is_numeric($word)) {
                    $wordConditions[] = "p.product_id = '" . (int)$word . "'";
                }

                $exactConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
            }

            if (!empty($exactConditions)) {
                $excludeExact = ' AND NOT (' . implode(' AND ', $exactConditions) . ')';
            }
        }

        $sql = "
            SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE pd.language_id = '" . (int)$language_id . "'
            AND ({$whereCondition})
            {$excludeExact}";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Брои резултатите за обикновено търсене (само точни съвпадения)
     */
    private function getExactMatchesOnlyCount($query) {
        $language_id = $this->getLanguageId();
        $escapedQuery = $this->db->escape($query);

        $sql = "
            SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE pd.language_id = '" . (int)$language_id . "'
            AND (
                p.product_id LIKE '" . $escapedQuery . "%'
                OR pd.name LIKE '" . $escapedQuery . "%'
                OR p.model LIKE '" . $escapedQuery . "%'
                OR p.sku LIKE '" . $escapedQuery . "%'
            )";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Изграждане на търсещи условия с интелигентно търсене
     */
    private function buildSearchConditions($query) {
        $conditions = [];

        // Почистваме и разделяме заявката на думи
        $words = $this->modelSearch->prepareSearchWords($query);

        if (empty($words)) {
            return "1=0"; // Няма валидни думи за търсене
        }

        // За всяка дума създаваме условия
        foreach ($words as $word) {
            $wordConditions = [];

            // Оригиналната дума
            $escapedWord = $this->db->escape(mb_strtolower($word));

            // Транслитерирана версия
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            // Fuzzy варианти (за думи над 4 символа)
            $fuzzyVariants = $this->modelSearch->generateFuzzyVariants($word);

            // Търсене в различни полета
            $fields = ['pd.name', 'pd.description', 'p.model', 'p.sku'];

            foreach ($fields as $field) {
                $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedWord}%'";

                if ($transliteratedWord !== $word) {
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedTranslit}%'";
                }

                // Добавяме fuzzy варианти (ограничаваме до първите 3 за производителност)
                $limitedFuzzy = array_slice($fuzzyVariants, 0, 3);
                foreach ($limitedFuzzy as $variant) {
                    $escapedVariant = $this->db->escape(mb_strtolower($variant));
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedVariant}%'";
                }
            }

            // Търсене в цена и product_id (само за числови стойности)
            if (is_numeric($word)) {
                $wordConditions[] = "p.price LIKE '%{$escapedWord}%'";
                $wordConditions[] = "p.product_id = '" . (int)$word . "'"; // Точно съвпадение за product_id
            }

            // Обединяваме условията за тази дума с OR
            $wordCondition = '(' . implode(' OR ', $wordConditions) . ')';
            $conditions[] = $wordCondition;
        }

        // Обединяваме всички думи с AND (всички думи трябва да се намират)
        $finalCondition = implode(' AND ', $conditions);

        return $finalCondition;
    }

    /**
     * Търсене само на точни съвпадения (за обикновено търсене)
     */
    private function searchExactMatchesOnly($query, $limit = 5, $offset = 0) {
        try {
            $language_id = $this->getLanguageId();
            $escapedQuery = $this->db->escape($query);

            $sql = "
                SELECT
                    p.product_id,
                    pd.name,
                    p.model,
                    p.sku,
                    p.price,
                    p.status,
                    p.image
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                    AND (
                        p.product_id LIKE '" . $escapedQuery . "%'
                        OR pd.name LIKE '" . $escapedQuery . "%'
                        OR p.model LIKE '" . $escapedQuery . "%'
                        OR p.sku LIKE '" . $escapedQuery . "%'
                    )
                ORDER BY
                    CASE
                        WHEN p.product_id = '" . $escapedQuery . "' THEN 0
                        WHEN pd.name = '" . $escapedQuery . "' THEN 1
                        WHEN p.model = '" . $escapedQuery . "' THEN 2
                        WHEN p.sku = '" . $escapedQuery . "' THEN 4
                        WHEN p.product_id LIKE '" . $escapedQuery . "%' THEN 3
                        WHEN pd.name LIKE '" . $escapedQuery . "%' THEN 4
                        WHEN p.model LIKE '" . $escapedQuery . "%' THEN 5
                        WHEN p.sku LIKE '" . $escapedQuery . "%' THEN 6
                        ELSE 7
                    END,
                    pd.name ASC
                LIMIT " . (int)$offset . ", " . (int)$limit;

            $result = $this->db->query($sql);
            $products = [];

            foreach ($result->rows as $row) {
                $products[] = [
                    'product_id' => $row['product_id'],
                    'name' => $row['name'],
                    'model' => $row['model'],
                    'sku' => $row['sku'],
                    'price' => number_format((float)$row['price'], 2, '.', ''),
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'image' => $row['image'],
                    'edit_url' => 'index.php?route=catalog/product/edit&product_id=' . $row['product_id'] . '&user_token=' . $this->session->data['user_token']
                ];
            }

            return $products;

        } catch (Exception $e) {
            $this->logDev('Products::searchExactMatchesOnly - Exception: ' . $e->getMessage());
            return [];
        }
    }

    /**
	 * Получава името на категорията на продукта
	 *
	 * @param int $product_id ID на продукта
	 * @return string Име на категорията
	 */
	private function getCategoryName($product_id) {
        $this->loadModelsAs([
			'catalog/product' => 'products',
			'catalog/category' => 'categories'
		]);

		$product_categories = $this->products->getProductCategories($product_id);

		if (!empty($product_categories)) {
			$category_id = reset($product_categories);
			$category_info = $this->categories->getCategory($category_id);

			if ($category_info) {
				return $category_info['name'];
			}
		}

		return 'Без категория';
	}

    /**
     * Получава данните за специалната цена на продукт
     */
    private function getSpecialPriceData($product) {
        $product_id = $product['product_id'];
        $original_price = (float)$product['price'];

        // Проверяваме за активна специална цена
        $sql = "SELECT price FROM " . DB_PREFIX . "product_special
                WHERE product_id = '" . (int)$product_id . "'
                AND customer_group_id = '" . (int)$this->config->get('config_customer_group_id') . "'
                AND ((date_start = '0000-00-00' OR date_start <= NOW())
                AND (date_end = '0000-00-00' OR date_end >= NOW()))
                ORDER BY priority ASC, price ASC LIMIT 1";

        $special_result = $this->db->query($sql);

        if ($special_result->num_rows && (float)$special_result->row['price'] < $original_price) {
            $special_price = (float)$special_result->row['price'];
            $discount_percent = round((($original_price - $special_price) / $original_price) * 100);

            return [
                'special' => true,
                'price' => $special_price,
                'old_price' => $this->formatCurrency($original_price, $this->getConfig('config_currency')),
                'discount_percent' => $discount_percent
            ];
        }

        return [
            'special' => false,
            'price' => $original_price,
            'old_price' => null,
            'discount_percent' => 0
        ];
    }
}
