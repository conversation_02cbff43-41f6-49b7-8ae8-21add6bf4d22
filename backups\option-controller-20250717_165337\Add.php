<?php

namespace Theme25\Backend\Controller\Catalog\Option;

class Add extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'option-form.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Добавяне на опция');
        
        // Инициализиране на данните
        $this->initAdminData();
        
        $this->prepareOptionForm();
        
        $this->renderTemplateWithDataAndOutput('catalog/option_form');
    }
    
    /**
     * Подготвя формата за добавяне на опция
     */
    private function prepareOptionForm() {
        $this->loadModelsAs([
            'localisation/language' => 'languageModel'
        ]);

        // Подготвяне на основните данни
        $this->prepareBasicData()
             ->prepareLanguageData()
             ->prepareOptionTypes()
             ->prepareFormUrls();

        return $this;
    }

    /**
     * Подготвя основните данни за формата
     */
    private function prepareBasicData() {
        $this->setData([
            'option_id' => 0,
            'sort_order' => 0,
            'type' => 'select'
        ]);

        return $this;
    }

    /**
     * Подготвя данните за езиците
     */
    private function prepareLanguageData() {
        $languages = $this->languages;
        $this->setData('languages', $languages);

        // Подготвяне на празни описания за всички езици
        $option_description = [];
        foreach ($languages as $language) {
            $option_description[$language['language_id']] = [
                'name' => ''
            ];
        }
        
        $this->setData('option_description', $option_description);

        return $this;
    }

    /**
     * Подготвя типовете опции
     */
    private function prepareOptionTypes() {
        $option_types = [
            'select' => 'Select',
            'radio' => 'Radio',
            'checkbox' => 'Checkbox',
            'text' => 'Text',
            'textarea' => 'Textarea',
            'file' => 'File',
            'date' => 'Date',
            'time' => 'Time',
            'datetime' => 'Date & Time'
        ];
        
        $this->setData('option_types', $option_types);

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls() {
        $this->setData([
            'action' => $this->getAdminLink('catalog/option/save'),
            'cancel' => $this->getAdminLink('catalog/option'),
            'back_url' => $this->getAdminLink('catalog/option')
        ]);

        return $this;
    }

    /**
     * Валидира данните от формата
     */
    public function validateForm($data) {
        $errors = [];

        // Проверка за тип опция
        if (empty($data['type'])) {
            $errors['type'] = 'Моля, изберете тип опция';
        }

        // Проверка за описания на опцията
        if (empty($data['option_description']) || !is_array($data['option_description'])) {
            $errors['option_description'] = 'Липсват описания на опцията';
        } else {
            foreach ($data['option_description'] as $language_id => $description) {
                if (empty($description['name']) || strlen(trim($description['name'])) < 1) {
                    $errors['name'][$language_id] = 'Името на опцията е задължително';
                } elseif (strlen($description['name']) > 128) {
                    $errors['name'][$language_id] = 'Името на опцията не може да бъде по-дълго от 128 символа';
                }
            }
        }

        // Проверка за sort_order
        if (isset($data['sort_order']) && !is_numeric($data['sort_order'])) {
            $errors['sort_order'] = 'Подредбата трябва да бъде число';
        }

        // Проверка за стойности на опцията (за select, radio, checkbox)
        if (in_array($data['type'], ['select', 'radio', 'checkbox'])) {
            if (empty($data['option_value']) || !is_array($data['option_value'])) {
                $errors['option_value'] = 'Опциите от тип ' . $data['type'] . ' трябва да имат поне една стойност';
            } else {
                foreach ($data['option_value'] as $key => $option_value) {
                    if (empty($option_value['option_value_description']) || !is_array($option_value['option_value_description'])) {
                        $errors['option_value'][$key] = 'Липсват описания на стойността';
                        continue;
                    }
                    
                    $has_valid_name = false;
                    foreach ($option_value['option_value_description'] as $language_id => $description) {
                        if (!empty($description['name']) && strlen(trim($description['name'])) >= 1) {
                            $has_valid_name = true;
                            break;
                        }
                    }
                    
                    if (!$has_valid_name) {
                        $errors['option_value'][$key] = 'Стойността трябва да има име поне за един език';
                    }
                }
            }
        }

        return $errors;
    }

    /**
     * Зареждане на модели с псевдоними
     */
    private function loadModelsAs($models) {
        foreach ($models as $model => $alias) {
            $this->load->model($model);
            $this->{$alias} = $this->{'model_' . str_replace('/', '_', $model)};
        }
    }
}
