<?php

namespace Theme25\Backend\Controller\Catalog\Product;

class Prepairform extends \Theme25\ControllerSubMethods {

    public function execute() {

        $this->loadLanguage('catalog/product');

        $this->initAdminData();

        // Зареждане на модели
        $this->loadModelsAs([
            'catalog/attribute'       => 'attributeModel',
            'catalog/product'       => 'productModel',
            'catalog/category'      => 'categoryModel',
            'catalog/manufacturer'  => 'manufacturerModel',
            'catalog/option'        => 'optionModel',
            'localisation/language' => 'languageModel',
            'setting/store'         => 'storeModel',
            'tool/image'            => 'imageModel' // За placeholder изображение
        ]);

        $product_id = $this->requestGet('product_id', 0);
        $product_info = [];

        if ($product_id && $this->request->server['REQUEST_METHOD'] != 'POST') {
            $product_info = $this->productModel->getProduct($product_id);
        }

        if ($product_id) {
            $this->setTitle('Редактиране на продукт');
            $this->setData('action', $this->getAdminLink('catalog/product/edit', 'product_id=' . $product_id, true));
        }
        else {
            $this->setTitle('Добавяне на продукт');
            $this->setData('action', $this->getAdminLink('catalog/product/add', '', true));
        }

        $this->setData('site_url', HTTPS_CATALOG);
        
        // Данни за продукта
        $this->setData('product_id', $product_id);
        $this->setBackUrl();
        
        // Задаване на пътя до контролера за използване в JavaScript
        $this->setData('catalog_controller', $this->getAdminLink('catalog/product/edit', '', true));

        

        // Определяне на активния език (първия наличен език или текущия език на администрацията)
        $active_language_id = $this->getConfig('config_language_id');
        $active_language_id_sdb = $this->getConfigFromSecondDB('config_language_id');



        if (!empty($active_language_id_sdb)) {
            $active_language_id = $active_language_id_sdb;
        }

        // Езици
        $languages = $this->languageModel->getLanguages();
        
        $_languages = [];

        foreach ($languages as $language) {
            $_languages[] = [
                'language_id' => $language['language_id'],
                'code' => $language['code'],
                'name' => $language['name'],
                'css' => $language['code'] == 'bg-bg' ? ' bg-languagebg' : ' bg-languageen'
            ];
        }

        $languages = $_languages;

        // Проверка дали текущият език съществува в списъка с езици
        $language_exists = false;
        foreach ($languages as $language) {
            if ($language['language_id'] == $active_language_id) {
                $language_exists = true;
                break;
            }
        }

        $this->setData('languages', $languages);

        // Ако текущият език не съществува, използваме първия наличен език
        if (!$language_exists && !empty($languages)) {
            $active_language_id = $languages[0]['language_id'];
        }

        $this->setData('active_language_id', $active_language_id);

        // Стойности по подразбиране за полетата
        $default_values = [
            'model'             => '',
            'sku'               => '',
            'upc'               => '',
            'ean'               => '',
            'jan'               => '',
            'isbn'              => '',
            'mpn'               => '',
            'location'          => '',
            'price'             => '0.00',
            'tax_class_id'      => '0',
            'quantity'          => 1,
            'minimum'           => 1,
            'subtract'          => 1,
            'stock_status_id'   => $this->getConfig('config_stock_status_id'),
            'shipping'          => 1,
            'date_available'    => date('Y-m-d'),
            'length'            => '0.00',
            'width'             => '0.00',
            'height'            => '0.00',
            'length_class_id'   => $this->getConfig('config_length_class_id'),
            'weight'            => '0.00',
            'weight_class_id'   => $this->getConfig('config_weight_class_id'),
            'status'            => 1, // Правилна стойност за статус (1 = активен, 0 = неактивен)
            'sort_order'        => 1,
            'manufacturer_id'   => 0,
            'image'             => '',
            'main_category_id'  => 0
        ];
        
        // Обединяване на стойностите по подразбиране с данните от продукта (ако има такъв)
        $product_data = !empty($product_info) ? array_merge($default_values, $product_info) : $default_values;

        // Полета, които трябва да се форматират като float с 2 знака след десетичната запетая
        $float_fields = ['price', 'length', 'width', 'height', 'weight'];

        // Задаване на данните, като приоритет имат POST стойностите
        foreach ($product_data as $field => $value) {
            $current_value = $this->requestPost($field, $value);

            // Форматиране на float стойностите
            if (in_array($field, $float_fields)) {
                // Заменяме запетая с точка за десетичен разделител и форматираме
                $current_value = number_format((float)str_replace(',', '.', (string)$current_value), 2, '.', '');
            }

            $this->setData($field, $current_value);
        }

        // Product Description (многоезични полета)
        $product_description_post = $this->requestPost('product_description');
        if ($product_description_post !== null) {
            $this->setData('product_description', $product_description_post);
        } elseif ($product_id) {
            $product_descriptions = $this->productModel->getProductDescriptions($product_id);
            $this->setData('product_description', $product_descriptions);
        } else {
            $product_description = [];
            foreach ($languages as $language) {
                $product_description[$language['language_id']] = [
                    'name'             => '',
                    'description'      => '',
                    'tag'              => '',
                    'meta_title'       => '',
                    'meta_description' => '',
                    'meta_keyword'     => ''
                ];
            }
            $this->setData('product_description', $product_description);
        }

        // Placeholder за изображение
        $this->setData('placeholder', $this->imageModel->resize('no_image.png', 192, 192));

        // Основно изображение на продукта
        $image_post = $this->requestPost('image');
        if ($image_post !== null && is_file(ThemeData()->getImageServerPath() . $image_post)) {
            $this->setData('thumb', $this->imageModel->resize($image_post, 192, 192));
        } elseif (!empty($product_info) && !empty($product_info['image'])) {

            $this->load->model('tool/Imageservice');
            $image_details = $this->model_tool_Imageservice->getImageDetailsByPath($product_info['image'], 192, 192);

            $this->setData('thumb', $image_details['resized_image_url']);
        } else {
            $this->setData('thumb', $this->imageModel->resize('no_image.png', 192, 192));
        }

        // Категории
        $categories_data = $this->categoryModel->getCategories(['sort' => 'name', 'order' => 'ASC']);
        $categories_flat = $this->getAllCategoriesFlat($categories_data);
        $this->setData('categories', $categories_flat);

        $product_categories = $this->requestPost('product_category');
        if ($product_categories !== null) {
        } elseif ($product_id) {
            $product_categories = $this->productModel->getProductCategories($product_id);
        } else {
            $product_categories = [];
        }
        $this->setData('product_category', $product_categories);

        // Основна категория (ако се използва)
        $main_category_id = $this->requestPost('main_category_id');
        if ($main_category_id !== null) {
            $this->setData('main_category_id', $main_category_id);
        } elseif (isset($product_info['main_category_id'])) {
            $this->setData('main_category_id', $product_info['main_category_id']);
        } else {
             $this->setData('main_category_id', 0);
        }

        // Производители
        $manufacturer_id = $this->requestPost('manufacturer_id');
        if ($manufacturer_id !== null) {
            $this->setData('manufacturer_id', $manufacturer_id);
        } elseif (!empty($product_info)) {
            $this->setData('manufacturer_id', $product_info['manufacturer_id']);
        } else {
            $this->setData('manufacturer_id', 0);
        }
        $this->setData('manufacturers', $this->manufacturerModel->getManufacturers(['sort' => 'name']));

        // Подготвяне на данните за бранд бадж-а
        $manufacturer_name = '';
        if($this->data['manufacturer_id']) {
            $manufacturer_name = $this->getManufacturerName($this->data['manufacturer_id']);
        }
        $this->setData('manufacturer_name', $manufacturer_name);

        // Промоционални цени
        $product_specials_post = $this->requestPost('product_special');
        if ($product_specials_post !== null) {
            $this->setData('product_special', $product_specials_post);
        } elseif ($product_id) {
            $this->setData('product_special', $this->productModel->getProductSpecials($product_id));
        } else {
            $this->setData('product_special', []);
        }

        // Изображения на продукта
        $product_images_data = $this->requestPost('product_image');
        if ($product_images_data !== null) {
            // При POST заявка използваме подадените данни
        } elseif ($product_id) {
            $product_images_data = $this->productModel->getProductImages($product_id);
        } else {
            $product_images_data = [];
        }

        $product_images = [];
        $main_image_path = !empty($product_info) ? $product_info['image'] : '';

        foreach ($product_images_data as $product_image_item) {
            if (isset($product_image_item['image'])) {
                $image = $product_image_item['image'];

                // Пропускаме главното изображение от допълнителните изображения
                if ($image === $main_image_path) {
                    continue;
                }

                $thumb = $this->model_tool_Imageservice->getImageDetailsByPath($product_image_item['image'], 192, 192);
            } else {
                $image = '';
                $thumb = ['resized_image_url' => $this->imageModel->resize('no_image.png', 192, 192)];
            }

            $product_images[] = [
                'image'      => $image,
                'thumb'      => $thumb,
                'sort_order' => isset($product_image_item['sort_order']) ? $product_image_item['sort_order'] : count($product_images),
                'is_main'    => false
            ];
        }

        // Сортиране на изображенията по sort_order
        usort($product_images, function($a, $b) {
            return (int)$a['sort_order'] - (int)$b['sort_order'];
        });

        $this->setData('product_images', $product_images);

        // Подаване на пътя до главното изображение за скритото поле
        $this->setData('image', $main_image_path);

        $_attributes = $this->attributeModel->getAttributes();

        $attributes = [];
        foreach ($_attributes as $attribute) {
            $attribute_id = $attribute['attribute_id'];
            $attribute_name = $attribute['name'];
            $attribute_language_id = $attribute['language_id'];
            $attributes[$attribute_id] = [
                $attribute_language_id => [
                    'name' => $attribute_name,
                    'attribute_id' => $attribute_id,
                ]
            ];
        }

        $this->setData('attributes', $attributes);

        // Атрибути на продукта
        $product_attributes_post = $this->requestPost('product_attribute');
        if ($product_attributes_post !== null) {
            $this->setData('product_attribute', $product_attributes_post);
        } elseif ($product_id) {
            $this->setData('product_attribute', $this->productModel->getProductAttributes($product_id));
        } else {
            $this->setData('product_attribute', []);
        }

        // Опции на продукта
        $product_options_post = $this->requestPost('product_option');
        if ($product_options_post !== null) {
            $this->setData('product_option', $product_options_post);
        } elseif ($product_id) {
            $this->setData('product_option', $this->productModel->getProductOptions($product_id));
        } else {
            $this->setData('product_option', []);
        }

        // SEO URL данни
        $product_seo_url_post = $this->requestPost('product_seo_url');
        if ($product_seo_url_post !== null) {
            $this->setData('product_seo_url', $product_seo_url_post);
        } elseif ($product_id) {
            $_product_seo_urls = $this->productModel->getProductSeoUrls($product_id);
            $product_seo_urls = [];
            foreach ($_product_seo_urls as $product_seo_url) {
                foreach ($product_seo_url    as $language_id => $keyword) {
                    $product_seo_urls[$language_id] = $keyword;
                }
            }
            $this->setData('product_seo_url', $product_seo_urls);
        } else {
            $this->setData('product_seo_url', []);
        }

        // Свързани продукти
        $product_related_post = $this->requestPost('product_related');
        if ($product_related_post !== null) {
            $product_related_ids = $product_related_post;
        } elseif ($product_id) {
            $product_related_ids = $this->productModel->getProductRelated($product_id);
        } else {
            $product_related_ids = [];
        }

        // Подготвяне на пълните данни за свързани продукти
        $product_related = [];
        if (!empty($product_related_ids)) {
            $this->loadModelAs('tool/Imageservice', 'imageService');
            $this->loadModelAs('tool/image', 'imageModel');

            foreach ($product_related_ids as $related_id) {
                $related_product = $this->productModel->getProduct($related_id);
                if ($related_product) {
                    // Получаване на името на продукта от product_description таблицата
                    $product_description = $this->productModel->getProductDescriptions($related_product['product_id']);
                    $product_name = '';
                    if (isset($product_description[$this->config->get('config_language_id')])) {
                        $product_name = $product_description[$this->config->get('config_language_id')]['name'];
                    } elseif (!empty($product_description)) {
                        // Ако няма описание за текущия език, вземи първото налично
                        $first_description = reset($product_description);
                        $product_name = $first_description['name'];
                    }

                    // Подготвяне на изображението
                    $thumb = '';
                    if (!empty($related_product['image'])) {
                        $image_details = $this->imageService->getImageDetailsByPath($related_product['image'], 48, 48);
                        $thumb = $image_details['resized_image_url'];
                    } else {
                        $thumb = $this->imageModel->resize('no_image.png', 48, 48);
                    }

                    $product_related[] = [
                        'product_id' => $related_product['product_id'],
                        'name' => $product_name,
                        'model' => $related_product['model'],
                        'thumb' => $thumb
                    ];
                }
            }
        }
        $this->setData('product_related', $product_related);

        // Зареждане на моделите с по-кратки алиаси
        $this->loadModelsAs([
            'localisation/stock_status' => 'stockStatusModel',
            'localisation/tax_class' => 'taxClassModel',
            'localisation/length_class' => 'lengthClassModel',
            'localisation/weight_class' => 'weightClassModel'
        ]);


        // Зареждане на всички option values за select менютата
        $option_values = $this->getOptionValues($this->data['product_option']);

        // Зареждане на всички опции за JavaScript
        $all_options = $this->getAllOptions();

        // Задаване на всички данни наведнъж
        $this->setData([
            'stock_statuses' => $this->stockStatusModel->getStockStatuses(),
            'tax_classes' => $this->taxClassModel->getTaxClasses(),
            'length_classes' => $this->lengthClassModel->getLengthClasses(),
            'weight_classes' => $this->weightClassModel->getWeightClasses(),
            'option_values' => $option_values,
            'all_options' => $all_options,
        ]);

    }

    private function setBackUrl() {
         $this->setData([
            'back_url' => $this->getAdminLink('catalog/product', '', true, ['product_id'])
        ]);
    }

    /**
     * Рекурсивно извличане на всички категории за плосък списък, подходящ за select
     */
    private function getAllCategoriesFlat($categories, $parent_id = 0, $parent_name = '') {
        $output = [];
        $category_data = [];
        foreach ($categories as $category) {
            $category_data[$category['parent_id']][] = $category;
        }
        return $this->buildCategoryFlatList($category_data, 0, '');
    }

    private function buildCategoryFlatList($categories_data, $parent_id = 0, $path = '') {
        $output = [];
        if (isset($categories_data[$parent_id])) {
            foreach ($categories_data[$parent_id] as $category) {
                $current_path = $path . $category['name'];
                $output[$category['category_id']] = [
                    'category_id' => $category['category_id'],
                    'name'        => $current_path
                ];
                $children = $this->buildCategoryFlatList($categories_data, $category['category_id'], $current_path . ' &gt; ');
                foreach ($children as $category_id => $child) {
                    $output[$category_id] = $child;
                }
            }
        }
        return $output;
    }

    private function getManufacturerName($manufacturer_id) {
        foreach ($this->data['manufacturers'] as $manufacturer) {
            if ($manufacturer['manufacturer_id'] == $manufacturer_id) {
                return $manufacturer['name'];
            }
        }
        return '';
    }

    /**
     * Зареждане на всички option values за select менютата
     */
    private function getOptionValues($product_options) {
        $option_values = [];

        // Зареждаме всички опции
        $options = $this->optionModel->getOptions();

        // Зареждаме всички option values за всички опции (не само за съществуващите в продукта)
        foreach ($options as $option) {
            $values = $this->optionModel->getOptionValues($option['option_id']);

            foreach ($values as $value) {
                $option_values[] = [
                    'option_value_id' => $value['option_value_id'],
                    'option_id' => $option['option_id'],
                    'option_name' => $option['name'],
                    'name' => $value['name'],
                    'sort_order' => $value['sort_order']
                ];
            }
        }

        // Сортираме по име на опцията и след това по име на стойността
        usort($option_values, function($a, $b) {
            $option_compare = strcmp($a['option_name'], $b['option_name']);
            if ($option_compare === 0) {
                return strcmp($a['name'], $b['name']);
            }
            return $option_compare;
        });

        return $option_values;
    }

    /**
     * Зареждане на всички опции за JavaScript
     */
    private function getAllOptions() {
        $options = $this->optionModel->getOptions();
        $all_options = [];

        foreach ($options as $option) {
            $all_options[] = [
                'option_id' => $option['option_id'],
                'name' => $option['name'],
                'type' => $option['type']
            ];
        }

        return $all_options;
    }
}