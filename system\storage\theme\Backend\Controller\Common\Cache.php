<?php

namespace Theme25\Backend\Controller\Common;

class Cache extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/cache');
    }

    /**
     * Изтриване на кеша
     */
    public function clear() {
        $json = [];
        
        ob_start();
        
        try {
            // Проверка за права
            // if (!$this->user->hasPermission('modify', 'common/developer')) {
            //     $json['error'] = 'Нямате права за изтриване на кеша';
            // } else {
                $cleared = [];
                
                // Изтриване на файловия кеш
                $this->clearFileCache();
                $cleared[] = 'файлов кеш';

                // Изтриване на Twig кеша
                $this->clearTwigCache();
                $cleared[] = 'Twig кеш';

                // Изтриване на кеша за търсене
                $this->clearSearchCache();
                $cleared[] = 'кеш за търсене';

                // Изтриване на модификационния кеш
                // $this->clearModificationCache();
                // $cleared[] = 'модификационен кеш';
                
                $json['success'] = 'Успешно изтрит: ' . implode(', ', $cleared);
            // }
        } catch (Exception $e) {
            $json['error'] = 'Грешка при изтриване на кеша: ' . $e->getMessage();
        }
        
        $output = ob_get_clean();
        if ($output) {
            $json['error'] = $output;
        }
        
        $this->setJSONResponseOutput($json);
    }
    
    /**
     * Изтриване на файловия кеш
     */
    private function clearFileCache() {
        $files = glob(DIR_CACHE . 'cache.*');
        
        if ($files) {
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * Изтриване на Twig кеша
     */
    private function clearTwigCache() {
        $directories = glob(DIR_CACHE . '*', GLOB_ONLYDIR);

        if ($directories) {
            foreach ($directories as $directory) {
                $files = glob($directory . '/*');

                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }

                if (is_dir($directory)) {
                    rmdir($directory);
                }
            }
        }
    }

    /**
     * Изтриване на кеша за търсене
     */
    private function clearSearchCache() {
        $searchCacheDir = DIR_CACHE . 'search/';

        if (is_dir($searchCacheDir)) {
            $files = glob($searchCacheDir . '*.cache');

            if ($files) {
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
            }
        }
    }
    
    /**
     * Изтриване на модификационния кеш
     */
    private function clearModificationCache() {
        $files = glob(DIR_MODIFICATION . '*.ocmod.xml');
        
        if ($files) {
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
}
