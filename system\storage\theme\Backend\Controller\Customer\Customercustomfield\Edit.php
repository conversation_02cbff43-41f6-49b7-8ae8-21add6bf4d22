<?php

namespace Theme25\Backend\Controller\Customer\Customercustomfield;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява подготовката на данните за формата за персонализирано поле
     */
    public function execute() {
        $custom_field_id = (int)$this->requestGet('custom_field_id', 0);
        
        if ($custom_field_id) {
            $this->setTitle('Редактиране на персонализирано поле');
        } else {
            $this->setTitle('Добавяне на персонализирано поле');
        }
        
        $this->initAdminData();
        $this->prepareCustomFieldForm($custom_field_id);
        $this->renderTemplateWithDataAndOutput('customer/custom_field_form');
    }

    /**
     * Подготвя формата за персонализирано поле
     */
    public function prepareCustomFieldForm($custom_field_id = 0) {
        $this->prepareCustomFieldData($custom_field_id)
             ->prepareLanguages()
             ->prepareCustomerGroups()
             ->prepareFormUrls($custom_field_id)
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за персонализираното поле
     */
    private function prepareCustomFieldData($custom_field_id) {
        $this->load->model('customer/custom_field');

        if ($custom_field_id && ($custom_field_info = $this->model_customer_custom_field->getCustomField($custom_field_id))) {
            $this->setData([
                'custom_field_id' => $custom_field_info['custom_field_id'],
                'type' => $custom_field_info['type'],
                'value' => $custom_field_info['value'],
                'validation' => $custom_field_info['validation'],
                'location' => $custom_field_info['location'],
                'status' => $custom_field_info['status'],
                'sort_order' => $custom_field_info['sort_order']
            ]);

            // Зареждане на описанията на различните езици
            $custom_field_description = $this->model_customer_custom_field->getCustomFieldDescriptions($custom_field_id);
            $this->setData('custom_field_description', $custom_field_description);

            // Зареждане на стойностите за select/radio/checkbox
            $custom_field_value = $this->model_customer_custom_field->getCustomFieldValues($custom_field_id);
            $this->setData('custom_field_value', $custom_field_value);

            // Зареждане на клиентските групи
            $custom_field_customer_group = $this->model_customer_custom_field->getCustomFieldCustomerGroups($custom_field_id);
            $this->setData('custom_field_customer_group', $custom_field_customer_group);
        } else {
            $this->setData([
                'custom_field_id' => 0,
                'type' => 'text',
                'value' => '',
                'validation' => '',
                'location' => 'account',
                'status' => 1,
                'sort_order' => 1,
                'custom_field_description' => [],
                'custom_field_value' => [],
                'custom_field_customer_group' => []
            ]);
        }

        return $this;
    }

    /**
     * Подготвя езиците
     */
    private function prepareLanguages() {
        $this->load->model('localisation/language');
        $languages = $this->model_localisation_language->getLanguages();

        $this->setData('languages', $languages);

        return $this;
    }

    /**
     * Подготвя клиентските групи
     */
    private function prepareCustomerGroups() {
        $this->load->model('customer/customer_group');
        $customer_groups = $this->model_customer_customer_group->getCustomerGroups();

        $this->setData('customer_groups', $customer_groups);

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls($custom_field_id) {
        $this->setData([
            'action' => $this->getAdminLink('customer/custom_field/save'),
            'cancel' => $this->getAdminLink('customer/custom_field')
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        $this->setData([
            'user_token' => $this->session->data['user_token'],
            'back_url' => $this->getAdminLink('customer/custom_field')
        ]);

        return $this;
    }
}
