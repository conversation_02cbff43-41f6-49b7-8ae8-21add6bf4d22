<?php

namespace Theme25\Backend\Controller\Catalog\Option;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Изтриване на опция(и)
     */
    public function execute() {
        $json = [];

        ob_start();

        // Проверка за валидност на заявката
        if ($this->request->server['REQUEST_METHOD'] != 'POST') {
            $json['error'] = 'Невалиден метод на заявка';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Проверка за права за модификация
        if (!$this->user->hasPermission('modify', 'catalog/option')) {
            $json['error'] = 'Нямате права за изтриване на опции';
            $this->setJSONResponseOutput($json);
            return;
        }

        $post = $this->requestPost();
        $option_ids = [];

        // Получаване на ID-тата за изтриване
        if (isset($post['selected']) && is_array($post['selected'])) {
            // Множествено изтриване
            $option_ids = array_map('intval', $post['selected']);
        } elseif (isset($post['option_id'])) {
            // Единично изтриване
            $option_ids = [(int)$post['option_id']];
        } else {
            $json['error'] = 'Не са избрани опции за изтриване';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Филтриране на валидните ID-та
        $option_ids = array_filter($option_ids, function($id) {
            return $id > 0;
        });

        if (empty($option_ids)) {
            $json['error'] = 'Не са избрани валидни опции за изтриване';
            $this->setJSONResponseOutput($json);
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/option' => 'optionModel',
            'catalog/product' => 'productModel'
        ]);

        try {
            $deleted_count = 0;
            $skipped_options = [];

            foreach ($option_ids as $option_id) {
                // Проверка дали опцията съществува
                $option_info = $this->optionModel->getOption($option_id);
                if (!$option_info) {
                    continue;
                }

                // Проверка дали опцията се използва от продукти
                if ($this->isOptionUsedByProducts($option_id)) {
                    $skipped_options[] = $option_info['name'];
                    continue;
                }

                // Изтриване на опцията
                $this->optionModel->deleteOption($option_id);
                $deleted_count++;
            }

            // Подготвяне на отговора
            if ($deleted_count > 0) {
                if ($deleted_count == 1) {
                    $json['success'] = 'Опцията беше успешно изтрита';
                } else {
                    $json['success'] = "Бяха изтрити {$deleted_count} опции";
                }
            }

            if (!empty($skipped_options)) {
                $skipped_list = implode(', ', $skipped_options);
                if (isset($json['success'])) {
                    $json['warning'] = "Следните опции не бяха изтрити, защото се използват от продукти: {$skipped_list}";
                } else {
                    $json['error'] = "Опциите не могат да бъдат изтрити, защото се използват от продукти: {$skipped_list}";
                }
            }

            if ($deleted_count == 0 && empty($skipped_options)) {
                $json['error'] = 'Няма опции за изтриване';
            }

        } catch (\Exception $e) {
            $json['error'] = 'Грешка при изтриване на опциите: ' . $e->getMessage();
        }

        $output = ob_get_clean();
        if($output) {
            $json['error'] = $output;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали опцията се използва от продукти
     */
    private function isOptionUsedByProducts($option_id) {
        try {
            // Проверка в таблицата product_option
            $query = $this->db->query("
                SELECT COUNT(*) as total 
                FROM `" . DB_PREFIX . "product_option` 
                WHERE option_id = '" . (int)$option_id . "'
            ");

            return $query->row['total'] > 0;
        } catch (\Exception $e) {
            // В случай на грешка, считаме че опцията се използва за безопасност
            return true;
        }
    }

    /**
     * Получава списък с продукти, които използват опцията
     */
    private function getProductsUsingOption($option_id) {
        $products = [];

        try {
            $query = $this->db->query("
                SELECT DISTINCT p.product_id, pd.name 
                FROM `" . DB_PREFIX . "product_option` po
                LEFT JOIN `" . DB_PREFIX . "product` p ON (po.product_id = p.product_id)
                LEFT JOIN `" . DB_PREFIX . "product_description` pd ON (p.product_id = pd.product_id)
                WHERE po.option_id = '" . (int)$option_id . "'
                AND pd.language_id = '" . (int)$this->getLanguageId() . "'
                LIMIT 10
            ");

            foreach ($query->rows as $row) {
                $products[] = $row['name'];
            }
        } catch (\Exception $e) {
            // В случай на грешка, връщаме празен масив
        }

        return $products;
    }

    /**
     * Зареждане на модели с псевдоними
     */
    private function loadModelsAs($models) {
        foreach ($models as $model => $alias) {
            $this->load->model($model);
            $this->{$alias} = $this->{'model_' . str_replace('/', '_', $model)};
        }
    }
}
