<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Copy extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Копиране на категория
     */
    public function execute() {
        $category_id = $this->requestGet('category_id', 0);

        if (!$category_id) {
            $this->session->data['error'] = 'Невалиден ID на категория';
            $this->response->redirect($this->getAdminLink('catalog/category'));
            return;
        }

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'catalog/category' => 'categoryModel'
        ]);

        // Получаване на данните за оригиналната категория
        $original_category = $this->categoryModel->getCategory($category_id);

        if (!$original_category) {
            $this->session->data['error'] = 'Категорията не съществува';
            $this->response->redirect($this->getAdminLink('catalog/category'));
            return;
        }

        try {
            // Копиране на категорията
            $new_category_id = $this->copyCategory($category_id);

            if ($new_category_id) {
                $this->session->data['success'] = 'Категорията "' . $original_category['name'] . '" беше успешно копирана';
                $this->response->redirect($this->getAdminLink('catalog/category/edit', 'category_id=' . $new_category_id));
            } else {
                $this->session->data['error'] = 'Грешка при копиране на категорията';
                $this->response->redirect($this->getAdminLink('catalog/category'));
            }

        } catch (Exception $e) {
            $this->session->data['error'] = 'Грешка при копиране на категорията: ' . $e->getMessage();
            $this->response->redirect($this->getAdminLink('catalog/category'));
        }
    }

    /**
     * Копира категория с всичките ѝ данни
     *
     * @param int $category_id ID на категорията за копиране
     * @return int|false ID на новата категория или false при грешка
     */
    private function copyCategory($category_id) {
        // Получаване на основните данни за категорията
        $category_info = $this->categoryModel->getCategory($category_id);
        if (!$category_info) {
            return false;
        }

        // Получаване на описанията за всички езици
        $category_descriptions = $this->getCategoryDescriptions($category_id);

        // Получаване на SEO URL-та
        $category_seo_urls = $this->categoryModel->getCategorySeoUrls($category_id);

        // Получаване на магазините
        $category_stores = $this->categoryModel->getCategoryStores($category_id);

        // Подготовка на данните за новата категория
        $data = [
            'parent_id' => $category_info['parent_id'],
            'top' => $category_info['top'],
            'column' => $category_info['column'],
            'sort_order' => $category_info['sort_order'],
            'status' => 0, // Новата категория е неактивна по подразбиране
            'image' => $category_info['image'],
            'category_description' => $this->prepareCopiedDescriptions($category_descriptions),
            'category_seo_url' => $this->prepareCopiedSeoUrls($category_seo_urls),
            'category_store' => $category_stores
        ];

        // Създаване на новата категория
        $new_category_id = $this->categoryModel->addCategory($data);

        return $new_category_id;
    }

    /**
     * Получава описанията на категория за всички езици
     *
     * @param int $category_id ID на категорията
     * @return array Описания за всички езици
     */
    private function getCategoryDescriptions($category_id) {
        $query = $this->db->query("
            SELECT * 
            FROM `" . DB_PREFIX . "category_description` 
            WHERE category_id = '" . (int)$category_id . "'
        ");

        $descriptions = [];
        foreach ($query->rows as $row) {
            $descriptions[$row['language_id']] = [
                'name' => $row['name'],
                'description' => $row['description'],
                'meta_title' => $row['meta_title'],
                'meta_description' => $row['meta_description'],
                'meta_keyword' => $row['meta_keyword']
            ];
        }

        return $descriptions;
    }

    /**
     * Подготвя описанията за копираната категория
     *
     * @param array $descriptions Оригинални описания
     * @return array Модифицирани описания
     */
    private function prepareCopiedDescriptions($descriptions) {
        $copied_descriptions = [];

        foreach ($descriptions as $language_id => $description) {
            $copied_descriptions[$language_id] = [
                'name' => $description['name'] . ' (копие)',
                'description' => $description['description'],
                'meta_title' => $description['meta_title'],
                'meta_description' => $description['meta_description'],
                'meta_keyword' => $description['meta_keyword']
            ];
        }

        return $copied_descriptions;
    }

    /**
     * Подготвя SEO URL-та за копираната категория
     *
     * @param array $seo_urls Оригинални SEO URL-та
     * @return array Модифицирани SEO URL-та
     */
    private function prepareCopiedSeoUrls($seo_urls) {
        $copied_seo_urls = [];

        foreach ($seo_urls as $store_id => $store_urls) {
            foreach ($store_urls as $language_id => $url) {
                if (!empty($url)) {
                    // Добавяне на суфикс към SEO URL-то
                    $copied_seo_urls[$store_id][$language_id] = $url . '-copy-' . time();
                } else {
                    $copied_seo_urls[$store_id][$language_id] = '';
                }
            }
        }

        return $copied_seo_urls;
    }

    /**
     * Копира категория заедно с всички подкатегории (рекурсивно)
     */
    public function copyWithChildren() {
        $category_id = $this->requestGet('category_id', 0);

        if (!$category_id) {
            $this->session->data['error'] = 'Невалиден ID на категория';
            $this->response->redirect($this->getAdminLink('catalog/category'));
            return;
        }

        try {
            $new_category_id = $this->copyCategory($category_id);
            
            if ($new_category_id) {
                // Копиране на всички подкатегории
                $this->copyChildCategories($category_id, $new_category_id);
                
                $this->session->data['success'] = 'Категорията и всичките ѝ подкатегории бяха успешно копирани';
                $this->response->redirect($this->getAdminLink('catalog/category/edit', 'category_id=' . $new_category_id));
            } else {
                $this->session->data['error'] = 'Грешка при копиране на категорията';
                $this->response->redirect($this->getAdminLink('catalog/category'));
            }

        } catch (Exception $e) {
            $this->session->data['error'] = 'Грешка при копиране на категорията: ' . $e->getMessage();
            $this->response->redirect($this->getAdminLink('catalog/category'));
        }
    }

    /**
     * Копира всички подкатегории на дадена категория
     *
     * @param int $parent_category_id ID на родителската категория
     * @param int $new_parent_id ID на новата родителска категория
     */
    private function copyChildCategories($parent_category_id, $new_parent_id) {
        // Получаване на всички подкатегории
        $children = $this->categoryModel->getCategories(['filter_parent_id' => $parent_category_id]);

        foreach ($children as $child) {
            // Копиране на подкатегорията
            $child_copy_id = $this->copyCategory($child['category_id']);
            
            if ($child_copy_id) {
                // Задаване на новия родител
                $this->db->query("
                    UPDATE `" . DB_PREFIX . "category` 
                    SET parent_id = '" . (int)$new_parent_id . "' 
                    WHERE category_id = '" . (int)$child_copy_id . "'
                ");

                // Рекурсивно копиране на подкатегориите на тази подкатегория
                $this->copyChildCategories($child['category_id'], $child_copy_id);
            }
        }
    }
}
