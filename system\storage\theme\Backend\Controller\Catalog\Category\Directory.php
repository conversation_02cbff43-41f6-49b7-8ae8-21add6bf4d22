<?php

namespace Theme25\Backend\Controller\Catalog\Category;

class Directory extends \Theme25\ControllerSubMethods {
    
    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Получава категория по ID
     *
     * @param int $category_id ID на категорията
     * @return array|false Данни за категорията или false
     */
    public function getCategoryById($category_id) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        return $this->categoryModel->getCategory($category_id);
    }

    /**
     * Получава списък с категории
     *
     * @param array $filter_data Филтри за заявката
     * @return array Списък с категории
     */
    public function getCategories($filter_data = []) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        return $this->categoryModel->getCategories($filter_data);
    }

    /**
     * Получава общия брой категории
     *
     * @param array $filter_data Филтри за заявката
     * @return int Общ брой категории
     */
    public function getTotalCategories($filter_data = []) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        return $this->categoryModel->getTotalCategories($filter_data);
    }

    /**
     * Добавя нова категория
     *
     * @param array $data Данни за категорията
     * @return int ID на новата категория
     */
    public function addCategory($data) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        // Валидация на данните
        $validated_data = $this->validateCategoryData($data);
        
        if ($validated_data === false) {
            return false;
        }

        return $this->categoryModel->addCategory($validated_data);
    }

    /**
     * Редактира съществуваща категория
     *
     * @param int $category_id ID на категорията
     * @param array $data Нови данни за категорията
     * @return bool Успех на операцията
     */
    public function editCategory($category_id, $data) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        // Валидация на данните
        $validated_data = $this->validateCategoryData($data, $category_id);
        
        if ($validated_data === false) {
            return false;
        }

        $this->categoryModel->editCategory($category_id, $validated_data);
        return true;
    }

    /**
     * Изтрива категория
     *
     * @param int $category_id ID на категорията
     * @return bool Успех на операцията
     */
    public function deleteCategory($category_id) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        // Проверка дали категорията има подкатегории
        $subcategories = $this->getCategories(['filter_parent_id' => $category_id]);
        if (!empty($subcategories)) {
            $this->setError('Категорията има подкатегории и не може да бъде изтрита');
            return false;
        }

        // Проверка дали категорията има продукти
        if (!isset($this->productModel)) {
            $this->loadModelAs('catalog/product', 'productModel');
        }

        $products_count = $this->productModel->getTotalProducts(['filter_category_id' => $category_id]);
        if ($products_count > 0) {
            $this->setError('Категорията съдържа продукти и не може да бъде изтрита');
            return false;
        }

        $this->categoryModel->deleteCategory($category_id);
        return true;
    }

    /**
     * Получава описанията на категория за всички езици
     *
     * @param int $category_id ID на категорията
     * @return array Описания за всички езици
     */
    public function getCategoryDescriptions($category_id) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        // Ако методът не съществува в модела, създаваме го тук
        $query = $this->db->query("
            SELECT * 
            FROM `" . DB_PREFIX . "category_description` 
            WHERE category_id = '" . (int)$category_id . "'
        ");

        $descriptions = [];
        foreach ($query->rows as $row) {
            $descriptions[$row['language_id']] = [
                'name' => $row['name'],
                'description' => $row['description'],
                'meta_title' => $row['meta_title'],
                'meta_description' => $row['meta_description'],
                'meta_keyword' => $row['meta_keyword']
            ];
        }

        return $descriptions;
    }

    /**
     * Получава SEO URL-та на категория
     *
     * @param int $category_id ID на категорията
     * @return array SEO URL-та
     */
    public function getCategorySeoUrls($category_id) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        return $this->categoryModel->getCategorySeoUrls($category_id);
    }

    /**
     * Получава магазините на категория
     *
     * @param int $category_id ID на категорията
     * @return array Магазини
     */
    public function getCategoryStores($category_id) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        return $this->categoryModel->getCategoryStores($category_id);
    }

    /**
     * Получава пътя на категория в йерархията
     *
     * @param int $category_id ID на категорията
     * @return array Път в йерархията
     */
    public function getCategoryPath($category_id) {
        if (!isset($this->categoryModel)) {
            $this->loadModelAs('catalog/category', 'categoryModel');
        }

        return $this->categoryModel->getCategoryPath($category_id);
    }

    /**
     * Валидира данните за категория
     *
     * @param array $data Данни за валидация
     * @param int $category_id ID на категорията (при редактиране)
     * @return array|false Валидирани данни или false при грешка
     */
    private function validateCategoryData($data, $category_id = 0) {
        $errors = [];

        // Проверка за задължителни полета
        if (empty($data['category_description']) || !is_array($data['category_description'])) {
            $errors[] = 'Липсва описание на категорията';
        } else {
            // Проверка дали има име поне за един език
            $has_name = false;
            foreach ($data['category_description'] as $description) {
                if (!empty($description['name'])) {
                    $has_name = true;
                    break;
                }
            }
            
            if (!$has_name) {
                $errors[] = 'Моля въведете име на категорията поне за един език';
            }
        }

        // Проверка за валидност на родителската категория
        if (!empty($data['parent_id'])) {
            $parent_category = $this->getCategoryById($data['parent_id']);
            if (!$parent_category) {
                $errors[] = 'Избраната родителска категория не съществува';
            } elseif ($category_id && $data['parent_id'] == $category_id) {
                $errors[] = 'Категорията не може да бъде родител на себе си';
            }
        }

        // Проверка за циклична зависимост при редактиране
        if ($category_id && !empty($data['parent_id'])) {
            if ($this->wouldCreateCycle($category_id, $data['parent_id'])) {
                $errors[] = 'Избраната родителска категория би създала циклична зависимост';
            }
        }

        if (!empty($errors)) {
            $this->setError(implode('; ', $errors));
            return false;
        }

        // Задаване на стойности по подразбиране
        $validated_data = [
            'parent_id' => isset($data['parent_id']) ? (int)$data['parent_id'] : 0,
            'top' => isset($data['top']) ? (int)$data['top'] : 0,
            'column' => isset($data['column']) ? (int)$data['column'] : 1,
            'sort_order' => isset($data['sort_order']) ? (int)$data['sort_order'] : 0,
            'status' => isset($data['status']) ? (int)$data['status'] : 0,
            'image' => isset($data['image']) ? $data['image'] : '',
            'category_description' => $data['category_description'],
            'category_store' => isset($data['category_store']) ? $data['category_store'] : [0],
            'category_seo_url' => isset($data['category_seo_url']) ? $data['category_seo_url'] : []
        ];

        return $validated_data;
    }

    /**
     * Проверява дали промяната на родителската категория би създала циклична зависимост
     *
     * @param int $category_id ID на категорията
     * @param int $parent_id ID на новата родителска категория
     * @return bool True ако би създала цикъл
     */
    private function wouldCreateCycle($category_id, $parent_id) {
        if ($parent_id == 0) {
            return false;
        }

        // Получаване на всички деца на текущата категория
        $children = $this->getAllCategoryChildren($category_id);
        
        return in_array($parent_id, $children);
    }

    /**
     * Получава всички деца на дадена категория (рекурсивно)
     *
     * @param int $category_id ID на категорията
     * @return array Масив с ID-та на всички деца
     */
    private function getAllCategoryChildren($category_id) {
        $children = [];
        
        $direct_children = $this->getCategories(['filter_parent_id' => $category_id]);
        
        foreach ($direct_children as $child) {
            $children[] = $child['category_id'];
            $grandchildren = $this->getAllCategoryChildren($child['category_id']);
            $children = array_merge($children, $grandchildren);
        }
        
        return $children;
    }

    /**
     * Задава грешка
     *
     * @param string $error Съобщение за грешка
     */
    private function setError($error) {
        if (!isset($this->errors)) {
            $this->errors = [];
        }
        $this->errors[] = $error;
    }

    /**
     * Получава грешките
     *
     * @return array Масив с грешки
     */
    public function getErrors() {
        return isset($this->errors) ? $this->errors : [];
    }
}
