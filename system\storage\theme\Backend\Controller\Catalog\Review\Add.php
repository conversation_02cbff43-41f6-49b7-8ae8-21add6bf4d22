<?php

namespace Theme25\Backend\Controller\Catalog\Review;

class Add extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'review-form.js'
        ], 'footer');
    }

    public function execute() {
        $this->setTitle('Добавяне на коментар');

        // Инициализиране на данните
        $this->initAdminData();

        $this->prepareData();

        // Рендиране на шаблона с данните от $this->data
        $this->renderTemplateWithDataAndOutput('catalog/review_form');
    }

    /**
     * Подготвя данните за формата за добавяне
     */
    private function prepareData() {
        $this->loadModelsAs([
            'catalog/review' => 'reviewModel',
            'catalog/product' => 'productModel'
        ]);

        $this->prepareFormData()
             ->prepareUrls();

        return $this;
    }

    /**
     * Подготвя данните за формата
     */
    private function prepareFormData() {
        // Подготвяне на празни данни за нов коментар
        $this->setData([
            'review_id' => '',
            'product_id' => $this->requestGet('product_id', ''),
            'product_name' => '',
            'author' => '',
            'text' => '',
            'rating' => 5,
            'status' => 1,
            'date_added' => date('Y-m-d H:i:s')
        ]);

        // Ако е зададен product_id, зареждаме информацията за продукта
        if (!empty($this->data['product_id'])) {
            $product_info = $this->productModel->getProduct($this->data['product_id']);
            if ($product_info) {
                $this->setData('product_name', $product_info['name']);
            }
        }

        // Подготвяне на опциите за рейтинг
        $rating_options = [];
        for ($i = 1; $i <= 5; $i++) {
            $rating_options[] = [
                'value' => $i,
                'text' => $i . ' звезд' . ($i == 1 ? 'а' : 'и')
            ];
        }

        $this->setData('rating_options', $rating_options);

        // Подготвяне на опциите за статус
        $status_options = [
            ['value' => 1, 'text' => 'Активен'],
            ['value' => 0, 'text' => 'Неактивен']
        ];

        $this->setData('status_options', $status_options);

        return $this;
    }

    /**
     * Подготвя URL адресите
     */
    private function prepareUrls() {
        $this->setData([
            'action_url' => $this->getAdminLink('catalog/review/save'),
            'cancel_url' => $this->getAdminLink('catalog/review'),
            'product_autocomplete_url' => $this->getAdminLink('catalog/product/autocomplete')
        ]);

        return $this;
    }
}
